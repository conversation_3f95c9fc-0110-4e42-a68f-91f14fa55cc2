"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("./User"));
const db_1 = __importDefault(require("../modules/db"));
const NotificationSummarySchema = new mongoose_1.default.Schema({
    unit_id: { type: [String], required: false }, // kept for backward compatibility
    vessel_ids: {
        type: [mongoose_1.default.Schema.Types.Mixed],
        required: true,
        validate: {
            validator: function (arr) {
                return arr.every((item) => item === "all" || mongoose_1.default.Types.ObjectId.isValid(item));
            },
            message: 'vessel_ids must contain valid ObjectIds or "all"',
        },
    },
    preference: { type: [String], required: true, enum: ["daily", "weekly", "monthly"] },
    receivers: [
        {
            type: String,
            default: [],
        },
    ],
    title: { type: [String], required: true },
    created_by: { type: mongoose_1.default.Schema.Types.ObjectId, ref: User_1.default },
    created_at: { type: Date, default: () => new Date().toISOString() },
    is_enabled: { type: Boolean, default: true },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});
const NotificationSummary = db_1.default.qm.model("NotificationSummary", NotificationSummarySchema, "notifications_summary");
exports.default = NotificationSummary;
