const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { apiEndpointsList } = require('../data/ApiEndpoints');
const ApiEndpoint = require('../../models/ApiEndpoint');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('API Endpoints API', () => {

    describe('GET /api/apiEndpoints', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/apiEndpoints');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if the user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app).get('/api/apiEndpoints').set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 200 and fetch the list of API endpoints if the user is authorized or 403 for api-key authorization', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    ApiEndpoint.find.mockResolvedValue(apiEndpointsList);

                    const res = await request(app).get('/api/apiEndpoints').set('Authorization', authToken);

                    if (authMethod === 'user') {
                        expect(res.status).toBe(200);
                        expect(res.body).toBeInstanceOf(Array);
                        ['_id', 'endpoint_id', 'name', 'category', 'is_public'].forEach(prop => {
                            expect(res.body[0]).toHaveProperty(prop);
                        });
                    } else if (authMethod === 'api-key') {
                        expect(res.status).toBe(403);
                    }
                });

                if (authMethod === 'user') {
                    it('should return 500 if an error occurs while fetching the API endpoints', async () => {
                        mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                        ApiEndpoint.find.mockRejectedValueOnce(new Error('Database error'));

                        const res = await request(app).get('/api/apiEndpoints').set('Authorization', authToken);

                        expect(res.status).toBe(500);
                    });
                }
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });


});
