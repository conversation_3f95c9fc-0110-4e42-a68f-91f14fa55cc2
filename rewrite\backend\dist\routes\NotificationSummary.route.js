"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_1 = __importDefault(require("express"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const NotificationSummary_1 = __importDefault(require("../models/NotificationSummary"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const email_1 = require("../modules/email");
const Email_1 = require("../utils/Email");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const functions_1 = require("../utils/functions");
const EmailDomains_1 = __importDefault(require("../models/EmailDomains"));
const permissions_1 = require("../utils/permissions");
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importStar(require("mongoose"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_NOTIFICATION_SUMMARIES), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const page = parseInt(req.query.page) || 1;
    const page_size = parseInt(req.query.page_size) || 10;
    try {
        const totalDocuments = yield NotificationSummary_1.default.countDocuments({ created_by: req.user._id });
        const totalPages = Math.ceil(totalDocuments / page_size);
        const notificationSummaries = yield NotificationSummary_1.default.find({ created_by: req.user._id })
            .skip((page - 1) * page_size)
            .limit(page_size);
        res.json({
            data: notificationSummaries,
            total_pages: totalPages,
            total_documents: totalDocuments,
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
        });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.get("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_NOTIFICATION_SUMMARIES), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationSummary = yield NotificationSummary_1.default.findOne({
            _id: req.params.id,
            created_by: req.user._id,
        });
        if (!notificationSummary) {
            return res.status(404).json({ message: "Notification Summary not found" });
        }
        res.json(notificationSummary);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.patch("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_NOTIFICATION_SUMMARIES), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
    (0, express_validator_1.body)("vessel_ids")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((vessels) => {
        if (vessels.length === 0) {
            throw new Error(`At least one Vessel ID is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids.*")
        .optional()
        .custom((value) => {
        if (value === "all")
            return true;
        return (0, mongoose_1.isValidObjectId)(value);
    })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("preference")
        .optional()
        .isArray()
        .notEmpty()
        .withMessage(() => `preference must be an array`)
        .custom((preferences) => {
        const validPreferences = ["daily", "weekly", "monthly"];
        if (!preferences.every((p) => validPreferences.includes(p))) {
            throw new Error(`Invalid preference value(s) provided`);
        }
        return true;
    }),
    (0, express_validator_1.body)("receivers")
        .optional()
        .isArray()
        .withMessage(() => `receivers must be an array`)
        .custom((receivers) => {
        if (receivers.length > 20) {
            throw new Error("A maximum of 20 receivers are allowed");
        }
        for (let email of receivers) {
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                throw new Error(`Invalid email address: ${email}`);
            }
        }
        return true;
    }),
    (0, express_validator_1.body)("is_enabled")
        .optional()
        .custom((value) => {
        if (value === 1 || value === 0) {
            return true;
        }
        throw new Error("is_enabled must be 0 or 1");
    }),
    (0, express_validator_1.body)("title")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((tit) => {
        if (tit.length === 0) {
            throw new Error(`At least one Title is required`);
        }
        return true;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { receivers, vessel_ids, preference, is_enabled, title, } = req.body;
    const updateData = {};
    if (vessel_ids) {
        updateData.vessel_ids = vessel_ids.map((id) => {
            if (id === "all")
                return "all";
            return new mongoose_1.default.Types.ObjectId(id);
        });
    }
    if (preference)
        updateData.preference = preference;
    if (receivers)
        updateData.receivers = receivers;
    if (typeof is_enabled !== "undefined")
        updateData.is_enabled = is_enabled;
    if (title)
        updateData.title = title;
    /** Ensure user cannot subscribe to vessels not assigned to them */
    if (vessel_ids) {
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vessel_ids.filter((id) => id !== "all") } });
        const hasUnauthorizedVessels = vessels.some((v) => !(0, functions_1.canAccessVessel)(req, v));
        if (hasUnauthorizedVessels) {
            return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
        }
    }
    if (receivers) {
        const getDomains = yield EmailDomains_1.default.find({});
        const domains = getDomains.map((domain) => domain.domain);
        if (!req.user.email) {
            return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
        }
        const userEmailDomain = req.user.email.split("@")[1];
        if (!domains.includes(userEmailDomain)) {
            return res.status(400).json({ message: "User email domain is not allowed." });
        }
        const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.additionalEmailAddressesPrivilege);
        const allReceiversValid = receivers.every((email) => {
            const receiverDomain = email.split("@")[1];
            return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
        });
        if (!allReceiversValid) {
            return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
        }
        const notificationSummary = yield NotificationSummary_1.default.findById(req.params.id);
        if (!notificationSummary) {
            return res.status(404).json({ message: "Notification Alert not found" });
        }
        //check if new addition is made in payload separate that out please for me
        const newReceivers = receivers.filter((receiver) => !notificationSummary.receivers.includes(receiver));
        const { email } = req.user;
        for (let receiver of newReceivers) {
            const token = (0, functions_1.generateUnsubscribeToken)(receiver, notificationSummary._id.toString());
            const payload = {
                addBy: email || "Unknown",
                vessel: Array.isArray(notificationSummary.title) ? notificationSummary.title.join(",") : notificationSummary.title || "Unknown",
                preference: Array.isArray(notificationSummary.preference)
                    ? notificationSummary.preference.join("/")
                    : notificationSummary.preference || "Unknown",
                link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
            };
            const emailBody = (0, Email_1.SUMMARY_SUBSCRIPTION_EMAIL_CONTENT)(payload, new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear());
            (0, email_1.sendEmail)({
                to: receiver,
                subject: "Notification Alert",
                html: emailBody,
            });
        }
    }
    try {
        const notificationSummary = yield NotificationSummary_1.default.findOneAndUpdate({ _id: req.params.id, created_by: req.user._id }, updateData, {
            new: true,
        });
        if (!notificationSummary) {
            return res.status(404).json({ message: "Notification Summary not found" });
        }
        res.json(notificationSummary);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_NOTIFICATION_SUMMARIES), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationSummary = yield NotificationSummary_1.default.findOneAndDelete({
            _id: req.params.id,
            created_by: req.user._id,
        });
        if (!notificationSummary) {
            return res.status(404).json({ message: "Notification Summary not found" });
        }
        res.json(notificationSummary);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_NOTIFICATION_SUMMARIES), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("vessel_ids")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((vessels) => {
        if (vessels.length === 0) {
            throw new Error(`At least one Vessel ID is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids.*")
        .custom((value) => {
        if (value === "all")
            return true;
        return (0, mongoose_1.isValidObjectId)(value);
    })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("preference")
        .notEmpty()
        .isArray()
        .withMessage(() => `preference must be an array`),
    (0, express_validator_1.body)("receivers")
        .isArray()
        .optional()
        .withMessage(() => `receivers must be an array`)
        .custom((receivers) => {
        if (receivers.length > 20) {
            throw new Error("A maximum of 20 receivers are allowed");
        }
        for (let email of receivers) {
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                throw new Error(`Invalid email address: ${email}`);
            }
        }
        return true;
    }),
    (0, express_validator_1.body)("is_enabled")
        .optional()
        .custom((value) => {
        if (value === 1 || value === 0) {
            return true;
        }
        throw new Error("is_enabled must be 0 or 1");
    }),
    (0, express_validator_1.body)("title")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((tit) => {
        if (tit.length === 0) {
            throw new Error(`At least one Title is required`);
        }
        return true;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { vessel_ids, preference, receivers, is_enabled, title, } = req.body;
        let notificationSummary;
        /** Ensure user cannot subscribe to vessels not assigned to them */
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vessel_ids.filter((id) => id !== "all") } });
        const hasUnauthorizedVessels = vessels.some((v) => !(0, functions_1.canAccessVessel)(req, v));
        if (hasUnauthorizedVessels) {
            return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
        }
        if (receivers) {
            const getDomains = yield EmailDomains_1.default.find({});
            const domains = getDomains.map((domain) => domain.domain);
            if (!req.user.email) {
                return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
            }
            const userEmailDomain = req.user.email.split("@")[1];
            if (!domains.includes(userEmailDomain)) {
                return res.status(400).json({ message: "User email domain is not allowed." });
            }
            const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.additionalEmailAddressesPrivilege);
            const allReceiversValid = receivers.every((email) => {
                const receiverDomain = email.split("@")[1];
                return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
            });
            if (!allReceiversValid) {
                return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
            }
        }
        const processedVesselIds = vessel_ids.map((id) => {
            if (id === "all")
                return "all";
            return new mongoose_1.default.Types.ObjectId(id);
        });
        if (is_enabled) {
            notificationSummary = yield NotificationSummary_1.default.create({
                vessel_ids: processedVesselIds,
                preference,
                receivers,
                title,
                is_enabled: is_enabled === 1,
                created_by: req.user._id,
            });
        }
        else {
            notificationSummary = yield NotificationSummary_1.default.create({
                vessel_ids: processedVesselIds,
                preference,
                receivers,
                title,
                created_by: req.user._id,
            });
        }
        if (receivers) {
            if (receivers.length > 0 && notificationSummary._id) {
                const { email } = req.user;
                for (let receiver of receivers) {
                    const token = (0, functions_1.generateUnsubscribeToken)(receiver, notificationSummary._id.toString());
                    const payload = {
                        addBy: email || "Unknown",
                        vessel: Array.isArray(notificationSummary.title)
                            ? notificationSummary.title.join(",")
                            : notificationSummary.title || "Unknown",
                        preference: Array.isArray(notificationSummary.preference)
                            ? notificationSummary.preference.join("/")
                            : notificationSummary.preference || "Unknown",
                        link: `${process.env.API_URL}/summaryReports/unsubscribe/email?token=${token}`,
                    };
                    const emailBody = (0, Email_1.SUMMARY_SUBSCRIPTION_EMAIL_CONTENT)(payload, new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear());
                    (0, email_1.sendEmail)({
                        to: receiver,
                        subject: "Notification Alert",
                        html: emailBody,
                    });
                }
            }
        }
        res.status(201).json(notificationSummary);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.get("/unsubscribe/email", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UNSUBSCRIBE_NOTIFICATION_SUMMARIES), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // decrypt jwt_token to get email and notification_summary id in it
    const { token } = req.query;
    const { notificationId, email } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
    try {
        const notificationAlert = yield NotificationSummary_1.default.findById(notificationId);
        if (!notificationAlert) {
            return res.redirect(`${process.env.APP_URL}/subscription?status=404&title=Something Went Wrong&message=Notification Summary does not exist`);
        }
        if (!notificationAlert.receivers.includes(email)) {
            const user = yield User_1.default.findOne({ email });
            if (user && user._id.toString() === notificationAlert.created_by.toString()) {
                yield NotificationSummary_1.default.updateOne({ _id: notificationId }, { $set: { is_enabled: false, updated_at: new Date().toISOString() } });
                return res.redirect(`${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification summary emails `);
            }
            return res.redirect(`${process.env.APP_URL}/subscription?status=401&title=Email Removed&message=This email does not exist or already removed from notification alert`);
        }
        const updateRes = yield NotificationSummary_1.default.updateOne({ _id: notificationId }, { $pull: { receivers: email } });
        if (!updateRes.modifiedCount) {
            return res.redirect(`${process.env.APP_URL}/subscription?status=404&title=Email Removed&message=This email does not exist or already removed from notification summary`);
        }
        return res.redirect(`${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
exports.default = router;
