"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ThingsboardDevices_1 = __importDefault(require("../models/ThingsboardDevices"));
const microservice_socket_1 = __importDefault(require("../modules/microservice_socket"));
class ThingsBoardService {
    getAllDevices() {
        return __awaiter(this, void 0, void 0, function* () {
            return ThingsboardDevices_1.default.find({}, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
        });
    }
    getDeviceByUnitId(unitId) {
        return __awaiter(this, void 0, void 0, function* () {
            return ThingsboardDevices_1.default.findOne({ deviceName: unitId }, { deviceId: 1, deviceName: 1, accessToken: 1, dashboardId: 1 });
        });
    }
    resetDashboards() {
        microservice_socket_1.default.emit("thingsboard/reset-dashboard");
    }
}
exports.default = new ThingsBoardService();
