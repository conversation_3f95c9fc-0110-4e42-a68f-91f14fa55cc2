"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const functions_1 = require("../utils/functions");
const awsS3_1 = require("../modules/awsS3");
const awsS3_2 = require("../modules/awsS3");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/signedUrl", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_FILE_URL), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("bucket_name")
        .isString()
        .withMessage("bucket_name must be a string")
        .notEmpty()
        .withMessage("bucket_name must not be an empty string"),
    (0, express_validator_1.query)("key").isString().withMessage("key must be a string").notEmpty().withMessage("key must not be an empty string"),
    (0, express_validator_1.query)("region").isString().withMessage("region must be a string").notEmpty().withMessage("region must not be an empty string"),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { bucket_name: Bucket, key: Key, region } = req.query;
    try {
        awsS3_1.s3.config.update({ region, signatureVersion: "v4" });
        const fileType = Key.split(".").pop();
        const ResponseContentType = (0, functions_1.getContentTypeFromFileExtension)(fileType || "");
        const signedUrl = awsS3_1.s3.getSignedUrl("getObject", { Bucket, Key, Expires: 3600, ResponseContentType });
        res.status(200).send({ signedUrl });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/signedUrl/batch", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_BATCH_URLS), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("batch").isArray().withMessage('batch should be an array of objects with "bucket_name" and "key" fields'),
    (0, express_validator_1.body)("batch.*.bucket_name").isString().notEmpty().withMessage("bucket_name must be a non-empty string in batch"),
    (0, express_validator_1.body)("batch.*.key").isString().notEmpty().withMessage("key must be a non-empty string in batch"),
    (0, express_validator_1.body)("batch.*.region").isString().notEmpty().withMessage("region must be a non-empty string in batch"),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { batch } = req.body;
        const signedUrls = batch.map(({ bucket_name, key, region }) => {
            awsS3_1.s3.config.update({ region, signatureVersion: "v4" });
            const fileType = key.split(".").pop();
            const ResponseContentType = (0, functions_1.getContentTypeFromFileExtension)(fileType || "");
            const signedUrl = awsS3_1.s3.getSignedUrl("getObject", { Bucket: bucket_name, Key: key, Expires: 3600, ResponseContentType });
            return { Bucket: bucket_name, Key: key, signedUrl };
        });
        res.status(200).send({ signedUrls });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/cloudfront/signedUrl", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_CLOUDFRONT_SIGNED_URL), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("key")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("bucketName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
], req, res, next), (req, res) => {
    try {
        const { key, bucketName, region } = req.query;
        if (!process.env.CLOUDFRONT_KEY_PAIR_ID || !process.env.CLOUDFRONT_PRIVATE_KEY) {
            return res.status(500).send({ message: "CloudFront configuration missing" });
        }
        const cloudfrontFileName = bucketName.includes(awsS3_1.s3Config.buckets.assets.name || "") || bucketName.includes(awsS3_1.s3Config.buckets.compressedItems.name || "")
            ? key
            : bucketName + "/" + region + "/" + key;
        const signedUrl = (0, awsS3_2.getCloudfrontSignedUrl)({ fileName: cloudfrontFileName, bucketName });
        if (signedUrl === null) {
            // console.log(`[S3 Route] CloudFront fallback for ${bucketName}/${key}`);
            const fallbackUrl = (0, awsS3_2.generateS3FallbackUrl)(bucketName, key, region);
            return res.status(200).send({ signedUrl: fallbackUrl });
        }
        return res.status(200).send({ signedUrl });
    }
    catch (err) {
        console.error("CloudFront signing error:", err);
        (0, functions_1.validateError)(err, res);
    }
});
router.post("/cloudfront/signedUrl/batch", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_CLOUDFRONT_BATCH_URLS), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("batch").isArray().withMessage('batch should be an array of objects with "bucketName" and "key" fields'),
    (0, express_validator_1.body)("batch.*.bucketName").isString().notEmpty().withMessage("bucketName must be a non-empty string in batch"),
    (0, express_validator_1.body)("batch.*.key").isString().notEmpty().withMessage("key must be a non-empty string in batch"),
    (0, express_validator_1.body)("batch.*.region").optional().isString().withMessage("region must be a string in batch"),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { batch } = req.body;
    try {
        const ts = new Date().getTime();
        if (!process.env.CLOUDFRONT_KEY_PAIR_ID || !process.env.CLOUDFRONT_PRIVATE_KEY) {
            return res.status(500).send({ message: "CloudFront configuration missing" });
        }
        const signedUrls = batch.map(awsS3_2.processBatchItem);
        console.log(`[S3 Route] CloudFront batch time taken: ${new Date().getTime() - ts} ms`);
        res.status(200).send({ signedUrls });
    }
    catch (err) {
        console.error("CloudFront batch signing error:", err);
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   name: Storage
 *   description: Fetch file URLs
 */
/**
 * @swagger
 * /s3/signedUrl:
 *   get:
 *     summary: Generate a signed URL for a file from S3.
 *     description: Fetches a pre-signed URL to access a file from the specified S3 bucket. The signed URL is valid for 1 hour (3600 seconds).<br/>Rate limited to 20 requests every 5 seconds.
 *     tags: [Storage]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: bucket_name
 *         required: true
 *         schema:
 *           type: string
 *         description: The name of the S3 bucket.
 *         example: "smartmast-prototype-33-ap"
 *       - in: query
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: The key (file path) in the S3 bucket.
 *         example: "artifacts/2024-09-21T07:22:34.941Z/image/prototype-33_cam-1_2024-09-21T08:41:43.736Z.jpg"
 *       - in: query
 *         name: region
 *         required: true
 *         schema:
 *           type: string
 *         description: The AWS region where the S3 bucket is located.
 *         example: "ap-southeast-1"
 *     responses:
 *       200:
 *         description: Successfully generated signed URL.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 signedUrl:
 *                   type: string
 *                   description: Signed URL for the file.
 *                   example: "https://s3.amazonaws.com/bucket_name/file_key?AWSAccessKeyId=ACCESSKEY&Expires=1479150230&Signature=..."
 *       400:
 *         description: Bad request (e.g., missing required fields).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Missing required parameters"
 *       429:
 *         description: Too many requests.
 *       500:
 *         description: Internal server error.
 */
/**
 * @swagger
 * /s3/signedUrl/batch:
 *   post:
 *     summary: Generate signed URLs for a batch of files from S3.
 *     description: Fetches pre-signed URLs to access multiple files from the specified S3 buckets. The signed URLs are valid for 1 hour (3600 seconds).<br/>Rate limited to 20 requests every 5 seconds.
 *     tags: [Storage]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       description: Provide an array of file details for batch requests.
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batch:
 *                 type: array
 *                 description: An array of objects representing files for batch requests.
 *                 items:
 *                   type: object
 *                   properties:
 *                     bucket_name:
 *                       type: string
 *                       description: The name of the S3 bucket.
 *                       example: "smartmast-prototype-33-ap"
 *                     key:
 *                       type: string
 *                       description: The key (file path) in the S3 bucket.
 *                       example: "artifacts/2024-09-21T07:22:34.941Z/image/prototype-33_cam-1_2024-09-21T08:41:43.736Z.jpg"
 *                     region:
 *                       type: string
 *                       description: The AWS region where the S3 bucket is located.
 *                       example: "ap-southeast-1"
 *     responses:
 *       200:
 *         description: Successfully generated signed URLs.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 signedUrls:
 *                   type: array
 *                   description: Array of signed URLs for batch requests.
 *                   items:
 *                     type: object
 *                     properties:
 *                       Bucket:
 *                         type: string
 *                         description: The name of the S3 bucket.
 *                       Key:
 *                         type: string
 *                         description: The key (file path) in the S3 bucket.
 *                       signedUrl:
 *                         type: string
 *                         description: The signed URL for the file.
 *                         example: "https://s3.amazonaws.com/bucket_name/file_key?AWSAccessKeyId=ACCESSKEY&Expires=1479150230&Signature=..."
 *       400:
 *         description: Bad request (e.g., missing required fields).
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Missing required parameters"
 *       429:
 *         description: Too many requests.
 *       500:
 *         description: Internal server error
 */
