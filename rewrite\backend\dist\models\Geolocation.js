"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const GeolocationSchema = new mongoose_1.default.Schema({
    location: {
        type: {
            type: String,
            enum: ["Point"],
            default: "Point",
        },
        coordinates: {
            type: [Number],
            required: true,
        },
    },
    name: {
        type: String,
        required: true,
    },
});
GeolocationSchema.index({ location: "2dsphere" });
const Geolocation = db_1.default.qm.model("Geolocation", GeolocationSchema, "geolocations");
exports.default = Geolocation;
