{"compilerOptions": {"target": "ES6", "module": "CommonJS", "rootDir": "src", "outDir": "dist", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "baseUrl": ".", "typeRoots": ["./src/types", "./node_modules/@types"], "paths": {"src/*": ["src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "src/tests", "src/**/*.test.ts", "src/**/*.spec.ts", "src/*.old.ts"]}