import React, { useState, useRef } from "react";
import { Pa<PERSON>, PlayArrow } from "@mui/icons-material";
import { Grid, IconButton, Typography } from "@mui/material";

function formatTime(time) {
    if (isNaN(time)) return "00:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
}

function AudioPlayer({ src }) {
    const audioRef = useRef();
    const progressBarRef = useRef();

    const [progress, setProgress] = useState(0); // percentage (0-100)
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0); // seconds
    const [duration, setDuration] = useState(0); // seconds

    const onTimeUpdate = () => {
        if (!audioRef.current) return;
        const current = audioRef.current.currentTime;
        setCurrentTime(current);
        setProgress((current / duration) * 100 || 0);
    };

    const onLoadedMetadata = () => {
        if (audioRef.current) {
            setDuration(audioRef.current.duration);
        }
    };
    const handleEnded = () => {
        setIsPlaying(false);
    };

    const handlePlayPause = () => {
        if (!audioRef.current) return;
        if (audioRef.current.paused) {
            audioRef.current.play();
            setIsPlaying(true);
        } else {
            audioRef.current.pause();
            setIsPlaying(false);
        }
    };

    const handleScrubClick = (e) => {
        if (!audioRef.current || !progressBarRef.current) return;

        const rect = progressBarRef.current.getBoundingClientRect();
        const clickX = e.clientX - rect.left; // position clicked inside progress bar
        const newTime = (clickX / rect.width) * duration;

        audioRef.current.currentTime = newTime;
        setCurrentTime(newTime);
        setProgress((newTime / duration) * 100);

        audioRef.current.play();
        setIsPlaying(true);
    };

    return (
        <Grid
            style={{
                background: "#282C39",
                borderRadius: "10px",
                padding: "8px 12px",
                display: "flex",
                alignItems: "center",
                gap: "10px",
                color: "white",
                width: "100%",
                height: "40px",
            }}
        >
            <IconButton
                onClick={handlePlayPause}
                sx={{
                    padding: 0,
                    background: "none",
                    border: "none",
                    color: "white",
                    cursor: "pointer",
                    fontWeight: "bold",
                }}
            >
                {isPlaying ? <Pause sx={{ fontSize: 20 }} /> : <PlayArrow sx={{ fontSize: 20 }} />}
            </IconButton>

            {/* Progress Bar */}
            <Grid
                ref={progressBarRef}
                onClick={handleScrubClick}
                style={{
                    flex: 1,
                    height: "7px",
                    background: "white",
                    borderRadius: "3px",
                    position: "relative",
                    cursor: "pointer",
                }}
            >
                <Grid
                    style={{
                        height: "7px",
                        width: `${progress}%`,
                        background: "#3A8DFF",
                        borderRadius: "3px",
                    }}
                ></Grid>
            </Grid>

            {/* Time Display */}
            <Grid style={{ minWidth: "70px", textAlign: "right", fontSize: "12px" }}>
                <Typography sx={{ fontWeight: "bold" }}>
                    {formatTime(currentTime)} / {formatTime(duration)}
                </Typography>
            </Grid>

            <audio
                ref={audioRef}
                src={src}
                onTimeUpdate={onTimeUpdate}
                onLoadedMetadata={onLoadedMetadata}
                onEnded={handleEnded}
                style={{ display: "none" }}
            />
        </Grid>
    );
}

export default AudioPlayer;
