import mongoose from "mongoose";

export interface IRegionGroup {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    timezone: string;
    vessel_ids?: mongoose.Types.ObjectId[];
    created_by: mongoose.Types.ObjectId | string | { _id: mongoose.Types.ObjectId; name: string };
    creation_timestamp?: Date;
    vessels?: { vessel_id: mongoose.Types.ObjectId | string; unit_id: string; name: string }[];
}
