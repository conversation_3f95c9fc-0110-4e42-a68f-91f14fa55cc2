"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStaticMap = getStaticMap;
exports.generateNumberedIconBuffer = generateNumberedIconBuffer;
exports.getClustersAndPoints = getClustersAndPoints;
// const { default: Supercluster } = require("supercluster");
const geo_viewport_1 = __importDefault(require("@mapbox/geo-viewport"));
const sharp_1 = __importDefault(require("sharp"));
const IconSize = {
    small: 24,
    medium: 36,
    large: 48,
};
const defaultOptions = {
    clusterRadius: 80,
    mapType: "terrain",
    zoom: 3,
    scale: 2,
    width: 800,
    height: 400,
};
/**
 * Calculates the geographic center (centroid) of an array of points
 * using the Cartesian vector average method. (Corrected Version)
 *
 * @param {Array<{lat: number, lon: number}> | Array<{latitude: number, longitude: number}>} points - An array of point objects.
 *                 Accepts objects with lat/lon or latitude/longitude properties.
 * @returns {LatLon_NvectorEllipsoidal | null} The calculated center point as a LatLonEllipsoidal object,
 *                                     or null if the input array is empty or contains no valid points.
 */
function getCenterFromPointsVector(points) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d;
        // const { default: LatLonEllipsoidal, Vector3d } = await import('geodesy/latlon-ellipsoidal.js');
        const { default: LatLon_NvectorEllipsoidal, Nvector } = yield Promise.resolve().then(() => __importStar(require("geodesy/latlon-nvector-ellipsoidal.js")));
        if (!points || points.length === 0) {
            return null; // No points, no center
        }
        if (points.length === 1) {
            const p = points[0];
            const lat = (_a = p.lat) !== null && _a !== void 0 ? _a : p.latitude;
            const lon = (_b = p.lng) !== null && _b !== void 0 ? _b : p.longitude;
            // Basic validation for single point
            if (typeof lat !== "number" || typeof lon !== "number" || isNaN(lat) || isNaN(lon)) {
                console.warn("Skipping single invalid point:", p);
                return null;
            }
            try {
                const ll = new LatLon_NvectorEllipsoidal(lat, lon);
                return { lat: ll.lat, lon: ll.lon };
            }
            catch (e) {
                console.warn(`Skipping single point due to geodesy error: ${e.message}`, p);
                return null;
            }
        }
        let sumVector = new Nvector(0, 0, 0);
        let validPointsCount = 0;
        for (const p of points) {
            const lat = (_c = p.lat) !== null && _c !== void 0 ? _c : p.latitude;
            const lon = (_d = p.lng) !== null && _d !== void 0 ? _d : p.longitude;
            if (typeof lat !== "number" || typeof lon !== "number" || isNaN(lat) || isNaN(lon)) {
                console.warn("Skipping invalid point (NaN or wrong type):", p);
                continue;
            }
            try {
                const pointLatLon = new LatLon_NvectorEllipsoidal(lat, lon);
                const vector = pointLatLon.toNvector();
                sumVector = sumVector.plus(vector);
                validPointsCount++;
            }
            catch (e) {
                console.warn(`Skipping point due to geodesy error (e.g., invalid lat/lon range [-90..90, -180..180]): ${e.message}`, p);
            }
        }
        if (validPointsCount === 0) {
            console.warn("No valid points found in the input array.");
            return null; //INFO: No valid points processed
        }
        const averageVector = sumVector.times(1 / validPointsCount);
        try {
            return new Nvector(averageVector.x, averageVector.y, averageVector.z).toLatLon();
        }
        catch (e) {
            // Possible if the average vector somehow ends up being zero (e.g., exactly two antipodal points)
            console.error("Error converting average vector back to LatLon:", e.message, averageVector);
            return null;
        }
    });
}
function numberToText(num) {
    return num < 1000 ? String(num) : num < 1000000 ? Math.ceil(num / 1000) + "K" : Math.ceil(num / 1000000) + "M";
}
/**
 * Generates a prettier SVG cluster icon string.
 *
 * @param {number} count - The number to display.
 * @param bgColor
 * @param textColor
 * @returns {string} The SVG string.
 */
function generateNumberedIconBuffer(count_1) {
    return __awaiter(this, arguments, void 0, function* (count, bgColor = "blue", textColor = "white") {
        // --- Default Options ---
        const numCount = typeof count === "number" ? count : parseInt(count, 10) || 0;
        const config = {
            size: numCount < 10 ? IconSize.small : numCount < 100 ? IconSize.medium : IconSize.large,
            bgColor: bgColor,
            textColor: textColor,
            borderColor: "rgba(0,0,0,0.7)",
            borderWidth: 1.5,
            useGradient: true,
            useTextShadow: true,
            fontFamily: "Arial, Helvetica, sans-serif",
        };
        const numSize = Math.max(16, Number(config.size)); // Min size 16px
        const center = numSize / 2;
        // Adjust radius slightly based on border width to prevent border clipping
        // const radius = (numSize / 2) * 0.95 - config.borderWidth / 2;
        const cleanBgColor = String(config.bgColor).replace(/[^a-zA-Z0-9#(),\s.%]/g, "") || "blue";
        const cleanTextColor = String(config.textColor).replace(/[^a-zA-Z0-9#(),\s.%]/g, "") || "white";
        // const cleanBorderColor =
        //     !config.borderColor || config.borderColor === "none" ? "none" : String(config.borderColor).replace(/[^a-zA-Z0-9#(),\s.%]/g, "");
        // const numBorderWidth = Math.max(0, parseFloat(config.borderWidth) || 0);
        // --- Dynamic Font Size ---
        let fontSize = numSize * 0.5; // Base font size
        if (numCount >= 1000)
            fontSize = numSize * 0.32;
        else if (numCount >= 100)
            fontSize = numSize * 0.4;
        else if (numCount >= 10)
            fontSize = numSize * 0.45;
        // // --- Optional Gradient Definition ---
        // let gradientDef = "";
        // let fillStyle = cleanBgColor;
        // if (config.useGradient) {
        //     const gradientId = `grad-${Math.random().toString(36).substring(2, 8)}`;
        //     gradientDef = `
        // <defs>
        //   <radialGradient id="${gradientId}" cx="50%" cy="40%" r="60%" fx="50%" fy="40%">
        //     <stop offset="0%" style="stop-color:${cleanBgColor}; stop-opacity:0.7" />
        //     <stop offset="100%" style="stop-color:${cleanBgColor}; stop-opacity:1" />
        //   </radialGradient>
        // </defs>`;
        //     fillStyle = `url(#${gradientId})`;
        // }
        // // Simple shadow by duplicating the text slightly offset
        // let textShadowElement = "";
        // if (config.useTextShadow) {
        //     const shadowOffset = Math.max(0.5, fontSize * 0.05); // Small offset based on font size
        //     textShadowElement = `
        //   <text x="${center + shadowOffset}" y="${center + shadowOffset}" dominant-baseline="middle" text-anchor="middle"
        //         font-size="${fontSize}px" font-family="${config.fontFamily}" font-weight="bold" fill="rgba(0,0,0,0.4)">
        //         ${numberToText(numCount)}
        //   </text>`;
        // }
        // // Using backticks for template literals makes multi-line strings easier
        // const svgString = `
        //     <svg width="${numSize}" height="${numSize}" viewBox="0 0 ${numSize} ${numSize}" xmlns="http://www.w3.org/2000/svg">
        //         ${gradientDef}
        //         <circle cx="${center}" cy="${center}" r="${radius}"
        //                 fill="${fillStyle}"
        //                 stroke="${cleanBorderColor}"
        //                 stroke-width="${numBorderWidth}"/>
        //         ${textShadowElement}
        //         <text x="${center}" y="${center}" dominant-baseline="middle" text-anchor="middle"
        //               font-size="${fontSize}px" font-family="${config.fontFamily}" font-weight="bold" fill="${cleanTextColor}">
        //               ${numberToText(numCount)}
        //         </text>
        //     </svg>
        // `;
        const svgString = `
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 ${numSize} ${numSize}"
          width="${numSize}"
          height="${numSize}"
          fill="none"
        >
          <title>Map Cluster Icon</title>
        
          <circle
            cx="${center}"
            cy="${center}"
            r="${numSize / 2 - 1}"           
            fill="${cleanBgColor}"   
            fill-opacity="0.3" 
          />
        
          <circle
            cx="${center}"
            cy="${center}"
            r="${numSize / 2 - 3}"           
            fill="${cleanBgColor}"   
            fill-opacity="0.5" 
          />
        
          <circle
            cx="${center}"
            cy="${center}"
            r="${numSize / 2 - 5}"            
            fill="${cleanBgColor}"   
            fill-opacity="0.9" 
          />
          
          <text x="${center}" y="${center}" dominant-baseline="middle" text-anchor="middle"
            font-size="${fontSize}px" font-family="${config.fontFamily}" font-weight="bold" fill="${cleanTextColor}">
            ${numberToText(numCount)}
          </text>
        </svg>
    `;
        try {
            return yield (0, sharp_1.default)(Buffer.from(svgString)).png().toBuffer();
        }
        catch (err) {
            console.error(`Error generating icon buffer for count ${numCount}:`, err);
            throw err;
        }
    });
}
// async function generateNumberedIconBuffer2(count, bgColor = "blue", textColor = "white") {
//     //INFO: Sanitize/validate inputs
//     const numCount = parseInt(count, 10) || 0;
//     const numSize = count < 10 ? IconSize.small : count < 100 ? IconSize.medium : IconSize.large;
//     const cleanBgColor = String(bgColor).replace(/[^a-zA-Z0-9#(),\s]/g, "") || "blue"; // Basic sanitize
//     const cleanTextColor = String(textColor).replace(/[^a-zA-Z0-9#(),\s]/g, "") || "white";
//
//     //INFO: Determine font size relative to icon size
//     let fontSize = numSize * 0.5;
//     if (numCount >= 1000)
//         fontSize = numSize * 0.3; // Smaller font for 4+ digits
//     else if (numCount >= 100) fontSize = numSize * 0.35; // Slightly smaller for 3 digits
//
//     //INFO: Generate SVG
//     const svgText = `
//         <svg width="${numSize}" height="${numSize}" xmlns="http://www.w3.org/2000/svg">
//             <circle cx="${numSize / 2}" cy="${numSize / 2}" r="${(numSize / 2) * 0.9}" fill="${cleanBgColor}" stroke="rgba(0,0,0,0.2)" stroke-width="1"/>
//             <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle"
//                 font-size="${fontSize}px" font-family="Arial, sans-serif" font-weight="bold" fill="${cleanTextColor}">
//                 ${numberToText(numCount)}
//             </text>
//         </svg>
//     `;
//
//     try {
//         return await sharp(Buffer.from(svgText)).png().toBuffer();
//     } catch (err) {
//         console.error(`Error generating icon buffer for count ${numCount}:`, err);
//         throw err;
//     }
// }
/**
 * Converts an array of {lat, lng} points to GeoJSON Point features
 * Supercluster expects [lng, lat] coordinate order.
 * @param {Array<{lat: number, lng: number}>} points
 * @returns {Array<Object>} GeoJSON Point features
 */
function pointsToGeoJSON(points) {
    return points.map((point, index) => ({
        type: "Feature",
        properties: {
            pointId: index,
            isCluster: false, // supercluster will add its own
        },
        geometry: {
            type: "Point",
            coordinates: [point.lng, point.lat], // GeoJSON uses [longitude, latitude]
        },
    }));
}
/**
 * Generates marker parameters for the Google Static Map API.
 * @param {Array<Object>} clustersAndPoints - Features from supercluster.getClusters()
 * @param {number | undefined} clusterRadius - Pixel radius for clustering
 * @param {string} defaultMarkerColor - e.g., 'red'
 * @param {string} clusterMarkerColor - e.g., 'blue'
 * @returns {Array<string>} Array of marker strings (e.g., "color:blue|label:5|lat,lng")
 */
function generateMarkerParams(clustersAndPoints, defaultMarkerColor = "red", clusterMarkerColor = "blue") {
    const clusterRoute = `${process.env.API_URL}/notificationsAlerts/cluster/`;
    const params = new URLSearchParams({
        bgColor: clusterMarkerColor,
        textColor: "white",
    });
    const markerStrings = [];
    for (const feature of clustersAndPoints) {
        const [lng, lat] = feature.geometry.coordinates;
        const props = feature.properties;
        if (props.cluster) {
            const pointCount = props.point_count;
            markerStrings.push(`icon:${clusterRoute}${pointCount}?${params.toString()}|${lat},${lng}`);
        }
        else {
            markerStrings.push(`color:${defaultMarkerColor}|size:tiny|${lat},${lng}`);
        }
    }
    return markerStrings;
}
function getClustersAndPoints(pointsData_1) {
    return __awaiter(this, arguments, void 0, function* (pointsData, options = {}) {
        if (!options.centerLat || !options.centerLng) {
            const center = yield getCenterFromPointsVector(pointsData);
            if (center) {
                options.centerLat = center.lat;
                options.centerLng = center.lon;
            }
        }
        let { centerLat, centerLng, clusterRadius = 60, maxZoom = 16, zoom = 3, width = 800, height = 400 } = options;
        if (pointsData.length) {
            const { default: Supercluster } = yield Promise.resolve().then(() => __importStar(require("supercluster")));
            const index = new Supercluster({
                radius: clusterRadius, // cluster radius in pixels
                maxZoom: maxZoom,
                minZoom: 0,
                log: false,
            });
            index.load(pointsToGeoJSON(pointsData));
            const bounds = geo_viewport_1.default.bounds([centerLng, centerLat], zoom, [width, height]);
            const clustersAndPoints = index.getClusters(bounds, zoom);
            return clustersAndPoints;
        }
        return undefined;
    });
}
/**
 * Generates a Google Static Map URL with clustered points.
 *
 * @param clustersAndPoints
 * @param {Object} options - Optional parameters.
 * @param {number} options.centerLat - Map center latitude.
 * @param {number} options.centerLng - Map center longitude.
 * @param {number} options.zoom - Map zoom level (0-21).
 * @param {number} options.width - Map width in pixels (max 640 for free tier).
 * @param {number} options.height - Map height in pixels (max 640 for free tier).
 * @param {number} options.clusterRadius - Pixel radius for clustering (default 60).
 * @param {number} options.maxZoom - Max zoom level to cluster up to (default 16).
 * @param {string} options.mapType - 'roadmap', 'satellite', 'hybrid', 'terrain' (default 'roadmap').
 * @returns {string|Error} The generated Static Map URL or an Error object if URL is too long.
 */
function getStaticMapWithClustersUrl(clustersAndPoints, options = {}) {
    const baseUrl = "https://maps.googleapis.com/maps/api/staticmap";
    const { centerLat, centerLng, mapType, zoom, width, height, format, scale } = options;
    const args = {
        zoom,
        scale,
        size: `${width}x${height}`,
        maptype: mapType,
        format,
        key: process.env.GOOGLE_API_KEY,
    };
    // set center point for map or use auto adjust of the borders basing on the markers
    if (centerLat && centerLng) {
        args.center = `${centerLat},${centerLng}`;
    }
    else {
        args.auto = "";
    }
    const params = new URLSearchParams(args);
    if (clustersAndPoints.length) {
        const markerParams = generateMarkerParams(clustersAndPoints);
        markerParams.forEach((marker) => {
            params.append("markers", marker);
        });
    }
    return `${baseUrl}?${params.toString()}`;
}
/**
 * Generates a Google Static Map data.
 *
 * @param clustersAndPoints
 * @param {string} format - format of the map image (default 'png').
 * @returns {Object|Error} The generated Static Map data.
 */
function getStaticMap(clustersAndPoints_1) {
    return __awaiter(this, arguments, void 0, function* (clustersAndPoints, format = "png") {
        try {
            const options = Object.assign(Object.assign({}, defaultOptions), { format: format || "png" });
            const url = getStaticMapWithClustersUrl(clustersAndPoints, options);
            const response = yield fetch(url);
            if (!response.ok) {
                // Handle HTTP errors (e.g., 400, 403, 500)
                const errorText = yield response.text(); // Get error message from response
                throw new Error(`Static Map API request failed: ${response.status} - ${errorText}`);
            }
            return {
                mimeType: "image/png",
                source: response,
            };
        }
        catch (error) {
            console.error("Error fetching static map:", error);
            return {};
        }
    });
}
