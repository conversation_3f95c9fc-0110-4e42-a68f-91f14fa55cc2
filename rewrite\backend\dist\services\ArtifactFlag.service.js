"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const ArtifactFlag_1 = __importDefault(require("../models/ArtifactFlag"));
const db_1 = __importDefault(require("../modules/db"));
const awsS3_1 = require("../modules/awsS3");
class ArtifactFlagService {
    flagArtifact(artifactId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const artifact = yield db_1.default.qmai.collection("analysis_results").findOne({
                _id: new mongoose_1.default.Types.ObjectId(artifactId),
            });
            if (!artifact) {
                throw new Error("Artifact not found");
            }
            const existingFlag = yield ArtifactFlag_1.default.findOne({
                artifactId: new mongoose_1.default.Types.ObjectId(artifactId),
                flaggedBy: new mongoose_1.default.Types.ObjectId(userId),
            });
            if (existingFlag) {
                throw new Error("You have already flagged this artifact");
            }
            const flag = new ArtifactFlag_1.default({
                artifactId: new mongoose_1.default.Types.ObjectId(artifactId),
                flaggedBy: new mongoose_1.default.Types.ObjectId(userId),
            });
            yield flag.save();
            return flag;
        });
    }
    getFlaggedArtifacts() {
        return __awaiter(this, void 0, void 0, function* () {
            const flaggedArtifacts = yield ArtifactFlag_1.default.aggregate([
                {
                    $lookup: {
                        from: "users",
                        let: { userId: "$flaggedBy" },
                        pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userId"] } } }, { $project: { _id: 1, name: 1, email: 1 } }],
                        as: "flaggedByUser",
                    },
                },
                {
                    $group: {
                        _id: "$artifactId",
                        flags: {
                            $push: {
                                _id: "$_id",
                                flaggedBy: "$flaggedBy",
                                flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                                flaggedAt: "$flaggedAt",
                            },
                        },
                        flagCount: { $sum: 1 },
                        latestFlagDate: { $max: "$flaggedAt" },
                    },
                },
                { $sort: { latestFlagDate: -1 } },
            ]);
            const artifactIds = flaggedArtifacts.map((item) => item._id);
            if (artifactIds.length === 0) {
                return [];
            }
            const artifacts = yield db_1.default.qmai
                .collection("analysis_results")
                .find({ _id: { $in: artifactIds } }, {
                projection: {
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    thumbnail_image_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    created_at: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                    portal: 1,
                },
            })
                .toArray();
            const results = flaggedArtifacts
                .map((flagData) => {
                const artifact = artifacts.find((a) => a._id.toString() === flagData._id.toString());
                if (!artifact)
                    return null;
                const artifactWithUrls = Object.assign({}, artifact);
                if (artifact.image_path) {
                    artifactWithUrls.image_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.video_path) {
                    artifactWithUrls.video_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.video_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.thumbnail_image_path) {
                    artifactWithUrls.thumbnail_url = (0, awsS3_1.processBatchItem)({
                        bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                        key: artifact.thumbnail_image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                return {
                    artifactId: artifact._id,
                    artifact: artifactWithUrls,
                    flags: flagData.flags.map((flag) => (Object.assign(Object.assign({}, flag), { user: flag.flaggedByUser, created_at: flag.flaggedAt }))),
                    flagCount: flagData.flagCount,
                    latestFlagDate: flagData.latestFlagDate,
                };
            })
                .filter((result) => { var _a; return result && result.artifact && ((_a = result.artifact.portal) === null || _a === void 0 ? void 0 : _a.is_archived) !== true; });
            return results;
        });
    }
    unflagArtifact(artifactId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const flag = yield ArtifactFlag_1.default.findOneAndDelete({
                artifactId: new mongoose_1.default.Types.ObjectId(artifactId),
                flaggedBy: new mongoose_1.default.Types.ObjectId(userId),
            });
            if (!flag) {
                throw new Error("Flag not found");
            }
            return flag;
        });
    }
    getUserFlaggedArtifactIds(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const flags = yield ArtifactFlag_1.default.find({ flaggedBy: new mongoose_1.default.Types.ObjectId(userId) }, { artifactId: 1, _id: 0 });
            return flags.map((flag) => flag.artifactId.toString());
        });
    }
    removeAllFlagsFromArtifact(artifactId) {
        return __awaiter(this, void 0, void 0, function* () {
            const result = yield ArtifactFlag_1.default.deleteMany({
                artifactId: new mongoose_1.default.Types.ObjectId(artifactId),
            });
            return result;
        });
    }
}
exports.default = new ArtifactFlagService();
