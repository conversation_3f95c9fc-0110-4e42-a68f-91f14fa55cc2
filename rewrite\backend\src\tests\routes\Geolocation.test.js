const User = require('../../models/User');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Geolocation = require('../../models/Geolocation');
const { fetchGeolocation } = require('../../modules/geolocation');
const request = require("supertest");
const app = require('../../server');


jest.mock('../../modules/geolocation', () => ({
    fetchGeolocation: jest.fn(() => { }),
}));

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Geolocation API', () => {

    describe('GET /api/geolocations/', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/geolocations')
                        .query({ lat: 122.123, lng: 123.123 });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if lat is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lng: 123.123 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if lng is missing', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lat: 122.123 });
                    expect(res.status).toBe(400);
                });

                it('should return 200 and return location name if location exists in DB', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Geolocation.findOne.mockResolvedValueOnce({
                        name: 'San Francisco, CA',
                        location: { type: 'Point', coordinates: [123.123, 122.123] }
                    });

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lat: 122.123, lng: 123.123 });

                    expect(res.status).toBe(200);
                    expect(res.body.name).toBe('San Francisco, CA');
                    expect(Geolocation.findOne).toHaveBeenCalledWith({
                        location: {
                            $geoWithin: {
                                $centerSphere: [[123.123, 122.123], 30 / 6371],
                            },
                        },
                    });
                });

                it('should return 200 and fetch location name if location does not exist in DB', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Geolocation.findOne.mockResolvedValueOnce(null);
                    fetchGeolocation.mockResolvedValue('San Francisco, CA');
                    Geolocation.create.mockResolvedValueOnce({
                        location: { type: 'Point', coordinates: [123.123, 122.123] },
                        name: 'San Francisco, CA',
                    });

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lat: 122.123, lng: 123.123 });

                    expect(res.status).toBe(200);
                    expect(res.body.name).toBe('San Francisco, CA');
                    expect(Geolocation.create).toHaveBeenCalledWith({
                        location: { type: 'Point', coordinates: [123.123, 122.123] },
                        name: 'San Francisco, CA',
                    });
                    expect(fetchGeolocation).toHaveBeenCalledWith('122.123', '123.123');
                });

                it('should return 200 and set location name to "Unknown Location" when fetchGeolocation throws an error', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Geolocation.findOne.mockResolvedValueOnce(null);
                    fetchGeolocation.mockRejectedValueOnce(new Error('Failed to fetch location'));

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lat: 122.123, lng: 123.123 });

                    expect(res.status).toBe(200);
                    expect(res.body.name).toBe('Unknown Location');
                    expect(Geolocation.create).toHaveBeenCalledWith({
                        location: { type: 'Point', coordinates: [123.123, 122.123] },
                        name: 'Unknown Location',
                    });
                    expect(fetchGeolocation).toHaveBeenCalledWith('122.123', '123.123');
                });

                it('should return 500 if there is an internal server error', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    Geolocation.findOne.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app).get('/api/geolocations')
                        .set('Authorization', authToken)
                        .query({ lat: 122.123, lng: 123.123 });

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Error retrieving geolocation');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
