"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const User_2 = require("../queries/User");
const mongoose_1 = __importStar(require("mongoose"));
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const Role_1 = __importDefault(require("../models/Role"));
const validator_1 = require("../middlewares/validator");
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const permissions_1 = require("../utils/permissions");
const auth_1 = __importDefault(require("../middlewares/auth"));
const email_1 = require("../modules/email");
const otpService_1 = require("../modules/otpService");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const ApiKey_1 = __importDefault(require("../models/ApiKey"));
const Email_1 = require("../utils/Email");
const Organization_1 = __importDefault(require("../models/Organization"));
const InviteToken_1 = __importDefault(require("../models/InviteToken"));
const Vessel_1 = __importDefault(require("../models/Vessel"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_USERS_LIST), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let page = parseInt(req.query.page) || 1;
        let limit = parseInt(req.query.limit) || 10;
        if (page < 1)
            page = 1;
        if (limit < 1)
            limit = 10;
        const query = {
            is_deleted: false,
        };
        if (!req.user.organization.is_internal) {
            query.organization_id = req.user.organization._id;
        }
        const skip = (page - 1) * limit;
        const totalCount = yield User_1.default.countDocuments(query);
        const totalPages = Math.ceil(totalCount / limit);
        const users = yield User_1.default.aggregate([
            { $match: query },
            {
                $lookup: {
                    from: "organizations",
                    localField: "organization_id",
                    foreignField: "_id",
                    as: "organization",
                },
            },
            {
                $addFields: {
                    organization: { $arrayElemAt: ["$organization", 0] },
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "invited_by",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                username: 1,
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    invited_by: { $arrayElemAt: ["$invited_by", 0] },
                },
            },
            {
                $project: {
                    password: 0,
                    jwt_tokens: 0,
                    reset_password_token: 0,
                    reset_password_expire: 0,
                },
            },
            { $skip: skip },
            { $limit: limit },
        ]);
        res.json({ users, totalCount, totalPages });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/invite", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.INVITE_USER), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("email")
        .isEmail()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("role_id")
        .custom(functions_1.isIntStrict)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("organization_id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels")
        .isArray({ min: 1 })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels.*")
        .custom(mongoose_1.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, role_id, allowed_vessels, organization_id } = req.body;
        let hasManageOrgPermission = false;
        if (req.user && req.user.permissions) {
            hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.manageOrganizations);
        }
        if (!hasManageOrgPermission && req.user.organization_id && req.user.organization_id.toString() !== organization_id.toString()) {
            return res.status(403).json({ message: "Forbidden: You can only invite users to your own organization" });
        }
        const existingUser = yield User_1.default.findOne({ email });
        if (existingUser)
            return res.status(400).json({ message: "User already exists with this email" });
        const roleDetails = yield Role_1.default.findOne({ role_id });
        if (!roleDetails)
            return res.status(400).json({ message: "Role does not exist with this ID" });
        const organizationDetails = yield Organization_1.default.findOne({ _id: organization_id });
        if (!organizationDetails)
            return res.status(400).json({ message: "Organization does not exist with this ID" });
        if (organizationDetails.is_miscellaneous && !roleDetails.denied_permissions.includes(permissions_1.permissions.manageUsers)) {
            return res
                .status(403)
                .json({ message: "Miscellaneous organization user are not allowed to assign a role which have manage user permission" });
        }
        const role = roleDetails.role_name;
        const organization = organizationDetails.name;
        const token = (0, functions_1.generateInvitationLink)(email, role_id.toString(), allowed_vessels, String(req.user._id), String(role), String(organization), String(organization_id));
        const date = new Date();
        const shortToken = Buffer.from(crypto_1.default.randomBytes(16)).toString("hex");
        const inviteToken = new InviteToken_1.default({
            token: token,
            invited_by: req.user._id,
            email,
            role_id,
            role,
            organization_id,
            allowed_vessels,
            short_token: shortToken,
        });
        yield inviteToken.save();
        const link = `${process.env.API_URL}/users/verify-invite?token=${encodeURIComponent(inviteToken.short_token)}`;
        const content = (0, Email_1.INVITE_EMAIL_CONTENT)(link, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
        yield (0, email_1.sendEmail)({
            to: email,
            subject: "Account Creation Invitation",
            html: content,
        });
        res.status(200).json({ link });
    }
    catch (error) {
        (0, functions_1.validateError)(error, res);
    }
}));
router.get("/verify-invite", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.VERIFY_INVITE_USER), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { token } = req.query;
    try {
        const inviteToken = yield InviteToken_1.default.findOne({ short_token: token, is_used: false, is_deleted: false });
        if (!inviteToken)
            return res.status(400).json({ message: "Invalid token" });
        const secretKey = process.env.JWT_SECRET;
        if (!secretKey)
            throw new Error("JWT_SECRET not configured");
        const decoded = jsonwebtoken_1.default.verify(inviteToken.token, secretKey);
        const { email, role_id, role, organization_id, organization_name } = decoded;
        return res.redirect(`${process.env.APP_URL}/signup?token=${inviteToken.short_token}&role=${role}&email=${encodeURIComponent(email)}&role_id=${role_id}&organization_id=${organization_id}&organization_name=${organization_name}`);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_USER), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("username")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("email")
        .isString()
        .isEmail()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("password")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .bail()
        .isLength({ min: 8 })
        .withMessage("Password must be at least 8 characters long"),
    (0, express_validator_1.body)("organization_id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("role_id")
        .custom(functions_1.isIntStrict)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, username, email, password, role_id, organization_id, token } = req.body;
        const inviteToken = yield InviteToken_1.default.findOne({ short_token: token, is_deleted: false, is_used: false });
        if (!inviteToken)
            return res.status(400).json({ message: "Invalid token" });
        const secretKey = process.env.JWT_SECRET;
        if (!secretKey)
            throw new Error("JWT_SECRET not configured");
        const decoded = jsonwebtoken_1.default.verify(inviteToken.token, secretKey);
        if (email != decoded.email)
            return res.status(400).json({ message: "Email is not associated with this link" });
        if (role_id != decoded.role_id)
            return res.status(400).json({ message: "Role_ID is not associated with this link" });
        if (organization_id != decoded.organization_id)
            return res.status(400).json({ message: "Organization_ID is not associated with this link" });
        // const { email, role_id, admin_id, role } = decoded;
        const escapedUsername = (0, functions_1.escapeRegExp)(username);
        const escapedEmail = (0, functions_1.escapeRegExp)(email);
        if (yield User_1.default.findOne({ username: { $regex: new RegExp(`^${escapedUsername}$`, "i") } })) {
            return res.status(400).json({ message: "Username is already taken" });
        }
        if (yield User_1.default.findOne({ email: { $regex: new RegExp(`^${escapedEmail}$`, "i") } })) {
            return res.status(400).json({ message: "Email is already taken" });
        }
        const role = yield Role_1.default.findOne({ role_id });
        if (!role)
            return res.status(404).json({ message: "Role does not exist" });
        const organization = yield Organization_1.default.findOne({ _id: organization_id });
        if (!organization)
            return res.status(404).json({ message: "Organization does not exist" });
        const hashedPassword = yield bcryptjs_1.default.hash(password, 10);
        yield User_1.default.create({
            name,
            username,
            email,
            password: hashedPassword,
            role_id,
            organization_id,
            allowed_vessels: decoded.allowed_vessels || [],
            created_by: inviteToken.invited_by,
        });
        inviteToken.is_used = true;
        yield inviteToken.save();
        const date = new Date();
        const content = (0, Email_1.WELCOME_EMAIL_CONTENT)(name, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
        yield (0, email_1.sendEmail)({
            to: email,
            subject: "Welcome aboard Quartermaster!",
            html: content,
        });
        res.status(201).json({ message: "User created successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/forgot-password", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_PASSWORD_RESET_TOKEN), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("email")
        .isString()
        .isEmail()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.body;
        const user = yield User_1.default.findOne({ email: { $regex: new RegExp(`^${email}$`, "i") } });
        if (!user)
            return res.status(404).json({ message: "No user found with that email address" });
        if (user.reset_password_token && user.reset_password_expire && user.reset_password_expire > Date.now())
            return res.status(400).json({ message: "A reset token already exists and is still valid. Please check your email" });
        const resetToken = Buffer.from(crypto_1.default.randomBytes(32)).toString("hex");
        user.reset_password_token = crypto_1.default.createHash("sha256").update(resetToken).digest("hex");
        user.reset_password_expire = Date.now() + 10 * 60 * 1000; // Token valid for 10 minutes
        yield user.save();
        const link = `${process.env.APP_URL}/reset-password/${resetToken}`;
        const date = new Date();
        const content = (0, Email_1.FORGET_PASSWORD_EMAIL_CONTENT)(link, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
        yield (0, email_1.sendEmail)({
            to: email,
            subject: "Password Reset Request",
            html: content,
        });
        res.json({ message: "If an account with this email exists, you will receive a password reset link shortly" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/reset-password/:token", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_PASSWORD), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("token")
        .isString()
        .notEmpty()
        .isLength({ min: 64, max: 64 })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("password")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .bail()
        .isLength({ min: 8 })
        .withMessage("Password must be at least 8 characters long"),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token } = req.params;
        const { password } = req.body;
        const hashedToken = crypto_1.default.createHash("sha256").update(token).digest("hex");
        const user = yield User_1.default.findOne({
            reset_password_token: hashedToken,
            reset_password_expire: { $gt: Date.now() },
        });
        if (!user) {
            return res.status(400).json({ message: "Invalid or expired token" });
        }
        user.password = yield bcryptjs_1.default.hash(password, 10);
        user.reset_password_token = null;
        user.reset_password_expire = null;
        yield user.save();
        res.json({ message: "Password has been reset successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/user", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_USER), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const authHeader = req.header("authorization");
        if (authHeader && authHeader.startsWith("Bearer ")) {
            const jwt_token = authHeader.split("Bearer ")[1];
            if (!process.env.JWT_SECRET)
                return res.status(500).json({ message: "Internal server error" });
            const { user_id } = jsonwebtoken_1.default.verify(jwt_token, process.env.JWT_SECRET);
            if (!user_id)
                return res.status(400).json({ message: "Unsupported authentication for this endpoint" });
            const user = yield (0, User_2.getUser)({ user_id });
            if (!user)
                return res.status(404).json({ message: "User does not exist" });
            res.json(user);
        }
        else {
            return res.status(401).json({ message: "Unauthorized" });
        }
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/auth", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_TOKEN), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const logRequestDetails = (req) => {
        var _a;
        const ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress;
        const time = new Date().toISOString();
        const origin = req.headers.origin || "Unknown";
        const endpoint = req.originalUrl || req.url;
        const method = req.method;
        const userAgent = req.headers["user-agent"];
        const deviceIdFromCookie = (_a = req.cookies) === null || _a === void 0 ? void 0 : _a.deviceId;
        const deviceIdFromHeader = req.headers["device-id"];
        const authHeader = req.headers["authorization"];
        const apiKeyHeader = req.headers["qm-api-key"];
        const referrer = req.headers["referer"];
        const host = req.headers["host"];
        console.log(`\n🛰️ [Request Info - ${time}]`);
        console.log(`🔹 IP: ${ip}`);
        console.log(`🔹 Method: ${method}`);
        console.log(`🔹 Endpoint: ${endpoint}`);
        console.log(`🔹 Origin: ${origin}`);
        console.log(`🔹 Referrer: ${referrer}`);
        console.log(`🔹 Host: ${host}`);
        console.log(`🔹 User-Agent: ${userAgent}`);
        console.log(`🔹 Authorization: ${authHeader || "Not provided"}`);
        console.log(`🔹 API Key: ${apiKeyHeader || "Not provided"}`);
        console.log(`🔹 Device ID (Cookie): ${deviceIdFromCookie || "Not provided"}`);
        console.log(`🔹 Device ID (Header): ${deviceIdFromHeader || "Not provided"}`);
        console.log(`🔹 All Cookies:`, req.cookies);
        console.log("------------------------------------------------------------\n");
    };
    logRequestDetails(req);
    try {
        if (req.header("authorization")) {
            const authHeader = req.header("authorization");
            console.log("authHeader is", authHeader);
            if (!authHeader || !authHeader.startsWith("Basic "))
                return res.status(401).json({ message: "Authorization header must be Basic" });
            const base64Credentials = authHeader.split("Basic ")[1];
            const credentials = Buffer.from(base64Credentials, "base64").toString("utf-8");
            const [username, password] = credentials.split(":");
            if (!username || !password)
                return res.status(400).json({ message: "Username and password is requried" });
            const escapedUsername = (0, functions_1.escapeRegExp)(username);
            const user = yield User_1.default.findOne({
                $or: [
                    {
                        username: { $regex: new RegExp(`^${escapedUsername}$`, "i") },
                    },
                    {
                        email: { $regex: new RegExp(`^${escapedUsername}$`, "i") },
                    },
                ],
            });
            if (!user)
                return res.status(400).json({ message: "Invalid credentials" });
            const isMatch = yield bcryptjs_1.default.compare(password, user.password);
            if (!isMatch)
                return res.status(400).json({ message: "Invalid credentials" });
            const deviceId = req.cookies.deviceId;
            if (user.email_verification_enabled && !user.email_verified_device_ids.includes(deviceId)) {
                return res.status(302).json({
                    message: "Email verification required",
                });
            }
            const token_expiry = new Date(new Date().getTime() + 86400000).toISOString();
            if (!process.env.JWT_SECRET)
                return res.status(500).json({ message: "Internal server error" });
            const jwt_token = jsonwebtoken_1.default.sign({ user_id: user._id }, process.env.JWT_SECRET, { expiresIn: "7d" });
            if (user.jwt_tokens.length >= 10) {
                user.jwt_tokens.shift();
            }
            user.jwt_tokens.push(jwt_token);
            yield user.save();
            res.json({ jwt_token, expires: token_expiry });
        }
        else if (req.header("qm-api-key")) {
            const api_key = req.header("qm-api-key");
            const apiKey = yield ApiKey_1.default.findOne({ api_key });
            if (!apiKey)
                return res.status(401).json({ message: "API key is invalid" });
            if (apiKey.is_deleted)
                return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });
            if (apiKey.is_revoked)
                return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });
            const token_expiry = new Date(new Date().getTime() + 86400000).toISOString();
            if (!process.env.JWT_SECRET)
                return res.status(500).json({ message: "Internal server error" });
            const jwt_token = jsonwebtoken_1.default.sign({ api_key_id: apiKey._id }, process.env.JWT_SECRET, { expiresIn: "24h" });
            apiKey.jwt_token = jwt_token;
            yield apiKey.save();
            res.json({ jwt_token, expires: token_expiry });
        }
        else {
            return res.status(401).json({ message: "Auth is required" });
        }
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/sendEmailOTP", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.SEND_OTP), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("username")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { username } = req.body;
    if (!username)
        return res.status(400).json({ message: "Username is required" });
    const user = yield User_1.default.findOne({
        $or: [
            {
                username: { $regex: new RegExp(`^${username}$`, "i") },
            },
            {
                email: { $regex: new RegExp(`^${username}$`, "i") },
            },
        ],
    });
    if (!user)
        return res.status(400).json({ message: "Invalid credentials" });
    const email = user.email;
    if (!email)
        return res.status(400).json({ message: "Email is required" });
    try {
        const result = yield (0, otpService_1.sendOtp)(email, user.name, email_1.sendEmail);
        res.status(200).json(result);
    }
    catch (err) {
        res.status(500).json({ message: "Failed to send OTP" });
        console.error("Error sending OTP email:", err);
    }
}));
router.post("/emailOTPVerification", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.VERIFY_OTP), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("username")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("otp")
        .notEmpty()
        .withMessage((value, { path }) => `Field '${path}' cannot be empty`)
        .isInt({ min: 100000, max: 999999 })
        .withMessage((value, { path }) => `Field '${path}' must be a 6-digit integer`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { username, otp } = req.body;
    if (!username) {
        return res.status(400).json({ message: "Username is required" });
    }
    const user = yield User_1.default.findOne({
        $or: [{ username: { $regex: new RegExp(`^${username}$`, "i") } }, { email: { $regex: new RegExp(`^${username}$`, "i") } }],
    });
    if (!user) {
        return res.status(400).json({ message: "Invalid credentials" });
    }
    const email = user.email;
    if (!email) {
        return res.status(400).json({ message: "Email is required" });
    }
    const { valid, message } = (0, otpService_1.verifyOtp)(email, otp);
    if (!valid) {
        return res.status(400).json({ message });
    }
    const deviceId = req.cookies.deviceId;
    if (deviceId && !user.email_verified_device_ids.includes(deviceId)) {
        user.email_verified_device_ids.push(deviceId);
        yield user.save();
    }
    res.status(200).json({ message: "OTP Verified." });
}));
router.patch("/userEmailVerification", auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("email_verification_enabled")
        .isBoolean()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailVerificationEnabled = req.query.email_verification_enabled === "true";
        const user = yield User_1.default.findById(req.user._id);
        if (!user)
            return res.status(404).json({ message: "User does not exist" });
        if (emailVerificationEnabled && !user.email) {
            return res.status(400).json({ message: "Email is required" });
        }
        user.email_verification_enabled = emailVerificationEnabled;
        yield user.save();
        res.status(200).json({ message: "User email verification status updated successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/role", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_USER_ROLE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("role_id")
        .custom(functions_1.isIntStrict)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user)
            return res.status(401).json({ message: "Unauthorized" });
        const { id: user_id } = req.params;
        const { role_id } = req.body;
        const user = yield User_1.default.findOne({ _id: user_id });
        if (!user)
            return res.status(404).json({ message: "User does not exist" });
        const role = yield Role_1.default.findOne({ role_id: role_id });
        if (!role)
            return res.status(404).json({ message: "Role does not exist" });
        if (req.user.role_id === user.role_id)
            return res.status(403).json({ message: "Not allowed to edit role for user having same role as you" });
        const reqUserRole = yield Role_1.default.findOne({ role_id: req.user.role_id });
        const targetUserRole = yield Role_1.default.findOne({ role_id: user.role_id });
        if (!reqUserRole || !targetUserRole) {
            return res.status(404).json({ message: "Role not found" });
        }
        if (reqUserRole.hierarchy_number >= targetUserRole.hierarchy_number) {
            return res.status(403).json({ message: "Not allowed to edit role for user having role above you" });
        }
        if (!req.user.organization.is_internal) {
            if (req.user.organization_id.toString() !== user.organization_id.toString()) {
                return res.status(403).json({ message: "Not allowed to edit role of user from different organization" });
            }
        }
        const userOrganization = yield Organization_1.default.findOne({ _id: user.organization_id });
        if (!userOrganization)
            return res.status(404).json({ message: "User organization does not exist" });
        if (userOrganization.is_miscellaneous && !role.denied_permissions.includes(permissions_1.permissions.manageUsers)) {
            return res
                .status(403)
                .json({ message: "Miscellaneous organization user are not allowed to assign a role which have manage user permission" });
        }
        user.role_id = role.role_id;
        yield user.save();
        res.json({ message: "User role has been updated" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/updateSettings", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_USER_SETTINGS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("date_time_format")
        .isString()
        .optional()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("use_MGRS")
        .isBoolean()
        .optional()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("home_port_filter_mode")
        .isString()
        .optional()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const dateTimeFormat = req.query.date_time_format;
        const useMGRS = req.query.use_MGRS;
        const home_port_filter_mode = req.query.home_port_filter_mode;
        if (!dateTimeFormat && !useMGRS && !home_port_filter_mode)
            return res.status(400).json({ message: "Date time format or use MGRS or home_port_filter_mode is required" });
        const user = yield User_1.default.findById(req.user._id);
        if (!user)
            return res.status(404).json({ message: "User does not exist" });
        if (dateTimeFormat) {
            user.date_time_format = dateTimeFormat;
        }
        if (useMGRS) {
            user.use_MGRS = useMGRS === "true";
        }
        if (home_port_filter_mode) {
            user.home_port_filter_mode = home_port_filter_mode;
        }
        yield user.save();
        res.status(200).json({ message: "User settings updated successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/allowedVessels", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_USER_ALLOWED_VESSELS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels")
        .isArray()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels.*")
        .custom(mongoose_1.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { allowed_vessels } = req.body;
        const user = yield User_1.default.findOne({ _id: id, is_deleted: false });
        if (!user)
            return res.status(404).json({ message: "User not found" });
        const inActiveVessels = yield Vessel_1.default.find({ _id: { $in: allowed_vessels }, is_active: false });
        if (inActiveVessels.length > 0) {
            return res.status(400).json({ message: "Cannot assign inactive vessels to user" });
        }
        user.allowed_vessels = allowed_vessels;
        yield user.save();
        res.json({ message: "User allowed vessels updated successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/organization", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_USER_ORGANIZATION), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers, permissions_1.permissions.manageOrganizations]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("organization_id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user)
            return res.status(401).json({ message: "Unauthorized" });
        const { id: user_id } = req.params;
        const { organization_id } = req.body;
        const user = yield User_1.default.findOne({ _id: user_id });
        if (!user)
            return res.status(404).json({ message: "User does not exist" });
        const userRole = yield Role_1.default.findOne({ role_id: user.role_id });
        const reqUserRole = yield Role_1.default.findOne({ role_id: req.user.role_id });
        const organization = yield Organization_1.default.findOne({ _id: organization_id });
        if (!organization)
            return res.status(404).json({ message: "Organization does not exist" });
        if (req.user._id.toString() === user_id.toString()) {
            return res.status(403).json({ message: "Not allowed to edit your own organization" });
        }
        if (!reqUserRole || !userRole) {
            return res.status(404).json({ message: "Role not found" });
        }
        if (reqUserRole.hierarchy_number >= userRole.hierarchy_number) {
            return res.status(403).json({ message: "Not allowed to edit organization for user having role above you" });
        }
        if (organization.is_miscellaneous && !userRole.denied_permissions.includes(permissions_1.permissions.manageUsers)) {
            return res.status(403).json({ message: "Miscellaneous organization cannot be assign to user which have manage user permission" });
        }
        user.organization_id = organization._id;
        yield user.save();
        res.json({ message: "User organization has been updated" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_USER), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageUsers]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield User_1.default.findById(req.params.id);
        if (!user)
            return res.status(404).json({ message: `User does not exist` });
        if (!user.deletable)
            return res.status(400).json({ message: `User cannot be deleted` });
        user.username = crypto_1.default.randomUUID();
        user.email = `${crypto_1.default.randomUUID()}@random.com`;
        user.is_deleted = true;
        yield user.save();
        return res.json({ message: `User has been deleted` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: Authentication endpoint
 * components:
 *   securitySchemes:
 *     basicAuth:
 *       type: http
 *       scheme: basic
 *     apiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: qm-api-key
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   schemas:
 *     AuthToken:
 *       type: object
 *       properties:
 *         jwt_token:
 *           type: string
 *           description: The JWT token
 *           example: eyJhbGciOiJIUzI1Ni....eyJzdWIiOiIxMjM0NTY3ODk....SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJ...
 *         expires:
 *           type: string
 *           format: date-time
 *           description: Expiration date and time of the token
 *           example: '2023-09-25T10:20:30Z'
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the user
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: The user's full name
 *           example: John Doe
 *         email:
 *           type: string
 *           description: The user's email address
 *           example: <EMAIL>
 *         username:
 *           type: string
 *           description: The unique username for the user
 *           example: johndoe123
 *         role_id:
 *           type: number
 *           description: The role ID of the user
 *           example: 1
 *         allowed_vessels:
 *           type: array
 *           description: The vessels that the user is allowed to access
 *           example: ["67942a74a7f838634a00190a","67942a74a7f838634a00190b"]
 *         email_verification_enabled:
 *           type: boolean
 *           description: Whether the user's email is verified
 *           example: false
 *         email_verified_device_ids:
 *           type: array
 *           description: The devices that the user has verified their email on
 *           items:
 *             type: string
 *             example: "762486b8-d22b-4813-b488-a4242017a47b"
 *         deletable:
 *           type: boolean
 *           description: Whether the user can be deleted
 *           example: false
 *         is_deleted:
 *           type: boolean
 *           description: Whether the user has been soft deleted
 *           example: false
 *         creation_timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was created
 *           example: '2023-09-25T10:20:30Z'
 */
/**
 * @swagger
 * /users/auth:
 *   get:
 *     summary: Fetch JWT token
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Auth]
 *     security:
 *       - basicAuth: []
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: JWT token and expiration date
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               $ref: '#/components/schemas/AuthToken'
 *       400:
 *         description: Invalid credentials
 *       302:
 *         description: Email verification required
 *       500:
 *         description: Server error
 */
