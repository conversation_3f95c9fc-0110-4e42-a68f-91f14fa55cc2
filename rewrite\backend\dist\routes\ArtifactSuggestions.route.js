"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const natural_1 = __importDefault(require("natural"));
const spellingCorrector_1 = __importDefault(require("../modules/spellingCorrector"));
const ArtifactSuggestion_1 = __importDefault(require("../models/ArtifactSuggestion"));
const ArtifactSynonym_1 = __importDefault(require("../models/ArtifactSynonym"));
const functions_1 = require("../utils/functions");
const router = express_1.default.Router();
const wordnet = new natural_1.default.WordNet();
const spelling = new spellingCorrector_1.default();
spelling.loadDictionary();
let lastSynonymCheckedAt = null;
const synonymCache = { synonyms: null };
const synonymCachePeriodMs = 300000; // 5 minutes
const SUGGESTION_LIMIT = 10;
const getSynonyms = (word) => {
    return new Promise((resolve) => {
        wordnet.lookup(word, (results) => {
            const synonyms = new Set();
            results.forEach((result) => {
                result.synonyms.forEach((syn) => {
                    if (syn.toLowerCase() !== word.toLowerCase()) {
                        synonyms.add(syn);
                    }
                });
            });
            resolve(Array.from(synonyms).slice(0, 5));
        });
    });
};
function rankSuggestions(suggestionsArr, suggestionDocs) {
    const docMap = new Map(suggestionDocs.map((s) => [s.search, s]));
    let ranked = suggestionsArr
        .map((s) => {
        const doc = docMap.get(s);
        return {
            suggestion: s,
            click: doc ? doc.click : 0,
        };
    })
        .sort((a, b) => {
        const aScore = a.click;
        const bScore = b.click;
        if (bScore !== aScore)
            return bScore - aScore;
        return a.suggestion.localeCompare(b.suggestion);
    })
        .map((r) => r.suggestion);
    return ranked;
}
function generateCombinations(wordAlternatives, limit = 20) {
    const out = [];
    function combine(arr, prefix = []) {
        if (out.length >= limit)
            return;
        if (arr.length === 0) {
            out.push(prefix.join(" "));
            return;
        }
        for (let i = 0; i < arr[0].length; i++) {
            combine(arr.slice(1), [...prefix, arr[0][i]]);
            if (out.length >= limit)
                break;
        }
    }
    combine(wordAlternatives);
    return out;
}
const getSynonymMap = () => __awaiter(void 0, void 0, void 0, function* () {
    if (!lastSynonymCheckedAt || !synonymCache.synonyms || Date.now() - lastSynonymCheckedAt.getTime() >= synonymCachePeriodMs) {
        const allSynonyms = (yield ArtifactSynonym_1.default.find({}, { word: 1, synonyms: 1 }));
        const synonymMap = {};
        for (const syn of allSynonyms) {
            if (Array.isArray(syn.synonyms)) {
                for (const s of syn.synonyms) {
                    synonymMap[s.toLowerCase()] = syn.word;
                }
            }
            synonymMap[syn.word.toLowerCase()] = syn.word;
        }
        synonymCache.synonyms = synonymMap;
        lastSynonymCheckedAt = new Date();
    }
    return synonymCache.synonyms;
});
function normalizeSuggestionsArray(arr, synonymMap) {
    const normalized = arr.map((str) => str
        .split(/\s+/)
        .map((word) => (synonymMap[word.toLowerCase()] || word).toLowerCase())
        .join(" ")
        .trim());
    return Array.from(new Set(normalized.filter(Boolean)));
}
router.post("/", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // 1. Validate and clean the query
        const { query } = req.body;
        if (!query || typeof query !== "string") {
            return res.status(400).json({ message: "Query is required" });
        }
        const cleanQuery = (0, functions_1.cleanSuggestion)(query);
        const synonymMap = yield getSynonymMap();
        // 2. For each word, get alternatives: synonym, spell-correct, or WordNet synonyms
        const words = cleanQuery.split(/\s+/);
        const wordAlternatives = yield Promise.all(words.map((word) => __awaiter(void 0, void 0, void 0, function* () {
            const lower = word.toLowerCase();
            if (synonymMap[lower])
                return [synonymMap[lower]];
            if (word && word.split(/\s+/).length === 1) {
                const correction = spelling.correct(word);
                if (correction && correction !== word) {
                    return [correction];
                }
            }
            const syns = yield getSynonyms(word);
            return [word, ...syns];
        })));
        let generated = generateCombinations(wordAlternatives, 20).map(functions_1.cleanSuggestion).filter(Boolean);
        // 3. Find DB suggestions (partial match, case-insensitive)
        const regex = new RegExp(words.map((w) => w.toLowerCase()).join("|"), "i");
        const dbSuggestions = (yield ArtifactSuggestion_1.default.find({ search: { $regex: regex } }));
        const dbNorm = dbSuggestions.map((s) => s.search);
        // 4. Merge, normalize, and dedupe all suggestions
        let suggestions = normalizeSuggestionsArray([...generated, ...dbNorm], synonymMap);
        // 5. Rank suggestions
        suggestions = rankSuggestions(suggestions, dbSuggestions);
        // 6. Always show the normalized/corrected query at the top
        const normalizedQuery = wordAlternatives
            .map((a) => a[0])
            .join(" ")
            .toLowerCase();
        suggestions = [normalizedQuery, ...suggestions.filter((s) => s.toLowerCase() !== normalizedQuery)];
        // 7. Limit suggestions before updating impressions and returning
        suggestions = suggestions.map((s) => s.toLowerCase()).slice(0, SUGGESTION_LIMIT);
        const limitedDbNorm = suggestions.filter((s) => dbNorm.map((x) => x.toLowerCase()).includes(s));
        // 8. Update impressions for found suggestions only
        if (limitedDbNorm.length > 0) {
            ArtifactSuggestion_1.default.updateMany({ search: { $in: limitedDbNorm } }, { $inc: { impressions: 1 } }).catch(console.error);
        }
        return res.json({ suggestions });
    }
    catch (_a) {
        return res.status(500).json({ message: "Internal server error" });
    }
}));
setInterval(() => {
    (0, functions_1.consoleLogObjectSize)(synonymCache, "artifactSuggestions.synonymCache");
}, 60000); // 1 minute
exports.default = router;
