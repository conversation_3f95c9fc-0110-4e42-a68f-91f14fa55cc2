import React, { useEffect, useRef, useState, useCallback } from "react";
import { GoogleMap } from "@react-google-maps/api";
import { Skeleton } from "@mui/material";
import dayjs from "dayjs";
import { useApp } from "../hooks/AppHook";
import { displayCoordinates, defaultValues, userValues, simplifyTimezone } from "../utils";
import gps_socket from "../gps_socket";
import { useUser } from "../hooks/UserHook.jsx";
import useVesselInfo from "../hooks/VesselInfoHook.jsx";

const InsetMap = ({ vessel = {}, newCoordinate = {}, initialZoom, streamMode, allVesselCoordinates = {}, onSelectVessel, artifactIndicator }) => {
    const { google } = useApp();
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();
    const [map, setMap] = useState(null);
    const [coordinate, setCoordinate] = useState();
    // eslint-disable-next-line no-unused-vars
    const [zoom, setZoom] = useState(initialZoom || defaultValues.zoom);
    const [center, setCenter] = useState({ lat: 0, lng: 0 });
    const markerRef = useRef(null);
    const infoWindowRef = useRef(null);
    const centerSetRef = useRef(false);
    const coordinateRef = useRef(coordinate);
    const allMarkersRef = useRef({});

    const onLoad = useCallback((mapInstance) => {
        setMap(mapInstance);
    }, []);

    const onUnmount = useCallback(() => {
        if (markerRef.current) {
            google.maps.event.clearListeners(markerRef.current, "mouseover");
            google.maps.event.clearListeners(markerRef.current, "mouseout");
        }
        // Clean up all vessel markers
        Object.values(allMarkersRef.current).forEach((marker) => {
            if (marker) {
                google.maps.event.clearListeners(marker, "mouseover");
                google.maps.event.clearListeners(marker, "mouseout");
                marker.setMap(null);
            }
        });
        allMarkersRef.current = {};

        setMap(null);
    }, [google]);

    useEffect(() => {
        if (!vessel.id && !newCoordinate) return;
        // Set initial coordinate if available
        if (newCoordinate && (newCoordinate.lat || newCoordinate.latitude)) {
            const coord = {
                ...newCoordinate,
                lat: newCoordinate.lat || newCoordinate.latitude,
                lng: newCoordinate.lng || newCoordinate.longitude,
            };
            setCoordinate(coord);
            if (!centerSetRef.current) {
                setCenter(coord);
                centerSetRef.current = true;
            }
        }

        // Only listen to GPS socket in live mode, not in replay mode
        if (streamMode !== "ON_DEMAND") {
            const updateCoordinate = (data) => {
                if (!data.latitude || !data.vesselName) return;

                // Check if this update is for the selected vessel
                if (data.vesselName === vessel.id) {
                    const newCoordinates = {
                        ...data,
                        lat: data.latitude,
                        lng: data.longitude,
                        timestamp: data.timestamp,
                    };

                    // Update the marker position directly in allMarkersRef without re-rendering
                    const selectedVesselId = vessel.vesselId || vessel.id;
                    const marker = allMarkersRef.current[selectedVesselId];

                    if (marker && map) {
                        marker.setPosition(newCoordinates);
                        marker.setZIndex(1000); // Set high z-index to appear above all other markers

                        // Update info window content if it exists
                        if (marker.infoWindow) {
                            const content = `
                                <div style="align-items: center; padding: 10px; font-size: 11px;">
                                    <strong>Location:</strong> ${displayCoordinates([newCoordinates.lng, newCoordinates.lat], !!user?.use_MGRS)}<br/>
                                    <strong>Time:</strong> ${dayjs(newCoordinates.timestamp).tz(vessel.timezone).format(userValues.dateTimeFormat(user))} ${vessel.timezoneFormat} <br/>
                                    <style>
                                        .gm-style-iw-chr {
                                            display: none !important;
                                        }
                                        .gm-style-iw-tc {
                                            display: none !important;
                                        }
                                        .gm-style .gm-style-iw-c  {
                                            background-color: #343B44 !important;
                                            outline: none;
                                            padding: 0;
                                        }
                                        .gm-style .gm-style-iw-d {
                                            overflow:auto !important;
                                        }
                                        p {
                                            margin: 0;
                                            color:white
                                        }
                                        strong {
                                            color:white
                                        }
                                    </style>
                                </div>
                            `;
                            marker.infoWindow.setContent(content);
                        }
                    }

                    if (!centerSetRef.current && newCoordinates) {
                        setCenter(newCoordinates);
                        centerSetRef.current = true;
                    }
                }
            };

            gps_socket.on(vessel.id + "/gps", updateCoordinate);
            return () => gps_socket.off(vessel.id + "/gps", updateCoordinate);
        }
    }, [vessel, streamMode, map, user, vessel.timezone, vessel.timezoneFormat, newCoordinate]);

    // Handle all vessel coordinates in live mode
    useEffect(() => {
        if (streamMode === "LIVE" && Object.keys(allVesselCoordinates).length > 0) {
            // Set center to first available coordinate if not set
            if (!centerSetRef.current) {
                const firstCoordinate = Object.values(allVesselCoordinates)[0];
                if (firstCoordinate) {
                    setCenter(firstCoordinate);
                    centerSetRef.current = true;
                }
            }
        }
    }, [allVesselCoordinates, streamMode]);

    const setMarker = useCallback(
        (coordinate) => {
            if (!coordinate || !coordinate.lat || !coordinate.lng || !infoWindowRef.current) return;
            const content = `
            <div style="align-items: center; padding: 10px; font-size: 11px;">
                <strong>Location:</strong> ${displayCoordinates([coordinate.lng, coordinate.lat], !!user?.use_MGRS)}<br/>
                <strong>Time:</strong> ${dayjs(coordinate.timestamp).tz(vessel.timezone).format(userValues.dateTimeFormat(user))} ${vessel.timezoneFormat} <br/>
                <style>
                    .gm-style-iw-chr {
                        display: none !important; /* Hide the close button */;
                    }
                    .gm-style-iw-tc {
                        display: none !important; /* Hide the close button */;
                    }
                    .gm-style .gm-style-iw-c  {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow:auto !important;
                    }
                    p {
                        margin: 0;
                        color:white
                    }
                    strong {
                        color:white
                    }
                </style>
            </div>
        `;

            if (!markerRef.current) {
                markerRef.current = new google.maps.Marker({
                    position: coordinate,
                    map: map,
                    icon: {
                        path: defaultValues.icons.location,
                        fillColor: "#FF0000",
                        fillOpacity: 1,
                        strokeColor: "#000000",
                        strokeOpacity: 1,
                        scaledSize: new google.maps.Size(32, 32),
                        anchor: new google.maps.Point(16, 16),
                        scale: 1,
                    },
                });

                infoWindowRef.current.setContent(content);

                const handleMouseOver = () => infoWindowRef.current.open(map, markerRef.current);
                const handleMouseOut = () => infoWindowRef.current.close();

                markerRef.current.addListener("mouseover", handleMouseOver);
                markerRef.current.addListener("mouseout", handleMouseOut);
            } else {
                markerRef.current.setPosition(coordinate);
                markerRef.current.setMap(map);
                infoWindowRef.current.setContent(content);
            }
        },
        [map, google, vessel.timezone, vessel.timezoneFormat, user],
    );

    const setAllVesselMarkers = useCallback(() => {
        if (!map || streamMode !== "LIVE") return;

        // Clear existing markers
        Object.values(allMarkersRef.current).forEach((marker) => {
            if (marker) {
                marker.setMap(null);
            }
        });
        allMarkersRef.current = {};

        // Create markers for all vessels
        Object.keys(allVesselCoordinates).forEach((vesselId) => {
            const coordinate = allVesselCoordinates[vesselId];
            if (!coordinate || !coordinate.lat || !coordinate.lng) return;

            const vesselFound = vesselInfo.find((v) => v.vessel_id === vesselId);
            const vesselName = vesselFound ? vesselFound.name : `Vessel ${vesselId}`;
            const vesselTimezone = vesselFound ? vesselFound.timezone : "UTC";
            const isSelected = vessel && vesselFound && vessel.vesselId === vesselFound.vessel_id;
            const isArtifactDetected = vesselId in artifactIndicator;
            const isLive = vesselFound && vesselFound.is_live;
            console.log("Vessel activity:", vesselId in artifactIndicator);
            const content = `
                <div style="align-items: center; padding: 10px; font-size: 11px;">
                    <strong>Vessel:</strong> ${vesselName}<br/>
                    <strong>Location:</strong> ${displayCoordinates([coordinate.lng, coordinate.lat], !!user?.use_MGRS)}<br/>
                    <strong>Time:</strong>  ${vesselTimezone ? dayjs(coordinate.timestamp).tz(vesselTimezone).format(userValues.dateTimeFormat(user)) + " " + simplifyTimezone(vesselTimezone) : dayjs(coordinate.timestamp).format(userValues.dateTimeFormat(user))} <br/>
                    <style>
                        .gm-style-iw-chr {
                            display: none !important;
                        }
                        .gm-style-iw-tc {
                            display: none !important;
                        }
                        .gm-style .gm-style-iw-c  {
                            background-color: #343B44 !important;
                            outline: none;
                            padding: 0;
                        }
                        .gm-style .gm-style-iw-d {
                            overflow:auto !important;
                        }
                        p {
                            margin: 0;
                            color:white
                        }
                        strong {
                            color:white
                        }
                    </style>
                </div>
            `;

            const marker = new google.maps.Marker({
                position: coordinate,
                map: map,
                zIndex: isSelected ? 1000 : 1,
                icon: {
                    path: defaultValues.icons.location,
                    fillColor: isSelected ? "#FF0000" : isArtifactDetected ? "lightgreen" : isLive ? "green" : "gray",
                    fillOpacity: 1,
                    strokeColor: "#000000",
                    strokeOpacity: 1,
                    scaledSize: new google.maps.Size(32, 32),
                    anchor: new google.maps.Point(16, 16),
                    scale: 1,
                },
            });

            const infoWindow = new google.maps.InfoWindow({
                content: content,
                disableAutoPan: true,
            });

            const handleMouseOver = () => infoWindow.open(map, marker);
            const handleMouseOut = () => infoWindow.close();
            const handleClick = () => {
                if (typeof onSelectVessel === "function") {
                    onSelectVessel(vesselId);
                }
            };

            marker.addListener("mouseover", handleMouseOver);
            marker.addListener("mouseout", handleMouseOut);
            marker.addListener("click", handleClick);

            // Store the infoWindow reference with the marker for later updates
            marker.infoWindow = infoWindow;
            allMarkersRef.current[vesselId] = marker;
        });
    }, [map, google, streamMode, allVesselCoordinates, vesselInfo, vessel?.vesselId, user, onSelectVessel]);
    useEffect(() => {
        infoWindowRef.current = new google.maps.InfoWindow({ disableAutoPan: true });
    }, [google]);

    // Handle all vessel markers in live mode
    useEffect(() => {
        if (!map) return;

        if (streamMode === "LIVE" && Object.keys(allVesselCoordinates).length > 0) {
            setAllVesselMarkers();
        } else if (streamMode === "ON_DEMAND" && coordinate) {
            setMarker(coordinate);
        }
    }, [map, allVesselCoordinates, coordinate, streamMode, setAllVesselMarkers, setMarker]);

    // Separate useEffect to handle newCoordinate changes (for replay mode)
    useEffect(() => {
        if (newCoordinate && (newCoordinate.lat || newCoordinate.latitude)) {
            const coord = {
                ...newCoordinate,
                lat: newCoordinate.lat || newCoordinate.latitude,
                lng: newCoordinate.lng || newCoordinate.longitude,
            };
            setCoordinate(coord);

            // Update center if it's the first coordinate or in replay mode
            if (!centerSetRef.current || streamMode === "ON_DEMAND") {
                setCenter(coord);
                centerSetRef.current = true;
            }
        }
    }, [newCoordinate, streamMode]);

    useEffect(() => {
        const intervalId = setInterval(
            () => {
                if (!coordinateRef.current) return;
                setCenter(coordinateRef.current);
            },
            5 * 60 * 1000,
        ); // 5 minutes

        return () => clearInterval(intervalId);
    }, []);

    return (
        <div data-testid="inset-map-container" style={{ color: "#FFFFFF", width: "100%", height: "100%" }}>
            <GoogleMap
                data-testid="google-map-component"
                mapContainerStyle={{
                    width: "100%",
                    height: "100%",
                }}
                center={center || { lat: 0, lng: 0 }}
                zoom={zoom}
                // onZoomChanged={() => map && setZoom && setZoom(map?.getZoom())}
                onLoad={onLoad}
                onUnmount={onUnmount}
                options={{
                    disableDefaultUI: true,
                    zoomControl: false,
                    streetViewControl: false,
                    mapTypeControl: false,
                    fullscreenControl: false,
                    // maxZoom: 20,
                    // minZoom: 1,
                }}
            />
        </div>
    );
};

export default React.memo(InsetMap);
