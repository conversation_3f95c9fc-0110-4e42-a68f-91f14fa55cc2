import mongoose from "mongoose";
import Permission from "./Permission";
import db from "../modules/db";
import { IRole } from "src/interfaces/Role";
import ioEmitter from "../modules/ioEmitter";

const roleSchema = new mongoose.Schema({
    role_id: { type: Number, required: true },
    role_name: { type: String, required: true, unique: true },
    denied_permissions: { type: [Number], required: true },
    deletable: { type: Boolean, required: true, default: true },
    editable: { type: Boolean, required: true, default: true },
    hierarchy_number: { type: Number, required: true },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
    },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

// Pre-save hook to auto-increment role_id
roleSchema.pre("save", async function (next) {
    if (this.isNew) {
        // Only increment for new documents
        try {
            // Find the document with the highest `role_id` and increment by 1
            const latest = await this.constructor().findOne().sort({ role_id: -1 });
            this.role_id = latest ? latest.role_id + 1 : 1;
            this.hierarchy_number = this.role_id;
            this.denied_permissions = await Permission.find().then((result) => result.map((p) => p.permission_id));
            next();
        } catch (err) {
            // next(err);
            console.error(err);
        }
    } else {
        next();
    }
});

roleSchema.post("save", (role) => {
    ioEmitter.emit("notifyAll", { name: `roles/changed`, data: role.toObject() });
});

roleSchema.post("findOneAndDelete", (role) => {
    ioEmitter.emit("notifyAll", { name: `roles/changed`, data: role.toObject() });
});

// function emitChangedEvent(role) {
//     ioEmitter.emit('notifyAll', { name: `roles/changed`, data: role.toObject() });
// }

const Role = db.qm.model<IRole>("Role", roleSchema);

export default Role;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
