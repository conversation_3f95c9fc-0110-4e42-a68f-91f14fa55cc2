"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const ApiKey_1 = __importDefault(require("../models/ApiKey"));
const User_1 = require("../queries/User");
const isAuthenticated = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    const authHeader = req.header("authorization");
    if (!authHeader)
        return res.status(401).json({ message: "Unauthorized" });
    if (!authHeader.startsWith("Bearer "))
        return res.status(401).json({ message: "Token type must be Bearer" });
    const token = authHeader.split("Bearer ")[1];
    if (!token)
        return res.status(401).json({ message: "Token is invalid" });
    try {
        const { user_id, api_key_id } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        if (user_id) {
            const user = yield (0, User_1.getUser)({ user_id, includeUnprojected: true });
            if (user.is_deleted)
                return res.status(401).json({ message: "Your account has been deleted." });
            if (!user.jwt_tokens.includes(token))
                return res.status(401).json({ message: "Token is not available." });
            req.user = user;
            console.log(`[${req.method}: ${req.originalUrl}] user: ${JSON.stringify({ _id: user._id, name: user.name, email: user.email })}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `);
            next();
        }
        else if (api_key_id) {
            const apiKey = yield ApiKey_1.default.findOne({ _id: api_key_id });
            if (!apiKey)
                return res.status(401).json({ message: "API key is invalid" });
            if (apiKey.is_deleted)
                return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });
            if (apiKey.is_revoked)
                return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });
            if (!apiKey.allowed_endpoints.includes(req._endpoint_id))
                return res.status(403).json({ message: "You cannot access this resource" });
            apiKey.requests += 1;
            apiKey.requests_endpoints[req._endpoint_id] = (apiKey.requests_endpoints[req._endpoint_id] || 0) + 1;
            apiKey.last_used = new Date();
            apiKey.markModified("requests_endpoints");
            yield apiKey.save();
            req.api_key = apiKey.toObject();
            console.log(`[${req.method}: ${req.originalUrl}] apiKey: ${apiKey._id}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `);
            next();
        }
        else
            throw new Error("JWT token returned unexpected data");
    }
    catch (err) {
        console.error(err);
        if (err instanceof jsonwebtoken_1.default.JsonWebTokenError)
            res.status(401).json({ message: "Token is invalid" });
        else if (err instanceof Error)
            res.status(500).json({ message: `Unexpected error occured: ${err.message}` });
        else
            res.status(500).json({ message: `Unexpected error occured: ${err}` });
    }
});
exports.default = isAuthenticated;
