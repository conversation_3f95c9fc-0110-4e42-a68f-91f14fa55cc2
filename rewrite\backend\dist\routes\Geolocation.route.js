"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Geolocation_1 = __importDefault(require("../models/Geolocation"));
const geolocation_1 = require("../modules/geolocation");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_LOCATION), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("lat").notEmpty().withMessage("lat is required").isFloat().withMessage("Latitude must be a float"),
    (0, express_validator_1.query)("lng").notEmpty().withMessage("lng is required").isFloat().withMessage("Longitude must be a float"),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { lat, lng } = req.query;
    try {
        const radiusInKilometers = 30;
        const radiusInRadians = radiusInKilometers / 6371;
        const existingGeolocation = yield Geolocation_1.default.findOne({
            location: {
                $geoWithin: {
                    $centerSphere: [[parseFloat(lng), parseFloat(lat)], radiusInRadians],
                },
            },
        });
        if (existingGeolocation) {
            return res.status(200).json({
                lat,
                lng,
                name: existingGeolocation.name,
            });
        }
        let locationName;
        try {
            locationName = yield (0, geolocation_1.fetchGeolocation)(parseFloat(lat), parseFloat(lng));
        }
        catch (fetchError) {
            console.warn("Unable to fetch location name:", fetchError.message);
        }
        if (!locationName) {
            locationName = `Unknown Location`;
        }
        yield Geolocation_1.default.create({
            location: { type: "Point", coordinates: [parseFloat(lng), parseFloat(lat)] },
            name: locationName,
        });
        return res.status(200).json({
            lat,
            lng,
            name: locationName,
        });
    }
    catch (err) {
        return res.status(500).json({
            message: "Error retrieving geolocation",
            error: err.message,
        });
    }
}));
exports.default = router;
/**
 * [temporarily removed from swagger]
 * tags:
 *   - name: Geolocations
 *     description: Operations related to geolocation fetching and storing.
 * components:
 *   schemas:
 *     Geolocation:
 *       type: object
 *       properties:
 *         lat:
 *           type: number
 *           description: Latitude of the location.
 *           example: 37.7749
 *         lng:
 *           type: number
 *           description: Longitude of the location.
 *           example: -122.4194
 *         name:
 *           type: string
 *           description: The resolved name of the location.
 *           example: San Francisco, CA, USA
 */
/**
 * [temporarily removed from swagger]
 * /geolocations:
 *   get:
 *     summary: Retrieve location details based on latitude and longitude.
 *     description: Checks for geolocation in the database within a 30 km radius. If not found, fetches from Google Maps API and stores it in the database. If the location is not found, returns "Unknown Location".
 *     tags: [Geolocations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: lat
 *         schema:
 *           type: number
 *           example: 37.7749
 *         required: true
 *         description: Latitude of the location.
 *       - in: query
 *         name: lng
 *         schema:
 *           type: number
 *           example: -122.4194
 *         required: true
 *         description: Longitude of the location.
 *     responses:
 *       200:
 *         description: Geolocation details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Geolocation'
 *       400:
 *         description: Invalid request parameters.
 *       403:
 *         description: Forbidden - Error accessing Google Maps API.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message indicating forbidden access.
 *                   example: "Error retrieving geolocation"
 *                 error:
 *                   type: string
 *                   description: Detailed error message.
 *                   example: "Google Maps Geocoding Error: Request failed with status code 403"
 *       429:
 *         description: Too many requests.
 *       500:
 *         description: Internal server error.
 */
