import express, { Request, Response } from "express";
import rateLimit from "express-rate-limit";
import isAuthenticated from "../middlewares/auth";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import EmailDomains from "../models/EmailDomains";
import { validateError } from "../utils/functions";
import { IEmailDomain } from "../interfaces/Email";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALLOWED_EMAIL_DOMAINS), isAuthenticated, async (req: Request, res: Response) => {
    try {
        const emailDomains: IEmailDomain[] = await EmailDomains.find({});
        res.status(200).json(emailDomains);
    } catch (error) {
        validateError(error, res);
    }
});

export default router;
