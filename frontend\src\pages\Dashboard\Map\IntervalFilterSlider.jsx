import { Slider } from "@mui/material";
import { useEffect, useState } from "react";
import theme from "../../../theme";

const IntervalFilterSlider = ({ setInterval, interval }) => {

    const [temp, setTemp] = useState(interval);

    useEffect(() => {
        setTemp(interval);
    }, [interval]);
    // console.count("IntervalFilterSlider Rendered");
    return (

        <Slider
            valueLabelFormat={(v) => `${v}m`}
            valueLabelDisplay="auto"
            value={temp}
            onChange={(e, v) => setTemp(v)}
            onChangeCommitted={(e, v) => setInterval(temp)}
            min={1}
            max={60}
            sx={{
                padding: 0,
                color: theme.palette.custom.mediumGrey,
            }}
        />

    )
}
export default IntervalFilterSlider;