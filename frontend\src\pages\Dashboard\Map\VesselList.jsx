import { Grid, Checkbox, IconButton, CircularProgress, Typography, Accordion, AccordionSummary, AccordionDetails } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { GpsFixed, Warning } from "@mui/icons-material";
import { useEffect, useState, useMemo } from "react";
import { defaultValues } from "../../../utils";
import useGroupRegions from "../../../hooks/GroupRegionHook";
import { useApp } from "../../../hooks/AppHook";

const VesselList = ({
    vessels = [],
    selectedVessels,
    availableVessels,
    emptyVessels,
    errorsomeVessels,
    loadingVessels,
    handleVesselSelect,
    setFocusedVessel,
    focusedVessel,
}) => {
    const { devMode } = useApp();
    const { regions } = useGroupRegions();
    const [regionGroups, setRegionGroups] = useState([]);
    const [expandedGroup, setExpandedGroup] = useState(null);

    useEffect(() => {
        if (!vessels.length || !regions.length) return;
        const filteredRegionGroups = regions.filter((rg) => vessels.some((v) => v.region_group === rg._id));
        setRegionGroups(filteredRegionGroups);
    }, [vessels, regions]);

    const vesselsByRegionGroup = useMemo(() => {
        return vessels
            .filter((v) => v.region_group)
            .map((v) => ({
                ...v,
                region_group_object: regionGroups.find((rg) => rg._id === v.region_group),
            }))
            .sort((a, b) => {
                const groupA = a.region_group_object?.name?.toLowerCase() || "";
                const groupB = b.region_group_object?.name?.toLowerCase() || "";
                if (groupA < groupB) return -1;
                if (groupA > groupB) return 1;

                return (a.name?.toLowerCase() || a.id).localeCompare(b.name?.toLowerCase() || b.id);
            });
    }, [vessels, regionGroups]);

    const allGroupNames = useMemo(() => {
        return [...new Set(vesselsByRegionGroup.map((v) => v.region_group_object?.name || "Ungrouped"))];
    }, [vesselsByRegionGroup]);

    const allSelected = selectedVessels.length === vesselsByRegionGroup.length && vesselsByRegionGroup.length > 0;
    const someSelected = selectedVessels.length > 0 && selectedVessels.length < vesselsByRegionGroup.length;
    const anyVesselLoading = loadingVessels.length > 0;

    const handleSelectAll = (checked) => {
        console.log("handleSelectAll", checked);
        if (checked) {
            vesselsByRegionGroup.forEach((v) => {
                if (!selectedVessels.some((sel) => sel.id === v.id)) handleVesselSelect(v);
            });
        } else {
            selectedVessels.forEach((v) => handleVesselSelect(v));
        }
    };

    const handleAccordionToggle = (group) => {
        setExpandedGroup(expandedGroup === group ? null : group);
    };

    return (
        <Grid
            container
            flexDirection={"column"}
            sx={{
                p: 0,
                m: 0,
                color: "#fff",
                bgcolor: "transparent",
            }}
            className="map-step-2"
            minWidth={{ xs: "auto", md: "auto", lg: "420px" }}
        >
            {/* Select All */}
            {allGroupNames.length !== 0 && (
                <Grid item sx={{ borderRadius: 1 }}>
                    <Grid container alignItems="center" sx={{ height: 45, pl: 2 }}>
                        <Checkbox
                            checked={allSelected}
                            indeterminate={someSelected}
                            disabled={anyVesselLoading}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            sx={{ 
                                color: "#fff", 
                                p: 0, 
                                mr: 1,
                                "&.Mui-disabled": { color: "#666" }
                            }}
                        />
                        <Typography sx={{ fontSize: "20px", fontWeight: 500 }}>Select All</Typography>
                    </Grid>
                </Grid>
            )}
            {/* Vessel Groups */}
            <Grid item sx={{ borderRadius: 1 }}>
                {allGroupNames.length === 0 && <Typography sx={{ color: "#fff", textAlign: "center", p: 2 }}>No vessels found</Typography>}

                {allGroupNames.map((group) => {
                    const groupVessels = vesselsByRegionGroup.filter(
                        (v) => ((v.region_group_object && v.region_group_object.name) || "Ungrouped") === group,
                    );
                    const checkedLength = groupVessels.filter((v) => selectedVessels.some((sel) => sel.id === v.id)).length;
                    const totalLength = groupVessels.length;

                    const allGroupChecked = checkedLength === totalLength && totalLength > 0;
                    const someGroupChecked = checkedLength > 0 && checkedLength < totalLength;

                    const isExpanded = expandedGroup === group;
                    const groupTimezone =
                        groupVessels.length > 0 && groupVessels[0].region_group_object ? groupVessels[0].region_group_object.timezone : "UTC";

                    // Show loader if any vessel in this group is loading
                    const groupIsLoading = groupVessels.some((v) => loadingVessels.includes(v.id));
                    return (
                        <Accordion
                            key={group}
                            disableGutters
                            expanded={isExpanded}
                            onChange={() => handleAccordionToggle(group)}
                            sx={{ boxShadow: "none" }}
                        >
                            <AccordionSummary
                                expandIcon={
                                    groupIsLoading ? (
                                        <CircularProgress size={20} sx={{ color: "#bfc9e0" }} />
                                    ) : isExpanded ? (
                                        <RemoveIcon sx={{ color: "#bfc9e0" }} />
                                    ) : (
                                        <AddIcon sx={{ color: "#bfc9e0" }} />
                                    )
                                }
                                sx={{
                                    height: 44,
                                    pl: 2,
                                    backgroundColor: "#464F59",
                                }}
                            >
                                <Checkbox
                                    checked={allGroupChecked}
                                    indeterminate={someGroupChecked}
                                    disabled={groupIsLoading}
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            groupVessels.forEach((v) => {
                                                if (!selectedVessels.some((sel) => sel.id === v.id)) handleVesselSelect(v);
                                            });
                                        } else {
                                            groupVessels.forEach((v) => {
                                                if (selectedVessels.some((sel) => sel.id === v.id)) handleVesselSelect(v);
                                            });
                                        }
                                    }}
                                    sx={{ 
                                        color: "#fff", 
                                        p: 0, 
                                        mr: 1,
                                        "&.Mui-disabled": { color: "#666" }
                                    }}
                                    onClick={(e) => e.stopPropagation()}
                                />
                                <Typography fontWeight={600}>
                                    {group} (UTC {groupTimezone})
                                </Typography>
                            </AccordionSummary>

                            <AccordionDetails sx={{ p: 0, m: 0, maxHeight: "400px", overflowY: "auto" }}>
                                {groupVessels.map((option) => {
                                    const selected = selectedVessels.some((sel) => sel.id === option.id);
                                    // const color = defaultValues.polylineColors[option.id] || "#1976d2";
                                    // Determine the right-side status icon
                                    let statusIcon = null;
                                    if (loadingVessels.includes(option.id)) {
                                        statusIcon = <CircularProgress size={18} sx={{ color: "white" }} />;
                                    } else if (selected) {
                                        if (availableVessels.some((id) => id === option.id)) {
                                            statusIcon = (
                                                <IconButton
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setFocusedVessel(option.id);
                                                    }}
                                                    sx={{ width: 10, height: 10 }}
                                                >
                                                    <GpsFixed sx={{ color: option.id === focusedVessel ? "red" : "white" }} />
                                                </IconButton>
                                            );
                                        } else if (emptyVessels.some((id) => id === option.id)) {
                                            // statusIcon = <GpsNotFixed  sx={{ fontSize: 18, color: "#999" }} />
                                            statusIcon = "";
                                        } else if (errorsomeVessels.some((id) => id === option.id)) {
                                            statusIcon = <Warning sx={{ fontSize: 18, color: "yellow" }} />;
                                        }
                                    }

                                    return (
                                        <Grid
                                            key={option.id}
                                            container
                                            alignItems="center"
                                            justifyContent="space-between" // ensures right-side icon stays aligned
                                            sx={{
                                                p: "8px 16px",
                                                pl: 3,
                                                "&:hover": { bgcolor: "#464F59" },
                                                cursor: loadingVessels.includes(option.id) ? "not-allowed" : "pointer",
                                            }}
                                            onClick={() => !loadingVessels.includes(option.id) && handleVesselSelect(option)}
                                        >
                                            {/* Left side: checkbox + label */}
                                            <Grid item display="flex" alignItems="center">
                                                <Checkbox
                                                    checked={selected}
                                                    disabled={loadingVessels.includes(option.id)}
                                                    // onChange={() => handleVesselSelect(option)}
                                                    sx={{
                                                        // color,
                                                        color: "white",
                                                        p: 0,
                                                        mr: 1,
                                                        "&.Mui-checked": { color: defaultValues.polylineColors[option.id] || undefined },
                                                        "&.Mui-disabled": { color: "#666" },
                                                    }}
                                                />
                                                <Typography
                                                    sx={{
                                                        color: "white",
                                                        fontWeight: 500,
                                                        width: { xs: "180px", sm: "220px", md: "260px", lg: "300px" },
                                                        maxWidth: { xs: "180px", sm: "220px", md: "260px", lg: "300px" },
                                                        whiteSpace: "normal",
                                                        overflow: "hidden",
                                                        textOverflow: "ellipsis",
                                                        wordBreak: "break-word",
                                                        display: "block",
                                                    }}
                                                >
                                                    {devMode ? `${option.name} (${option.unit_id || ""})` : option.name}
                                                </Typography>
                                            </Grid>

                                            {/* Right side: status icon */}
                                            <Grid item display="flex" alignItems="center">
                                                {statusIcon}
                                            </Grid>
                                        </Grid>
                                    );
                                })}
                            </AccordionDetails>
                        </Accordion>
                    );
                })}
            </Grid>
        </Grid>
    );
};

export default VesselList;
