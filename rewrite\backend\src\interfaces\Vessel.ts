import mongoose from "mongoose";
import { IUser } from "./User";
import { IRegionGroup } from "./RegionGroup";
import { IPaginatedResponse } from "./Common";
import { ICoordinates } from "./VesselLocation";

export interface IUnitsHistory {
    unit_id: string;
    mount_timestamp: Date;
    unmount_timestamp: Date | null;
}

export interface IVessel {
    _id: mongoose.Types.ObjectId;
    name: string;
    thumbnail_s3_key: string | null;
    thumbnail_compressed_s3_key: string | null;
    unit_id: string | null;
    is_active: boolean;
    units_history: IUnitsHistory[];
    creation_timestamp: Date;
    created_by: mongoose.Types.ObjectId;
    region_group_id: mongoose.Types.ObjectId | undefined;
    home_port_location?: ICoordinates;
}

export interface IVesselWithUser extends IVesselWithUserDetails {
    regionGroup: Pick<IRegionGroup, "_id" | "name" | "timezone"> | null;
}

export interface IVesselPaginationResult extends IPaginatedResponse<IVesselWithUser> {
    vessels: IVesselWithUser[];
}

export interface IVesselWithUserDetails extends IVessel {
    user: Pick<IUser, "_id" | "name" | "email"> | null;
}
