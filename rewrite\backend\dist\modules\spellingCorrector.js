"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const nspell_1 = __importDefault(require("nspell"));
class SpellingCorrector {
    constructor() {
        this.spell = null;
        this.spell = null;
    }
    loadDictionary(affPath, dicPath) {
        try {
            const defaultAffPath = path_1.default.join(__dirname, "../assets/dictionaries/en_US.aff");
            const defaultDicPath = path_1.default.join(__dirname, "../assets/dictionaries/en_US.dic");
            const finalAffPath = affPath || defaultAffPath;
            const finalDicPath = dicPath || defaultDicPath;
            const aff = fs_1.default.readFileSync(finalAffPath, "utf-8");
            const dic = fs_1.default.readFileSync(finalDicPath, "utf-8");
            this.spell = (0, nspell_1.default)(aff, dic);
            console.log("SpellingCorrector dictionary loaded successfully");
        }
        catch (error) {
            console.error("Failed to load SpellingCorrector dictionary:", error);
            throw error;
        }
    }
    correct(word) {
        if (!this.spell) {
            console.warn("Dictionary not loaded, returning original word");
            return word;
        }
        try {
            // Check if word is spelled correctly
            if (this.spell.correct(word)) {
                return word;
            }
            // Get suggestions and return the first one, or original word if no suggestions
            const suggestions = this.spell.suggest(word);
            return suggestions && suggestions.length > 0 ? suggestions[0] : word;
        }
        catch (error) {
            console.error("Error in spelling correction:", error);
            return word;
        }
    }
}
exports.default = SpellingCorrector;
