"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const artifactFlaggedSchema = new mongoose_1.default.Schema({
    artifactId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        required: true,
        index: true,
    },
    flaggedBy: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});
artifactFlaggedSchema.index({ artifactId: 1, flaggedBy: 1 });
artifactFlaggedSchema.index({ flaggedAt: -1 });
artifactFlaggedSchema.post("save", (flag) => {
    ioEmitter_1.default.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});
artifactFlaggedSchema.post("findOneAndDelete", (flag) => {
    if ((flag === null || flag === void 0 ? void 0 : flag.deletedCount) > 0)
        return ioEmitter_1.default.emit("notifyAll", { name: `artifacts_flagged/changed` });
    ioEmitter_1.default.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});
artifactFlaggedSchema.post("deleteMany", (flag) => {
    if ((flag === null || flag === void 0 ? void 0 : flag.deletedCount) > 0)
        return ioEmitter_1.default.emit("notifyAll", { name: `artifacts_flagged/changed` });
    ioEmitter_1.default.emit("notifyAll", { name: `artifacts_flagged/changed`, data: flag.toObject() });
});
const ArtifactFlagged = db_1.default.qm.model("ArtifactFlagged", artifactFlaggedSchema, "artifacts_flagged");
exports.default = ArtifactFlagged;
