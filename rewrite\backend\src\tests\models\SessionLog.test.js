const mockingoose = require('mockingoose');
const ioEmitter = require('../../modules/ioEmitter');
const { sessionLogsList } = require('../data/Logs');
const SessionLog = require('../../models/SessionLog');

// Mock the ioEmitter
jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn(),
}));

describe('SessionLog Model', () => {
    it('should create the document', async () => {
        const doc = sessionLogsList[0]
        delete doc.user

        mockingoose(SessionLog).toReturn(doc, 'create');

        const result = await SessionLog.create({
            socket_id: 'test-id',
            type: 'disconnect',
            device: 'linux',
            browser: 'chrome',
            user_id: 'test-object-id'
        });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with findById', async () => {
        const doc = sessionLogsList[0]
        delete doc.user

        mockingoose(SessionLog).toReturn(doc, 'findOne');

        const result = await SessionLog.findById({ _id: doc._id });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with find', async () => {
        const docs = sessionLogsList
        docs.forEach((doc) => {
            delete doc.user
        })

        mockingoose(SessionLog).toReturn(docs, 'find');

        const result = await SessionLog.find();

        expect(result).toBeInstanceOf(Array)
        Object.keys(docs[0]).forEach(prop => {
            expect(result[0]).toHaveProperty(prop)
        })
    });

    it('should trigger post save hook and emit an event', async () => {
        const doc = sessionLogsList[0]
        delete doc.user

        // Mocking the save operation
        mockingoose(SessionLog).toReturn(doc, 'save');

        const sessionLog = new SessionLog(doc);
        await sessionLog.save(); // This should trigger the `post('save')` hook

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });

    it('should trigger post findOneAndDelete hook and emit an event', async () => {
        const doc = sessionLogsList[0]
        delete doc.user

        // Mocking the save operation
        mockingoose(SessionLog).toReturn(doc, 'findOneAndDelete');

        const deletedDoc = await SessionLog.findOneAndDelete({ _id: doc._id })

        SessionLog.emitChangedEvent(deletedDoc)

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });
});
