"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const Vessel_1 = __importDefault(require("../models/Vessel"));
const User_1 = __importDefault(require("../models/User"));
const NotificationAlert_1 = __importDefault(require("../models/NotificationAlert"));
const NotificationSummary_1 = __importDefault(require("../models/NotificationSummary"));
const InviteToken_1 = __importDefault(require("../models/InviteToken"));
const ApiKey_1 = __importDefault(require("../models/ApiKey"));
const awsS3_1 = require("../modules/awsS3");
const functions_1 = require("../utils/functions");
const sharp_1 = __importDefault(require("sharp"));
const RegionGroup_1 = __importDefault(require("../models/RegionGroup"));
class VesselService {
    compressImageToWebp(buffer) {
        return __awaiter(this, void 0, void 0, function* () {
            return (0, sharp_1.default)(buffer).resize({ width: 512 }).webp({ quality: 90 }).toBuffer();
        });
    }
    uploadVesselThumbnail(file) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                if (!file || !file.buffer) {
                    return null;
                }
                const vesselThumbnailsPath = "vessel-thumbnails";
                const timestamp = Date.now();
                const randomString = Math.random().toString(36).substring(2, 15);
                const baseName = file.originalname ? file.originalname.split(".")[0] : "thumbnail";
                const origExt = file.originalname ? file.originalname.split(".").pop() : "jpg";
                const origFileName = `${vesselThumbnailsPath}/${timestamp}-${randomString}-${baseName}.${origExt}`;
                const origS3Key = yield (0, awsS3_1.uploadFileToS3)(file, awsS3_1.s3Config.buckets.assets, origFileName, { ACL: "private" });
                // Save compressed .webp image
                const webpBuffer = yield this.compressImageToWebp(file.buffer);
                const compressedFileName = `${vesselThumbnailsPath}/${timestamp}-${randomString}-${baseName}.webp`;
                const compressedS3Key = yield (0, awsS3_1.uploadFileToS3)({
                    buffer: webpBuffer,
                    originalname: `${baseName}.webp`,
                    mimetype: "image/webp",
                }, awsS3_1.s3Config.buckets.assets, compressedFileName, { ACL: "private" });
                return {
                    thumbnail_s3_key: origS3Key,
                    thumbnail_compressed_s3_key: compressedS3Key,
                };
            }
            catch (error) {
                console.error("[VesselService.uploadVesselThumbnail] Error:", error);
                throw new Error("Failed to upload vessel thumbnail to S3");
            }
        });
    }
    find() {
        return __awaiter(this, arguments, void 0, function* (query = {}, projection) {
            try {
                const vessels = yield Vessel_1.default.find(query, projection);
                return vessels;
            }
            catch (error) {
                console.error("[VesselService.find] Error:", error);
                throw new Error("Failed to fetch vessels");
            }
        });
    }
    fetchPaginated(_a) {
        return __awaiter(this, arguments, void 0, function* ({ page = 1, limit = 10, search = "", }) {
            try {
                const skip = (page - 1) * limit;
                const escapedSearch = (0, functions_1.escapeRegExp)(search);
                const searchQuery = escapedSearch
                    ? {
                        $or: [{ name: { $regex: escapedSearch, $options: "i" } }, { unit_id: { $regex: escapedSearch, $options: "i" } }],
                    }
                    : {};
                const total = yield Vessel_1.default.countDocuments(searchQuery);
                const vessels = yield Vessel_1.default.aggregate([
                    { $match: searchQuery },
                    { $sort: { creation_timestamp: -1 } },
                    { $skip: skip },
                    { $limit: limit },
                ]);
                const userIds = [...new Set(vessels.map((v) => v.created_by.toString()))].map((id) => new mongoose_1.default.Types.ObjectId(id));
                const regionGroupIds = [
                    ...new Set(vessels.map((v) => (v && v.region_group_id ? v.region_group_id.toString() : null)).filter((id) => id)),
                ].map((id) => new mongoose_1.default.Types.ObjectId(id));
                const users = yield User_1.default.aggregate([{ $match: { _id: { $in: userIds } } }, { $project: { _id: 1, name: 1, email: 1 } }]);
                const regionGroups = yield RegionGroup_1.default.find({ _id: { $in: regionGroupIds } }, { _id: 1, name: 1, timezone: 1 });
                const usersById = users.reduce((acc, user) => {
                    acc[user._id.toString()] = user;
                    return acc;
                }, {});
                const regionGroupsById = regionGroups.reduce((acc, group) => {
                    acc[group._id.toString()] = {
                        _id: group._id.toString(),
                        name: group.name,
                        timezone: group.timezone,
                    };
                    return acc;
                }, {});
                const vesselsWithUser = vessels.map((vessel) => (Object.assign(Object.assign({}, vessel), { user: usersById[vessel.created_by.toString()] || null, regionGroup: vessel && vessel.region_group_id ? regionGroupsById[vessel.region_group_id.toString()] || null : null })));
                return {
                    vessels: vesselsWithUser,
                    pagination: {
                        total,
                        page,
                        limit,
                        totalPages: Math.ceil(total / limit),
                    },
                };
            }
            catch (error) {
                console.error("[VesselService.fetchPaginated] Error:", error);
                throw new Error("Failed to fetch vessels");
            }
        });
    }
    findById(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id }) {
            try {
                if (!(0, mongoose_1.isValidObjectId)(id)) {
                    throw new Error("Invalid vessel ID");
                }
                const vessel = yield Vessel_1.default.aggregate([{ $match: { _id: new mongoose_1.default.Types.ObjectId(id) } }]);
                if (!vessel[0])
                    return null;
                const users = yield User_1.default.aggregate([{ $match: { _id: vessel[0].created_by } }, { $project: { _id: 1, name: 1, email: 1 } }]);
                return Object.assign(Object.assign({}, vessel[0]), { user: users[0] || null });
            }
            catch (error) {
                console.error("[VesselService.findById] Error:", error);
                throw error;
            }
        });
    }
    findByAssignedUnitId(_a) {
        return __awaiter(this, arguments, void 0, function* ({ unitId }) {
            try {
                if (!unitId) {
                    throw new Error("unitId is required");
                }
                const vessel = yield Vessel_1.default.findOne({ unit_id: unitId });
                return vessel;
            }
            catch (error) {
                throw new Error(error);
            }
        });
    }
    getAllAssignedUnitIds() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const vessels = yield Vessel_1.default.find({ unit_id: { $ne: null, $exists: true } }, { unit_id: 1, _id: 0 });
                return vessels
                    .map((vessel) => vessel.unit_id)
                    .filter((unitId) => unitId !== null && unitId !== undefined && unitId.trim() !== "");
            }
            catch (error) {
                console.error("[VesselService.getAllAssignedUnitIds] Error:", error);
                throw new Error("Failed to fetch assigned unit IDs");
            }
        });
    }
    create(_a) {
        return __awaiter(this, arguments, void 0, function* ({ name, thumbnail_file, unit_id, is_active = true, created_by, region_group_id, home_port_location, }) {
            try {
                if (unit_id && unit_id.trim() !== "") {
                    const existingVessel = yield Vessel_1.default.findOne({ unit_id });
                    if (existingVessel) {
                        throw new Error("A vessel with this unit ID already exists");
                    }
                }
                if (!region_group_id) {
                    throw new Error("Region group ID is required");
                }
                let thumbnailS3Key = null;
                if (thumbnail_file && thumbnail_file.buffer) {
                    const s3Key = yield this.uploadVesselThumbnail(thumbnail_file);
                    if (s3Key) {
                        thumbnailS3Key = s3Key;
                    }
                }
                const vessel = yield Vessel_1.default.create({
                    name,
                    thumbnail_s3_key: (thumbnailS3Key === null || thumbnailS3Key === void 0 ? void 0 : thumbnailS3Key.thumbnail_s3_key) || null,
                    thumbnail_compressed_s3_key: (thumbnailS3Key === null || thumbnailS3Key === void 0 ? void 0 : thumbnailS3Key.thumbnail_compressed_s3_key) || null,
                    unit_id: unit_id && unit_id.trim() !== "" ? unit_id.trim() : null,
                    is_active,
                    region_group_id: new mongoose_1.default.Types.ObjectId(region_group_id),
                    home_port_location: home_port_location && home_port_location.length === 2
                        ? {
                            type: "Point",
                            coordinates: home_port_location,
                        }
                        : null,
                    created_by,
                });
                const result = yield this.findById({ id: vessel._id.toString() });
                if (!result) {
                    throw new Error("Failed to retrieve created vessel");
                }
                return result;
            }
            catch (error) {
                console.error("[VesselService.create] Error:", error);
                if (error.message.includes("already exists")) {
                    throw error;
                }
                if (error.message.includes("Longitude/latitude is out of bounds")) {
                    throw new Error("Location Coordinates are out of bounds");
                }
                throw new Error("Failed to create vessel");
            }
        });
    }
    update(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, name, thumbnail_file, unit_id, is_active, remove_thumbnail, region_group_id, home_port_location, }) {
            try {
                if (!(0, mongoose_1.isValidObjectId)(id)) {
                    throw new Error("Invalid vessel ID");
                }
                if (unit_id && unit_id.trim() !== "") {
                    const existingVessel = yield Vessel_1.default.findOne({
                        unit_id: unit_id.trim(),
                        _id: { $ne: id },
                    });
                    if (existingVessel) {
                        throw new Error("A vessel with this unit ID already exists");
                    }
                }
                const vessel = yield Vessel_1.default.findById(id);
                if (!vessel) {
                    throw new Error("Vessel not found");
                }
                if (region_group_id) {
                    if (!(0, mongoose_1.isValidObjectId)(region_group_id)) {
                        throw new Error("Invalid region group ID");
                    }
                    const regionGroup = yield RegionGroup_1.default.findById(region_group_id);
                    if (!regionGroup) {
                        throw new Error("Region group ID does not exist");
                    }
                    vessel.region_group_id = new mongoose_1.default.Types.ObjectId(region_group_id);
                }
                let thumbnailS3Key = undefined;
                if (thumbnail_file && thumbnail_file.buffer) {
                    if (vessel.thumbnail_s3_key) {
                        yield (0, awsS3_1.deleteFileFromS3)(awsS3_1.s3Config.buckets.assets, vessel.thumbnail_s3_key);
                    }
                    const s3Key = yield this.uploadVesselThumbnail(thumbnail_file);
                    if (s3Key) {
                        thumbnailS3Key = s3Key;
                    }
                    else {
                        thumbnailS3Key = null;
                    }
                }
                else if (remove_thumbnail === "true") {
                    if (vessel.thumbnail_s3_key) {
                        yield (0, awsS3_1.deleteFileFromS3)(awsS3_1.s3Config.buckets.assets, vessel.thumbnail_s3_key);
                    }
                    if (vessel.thumbnail_compressed_s3_key) {
                        yield (0, awsS3_1.deleteFileFromS3)(awsS3_1.s3Config.buckets.assets, vessel.thumbnail_compressed_s3_key);
                    }
                    thumbnailS3Key = null;
                }
                if (name)
                    vessel.name = name;
                if (thumbnailS3Key !== undefined) {
                    vessel.thumbnail_s3_key = (thumbnailS3Key === null || thumbnailS3Key === void 0 ? void 0 : thumbnailS3Key.thumbnail_s3_key) || null;
                    vessel.thumbnail_compressed_s3_key = (thumbnailS3Key === null || thumbnailS3Key === void 0 ? void 0 : thumbnailS3Key.thumbnail_compressed_s3_key) || null;
                }
                if (unit_id !== undefined)
                    vessel.unit_id = unit_id && unit_id.trim() !== "" ? unit_id.trim() : null;
                if (home_port_location !== undefined) {
                    vessel.home_port_location =
                        home_port_location && home_port_location.length === 2
                            ? {
                                type: "Point",
                                coordinates: home_port_location,
                            }
                            : undefined;
                }
                if (typeof is_active !== "undefined") {
                    vessel.is_active = is_active;
                    if (!is_active) {
                        const vesselId = vessel._id;
                        yield NotificationAlert_1.default.updateMany({ vessel_ids: vesselId }, { $pull: { vessel_ids: vesselId } });
                        yield NotificationSummary_1.default.updateMany({ vessel_ids: vesselId }, { $pull: { vessel_ids: vesselId } });
                        yield InviteToken_1.default.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                        yield ApiKey_1.default.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                        yield User_1.default.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                    }
                }
                yield vessel.save();
                const result = yield this.findById({ id: vessel._id.toString() });
                if (!result) {
                    throw new Error("Failed to retrieve created vessel");
                }
                return result;
            }
            catch (error) {
                console.error("[VesselService.update] Error:", error);
                if (error.message.includes("already exists") || error.message.includes("not found")) {
                    throw error;
                }
                if (error.message.includes("Longitude/latitude is out of bounds")) {
                    throw new Error("Location Coordinates are out of bounds");
                }
                throw new Error("Failed to update vessel");
            }
        });
    }
}
const vesselService = new VesselService();
exports.default = vesselService;
