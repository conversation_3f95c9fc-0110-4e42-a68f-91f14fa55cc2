"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importStar(require("mongoose"));
const NotificationAlert_1 = __importDefault(require("../models/NotificationAlert"));
const User_1 = __importDefault(require("../models/User"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const functions_1 = require("../utils/functions");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const permissions_1 = require("../utils/permissions");
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const Email_1 = require("../utils/Email");
const email_1 = require("../modules/email");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const EmailDomains_1 = __importDefault(require("../models/EmailDomains"));
const node_stream_1 = require("node:stream");
const microservice_socket_1 = __importDefault(require("../modules/microservice_socket"));
const staticMap_1 = require("../utils/staticMap");
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
const preferenceMap = {
    email: "email",
    app: "app",
    both: "email & app",
    "not given": "not given",
};
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_NOTIFICATION_ALERTS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const page = parseInt(req.query.page) || 1;
    const page_size = parseInt(req.query.page_size) || 10;
    try {
        const totalDocuments = yield NotificationAlert_1.default.countDocuments({ created_by: req.user._id });
        const totalPages = Math.ceil(totalDocuments / page_size);
        const notificationAlerts = yield NotificationAlert_1.default.find({ created_by: req.user._id })
            .skip((page - 1) * page_size)
            .limit(page_size);
        res.json({
            data: notificationAlerts,
            total_pages: totalPages,
            total_documents: totalDocuments,
            current_page: page,
            next_page: page < totalPages ? page + 1 : null,
            previous_page: page > 1 ? page - 1 : null,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_NOTIFICATION_ALERTS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("super_category")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((suer_category) => {
        if (suer_category.length === 0) {
            throw new Error(`At least one super category is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("sub_category")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((sub_category) => {
        if (sub_category.length === 0) {
            throw new Error(`At least one sub category is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("country_flags")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
    (0, express_validator_1.body)("type")
        .isString()
        .isIn(["email", "app", "both"])
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
    (0, express_validator_1.body)("title")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((tit) => {
        if (tit.length === 0) {
            throw new Error(`At least one Title is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids")
        .isArray()
        .notEmpty()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((vessels) => {
        if (vessels.length === 0) {
            throw new Error(`At least one Vessel ID is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids.*")
        .custom((value) => {
        if (value === "all")
            return true;
        return (0, mongoose_1.isValidObjectId)(value);
    })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("receivers")
        .isArray()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((receivers) => {
        if (receivers.length === 0) {
            throw new Error(`At least one receiver is required`);
        }
        for (let email of receivers) {
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                throw new Error(`Invalid email address: ${email}`);
            }
        }
        return true;
    }),
    (0, express_validator_1.body)("is_enabled")
        .optional()
        .custom((value) => {
        if (value === 1 || value === 0) {
            return true;
        }
        else {
            throw new Error(`Invalid value ${value} provided for is_enabled`);
        }
    }),
]), hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { super_category, sub_category, country_flags, type, title, vessel_ids, receivers, is_enabled, } = req.body;
        if (["email", "both"].includes(type)) {
            if (!req.user.email) {
                return res.status(403).json({ message: "No email is associated with your account" });
            }
        }
        /** Ensure user cannot subscribe to vessels not assigned to them */
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vessel_ids.filter((id) => id !== "all") } });
        const hasUnauthorizedVessels = vessels.some((v) => !(0, functions_1.canAccessVessel)(req, v));
        if (hasUnauthorizedVessels) {
            return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
        }
        if (receivers) {
            const getDomains = yield EmailDomains_1.default.find({});
            const domains = getDomains.map((domain) => domain.domain);
            if (!req.user.email) {
                return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
            }
            const userEmailDomain = req.user.email.split("@")[1];
            if (!domains.includes(userEmailDomain)) {
                return res.status(400).json({ message: "User email domain is not allowed." });
            }
            const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.additionalEmailAddressesPrivilege);
            const allReceiversValid = receivers.every((email) => {
                const receiverDomain = email.split("@")[1];
                return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
            });
            if (!allReceiversValid) {
                return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
            }
        }
        const processedVesselIds = vessel_ids.map((id) => {
            if (id === "all")
                return "all";
            return new mongoose_1.default.Types.ObjectId(id);
        });
        const notificationAlert = yield NotificationAlert_1.default.create({
            super_category,
            sub_category,
            country_flags,
            type,
            title,
            vessel_ids: processedVesselIds,
            receivers,
            is_enabled: is_enabled !== 0,
            created_by: req.user._id,
        });
        if (receivers) {
            const { email } = req.user;
            if (receivers.length > 0 && (type === "email" || type === "both")) {
                for (let newReceiver of receivers) {
                    const token = (0, functions_1.generateUnsubscribeToken)(newReceiver, notificationAlert._id.toString());
                    const payload = {
                        addBy: email || "",
                        vessel: notificationAlert.title.length > 0 ? notificationAlert.title.join(",") : "All",
                        sub: notificationAlert.sub_category.length > 0 ? notificationAlert.sub_category.join(",") : "All",
                        super: notificationAlert.super_category.length > 0 ? notificationAlert.super_category.join(",") : "All",
                        flag: notificationAlert.country_flags.length > 0 ? notificationAlert.country_flags.join(",") : "All",
                        preference: notificationAlert.type === "email"
                            ? "email"
                            : notificationAlert.type === "app"
                                ? "app"
                                : notificationAlert.type === "both"
                                    ? "email & app"
                                    : "not given",
                        link: `${process.env.API_URL}/notificationsAlerts/unsubscribe/email?token=${token}`,
                    };
                    const emailBody = (0, Email_1.NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT)(payload, new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear());
                    (0, email_1.sendEmail)({
                        to: newReceiver,
                        subject: "Detection Notification",
                        html: emailBody,
                    });
                }
            }
        }
        res.json(notificationAlert);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_NOTIFICATION_ALERTS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("super_category")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((suer_category) => {
        if (suer_category.length === 0) {
            throw new Error(`At least one super category is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("sub_category")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((sub_category) => {
        if (sub_category.length === 0) {
            throw new Error(`At least one sub category is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("country_flags")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
    (0, express_validator_1.body)("type")
        .isString()
        .isIn(["email", "app", "both"])
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
    (0, express_validator_1.body)("title")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((tit) => {
        if (tit.length === 0) {
            throw new Error(`At least one Title is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids")
        .isArray()
        .notEmpty()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((vessels) => {
        if (vessels.length === 0) {
            throw new Error(`At least one Vessel ID is required`);
        }
        return true;
    }),
    (0, express_validator_1.body)("vessel_ids.*")
        .optional()
        .custom((value) => {
        if (value === "all")
            return true;
        return (0, mongoose_1.isValidObjectId)(value);
    })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("receivers")
        .isArray()
        .optional()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    })
        .custom((receivers) => {
        // if (receivers.length === 0) {
        //     throw new Error(`At least one receiver is required`);
        // }
        for (let email of receivers) {
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                throw new Error(`Invalid email address: ${email}`);
            }
        }
        return true;
    }),
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { super_category, sub_category, country_flags, type, title, vessel_ids, receivers } = req.body;
    if (["email", "both"].includes(type)) {
        if (!req.user.email) {
            return res.status(403).json({ message: "No email is associated with your account" });
        }
    }
    /** Ensure user cannot subscribe to vessels not assigned to them */
    if (vessel_ids) {
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vessel_ids.filter((id) => id !== "all") } });
        const hasUnauthorizedVessels = vessels.some((v) => !(0, functions_1.canAccessVessel)(req, v));
        if (hasUnauthorizedVessels) {
            return res.status(403).json({ message: "Not allowed to subscribe to vessels not assigned to you" });
        }
    }
    if (receivers) {
        const getDomains = yield EmailDomains_1.default.find({});
        const domains = getDomains.map((domain) => domain.domain);
        if (!req.user.email) {
            return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
        }
        const userEmailDomain = req.user.email.split("@")[1];
        if (!domains.includes(userEmailDomain)) {
            return res.status(400).json({ message: "User email domain is not allowed." });
        }
        const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.additionalEmailAddressesPrivilege);
        const allReceiversValid = receivers.every((email) => {
            const receiverDomain = email.split("@")[1];
            return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
        });
        if (!allReceiversValid) {
            return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
        }
        const notificationAlert = yield NotificationAlert_1.default.findById(req.params.id);
        if (!notificationAlert) {
            return res.status(404).json({ message: "Notification Alert not found" });
        }
        //check if new addition is made in payload separate that out please for me
        const newReceivers = receivers.filter((receiver) => !notificationAlert.receivers.includes(receiver));
        const { email } = req.user;
        if (newReceivers.length > 0 &&
            (notificationAlert.type === "email" || notificationAlert.type === "both") &&
            notificationAlert.is_enabled) {
            //generate new token for each new receiver
            for (let newReceiver of newReceivers) {
                const token = (0, functions_1.generateUnsubscribeToken)(newReceiver, notificationAlert._id.toString());
                const payload = {
                    addBy: email || "",
                    vessel: notificationAlert.title.length > 0 ? notificationAlert.title.join(",") : "All",
                    sub: notificationAlert.sub_category.length > 0 ? notificationAlert.sub_category.join(",") : "All",
                    super: notificationAlert.super_category.length > 0 ? notificationAlert.super_category.join(",") : "All",
                    flag: notificationAlert.country_flags.length > 0 ? notificationAlert.country_flags.join(",") : "All",
                    preference: preferenceMap[notificationAlert.type],
                    link: `${process.env.API_URL}/notificationsAlerts/unsubscribe/email?token=${token}`,
                };
                const emailBody = (0, Email_1.NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT)(payload, new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear());
                (0, email_1.sendEmail)({
                    to: newReceiver,
                    subject: "Detection Notification",
                    html: emailBody,
                });
            }
        }
    }
    try {
        let processedVesselIds;
        if (vessel_ids) {
            processedVesselIds = vessel_ids.map((id) => {
                if (id === "all")
                    return "all";
                return new mongoose_1.default.Types.ObjectId(id);
            });
        }
        const updateData = {
            super_category,
            sub_category,
            country_flags,
            type,
            title,
            receivers,
            updated_at: new Date().toISOString(),
        };
        if (processedVesselIds) {
            updateData.vessel_ids = processedVesselIds;
        }
        const notificationAlert = yield NotificationAlert_1.default.findByIdAndUpdate(req.params.id, updateData, { new: true });
        res.json(notificationAlert);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/enable", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_NOTIFICATION_ALERTS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationAlert = yield NotificationAlert_1.default.findByIdAndUpdate(req.params.id, { is_enabled: true, updated_at: new Date().toISOString() }, { new: true });
        res.json(notificationAlert);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/disable", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_NOTIFICATION_ALERTS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationAlert = yield NotificationAlert_1.default.findByIdAndUpdate(req.params.id, { is_enabled: false, updated_at: new Date().toISOString() }, { new: true });
        res.json(notificationAlert);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_NOTIFICATION_ALERTS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), hasPermission_1.default.bind(this, [permissions_1.permissions.manageNotifications]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const notificationAlert = yield NotificationAlert_1.default.findByIdAndDelete(req.params.id);
        res.json(notificationAlert);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/unsubscribe/email", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UNSUBSCRIBE_NOTIFICATION_ALERTS), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // decrypt jwt_token to get email and notification_summary id in it
    const { token } = req.query;
    const { notificationId, email } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
    try {
        const notificationAlert = yield NotificationAlert_1.default.findById(notificationId);
        if (!notificationAlert) {
            return res.redirect(`${process.env.APP_URL}/subscription?status=404&title=Something Went Wrong&message=Notification Alert does not exist`);
        }
        if (!notificationAlert.receivers.includes(email)) {
            const user = yield User_1.default.findOne({ email });
            if (user && user._id.toString() === notificationAlert.created_by.toString()) {
                yield NotificationAlert_1.default.updateOne({ _id: notificationId }, { $set: { is_enabled: false, updated_at: new Date().toISOString() } });
                return res.redirect(`${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `);
            }
            return res.redirect(`${process.env.APP_URL}/subscription?status=401&title=Email Removed&message=This email does not exist or already removed from notification alert`);
        }
        const updateRes = yield NotificationAlert_1.default.updateOne({ _id: notificationId }, { $pull: { receivers: email } });
        if (!updateRes.modifiedCount) {
            return res.redirect(`${process.env.APP_URL}/subscription?status=404&title=Email Removed&message=This email does not exist or already removed from notification alert`);
        }
        return res.redirect(`${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `);
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}));
router.get("/map.:ext?", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_MAP_FOR_ALERT), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // decrypt jwt_token to get locations
    const { token } = req.query;
    const { locations } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
    try {
        if (!locations) {
            return res.status(500).send({ message: "Error processing request" });
        }
        const markers = locations.map((location) => {
            return (0, functions_1.buildStaticMarkerSignature)(location[0], location[1]);
        });
        const { ext } = req.params;
        const mapData = yield (0, functions_1.getStaticMapOld)(markers, null, ext);
        if (mapData && "mimeType" in mapData && "source" in mapData) {
            res.setHeader("Content-Type", mapData.mimeType);
            res.setHeader("Content-Length", mapData.source.headers.get("content-length"));
            res.setHeader("access-control-allow-origin", "*");
            res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            res.setHeader("Expires", "0");
            res.setHeader("Pragma", "no-cache");
            if (mapData.source) {
                const nodeStream = node_stream_1.Readable.fromWeb(mapData.source.body);
                nodeStream.pipe(res);
            }
            else {
                return res.status(500).send({ message: "Error serving image" });
            }
        }
        else {
            res.status(500).send({ message: "Error serving image" });
        }
    }
    catch (error) {
        console.error("Error serving image:", error);
        res.status(500).send({ message: "Error serving image" });
    }
}));
router.get("/cluster/:amount", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_MAP_CLUSTER), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("amount")
        .isNumeric()
        .withMessage((value) => {
        return `Invalid value ${value} provided for amount`;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { bgColor, textColor } = req.query;
        const { amount } = req.params;
        const buffer = yield (0, staticMap_1.generateNumberedIconBuffer)(amount, bgColor, textColor);
        if (buffer) {
            res.setHeader("Content-Type", "image/png");
            res.setHeader("Content-Length", buffer.byteLength);
            res.setHeader("access-control-allow-origin", "*");
            res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            res.setHeader("Expires", "0");
            res.setHeader("Pragma", "no-cache");
            const nodeStream = node_stream_1.Readable.from(buffer);
            nodeStream.pipe(res);
        }
        else {
            res.status(500).send({ message: "Error serving image" });
        }
    }
    catch (error) {
        console.error("Error serving image:", error);
        res.status(500).send({ message: "Error serving image" });
    }
}));
router.get("/testing", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_NOTIFICATION_ALERTS_BY_USER), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.testNotificationAlerts]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        microservice_socket_1.default.emit("testing/triggerNotifications", JSON.stringify({ user_id: req.user._id }));
        res.status(200).json({ message: "Notification Alerts have been emitted" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
