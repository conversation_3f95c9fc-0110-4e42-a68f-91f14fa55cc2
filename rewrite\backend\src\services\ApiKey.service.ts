import ApiEndpoint from "../models/ApiEndpoint";
import <PERSON><PERSON><PERSON>ey from "../models/ApiKey";
import Vessel from "../models/Vessel";
import { isValidObjectId } from "mongoose";

class ApiKeyService {
    async fetchAll() {
        const apiKeys = await Api<PERSON>ey.aggregate([
            {
                $match: { is_deleted: false },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                username: 1,
                            },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    created_by: { $arrayElemAt: ["$created_by", 0] },
                },
            },
        ]);
        return apiKeys;
    }

    async findById({ id }: { id: string }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOne({
            _id: id,
            is_deleted: false,
        });

        if (!apiKey) return null;
        return apiKey;
    }

    async create({ description, email, created_by }: { description: string; email: string; created_by: string }) {
        const apiKey = await Api<PERSON><PERSON>.create({ description, email, created_by });
        return await this.findById({ id: apiKey._id.toString() });
    }

    async update({ id, description, email }: { id: string; description?: string; email?: string }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const data = { description, email };
        Object.keys(data).forEach((el) => {
            const key = el as keyof typeof data;
            if (data[key] === undefined) delete data[key];
        });

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, data, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id.toString() });
    }

    async updateAllowedEndpoints({ id, allowed_endpoints }: { id: string; allowed_endpoints: number[] }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiEndpoints = await ApiEndpoint.find();
        if (allowed_endpoints.some((e_id) => !apiEndpoints.find((e) => e.endpoint_id === e_id))) throw new Error("Invalid endpoint provided");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_endpoints }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id.toString() });
    }

    async updateAllowedVessels({ id, allowed_vessels }: { id: string; allowed_vessels: string[] }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const inActiveVessels = await Vessel.find({ _id: { $in: allowed_vessels }, is_active: false });
        if (inActiveVessels.length > 0) {
            throw new Error("Cannot assign inactive vessels to API key");
        }

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_vessels }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id.toString() });
    }

    async updateRevocationStatus({ id, is_revoked }: { id: string; is_revoked: boolean }) {
        if (!isValidObjectId(id)) throw new Error("Invalid API key id");

        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_revoked }, { new: true });

        if (!apiKey) return null;
        return await this.findById({ id: apiKey._id.toString() });
    }

    async delete({ id }: { id: string }) {
        const apiKey = await ApiKey.findOneAndUpdate({ _id: id, is_deleted: false }, { is_deleted: true }, { new: true });
        return apiKey !== null;
    }
}

const apiKeyService = new ApiKeyService();

export default apiKeyService;
