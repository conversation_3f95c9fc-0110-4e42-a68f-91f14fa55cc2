"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const RegionGroup_1 = __importDefault(require("../models/RegionGroup"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const mongoose_1 = __importStar(require("mongoose"));
const Stream_service_1 = __importDefault(require("./Stream.service"));
const functions_1 = require("../utils/functions");
class RegionGroupService {
    find(query, projection) {
        return __awaiter(this, void 0, void 0, function* () {
            const pipeline = [];
            if (query) {
                pipeline.push({ $match: query });
            }
            if (projection) {
                pipeline.push({ $project: projection });
            }
            pipeline.push({
                $lookup: {
                    from: "users",
                    localField: "created_by",
                    foreignField: "_id",
                    as: "created_by",
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                            },
                        },
                    ],
                },
            });
            pipeline.push({
                $unwind: "$created_by",
            });
            const regionGroups = yield RegionGroup_1.default.aggregate(pipeline);
            const vessels = (yield Vessel_service_1.default.find({}, { _id: 1, name: 1, unit_id: 1, region_group_id: 1 }));
            const updatedRegionGroups = regionGroups.map((regionGroup) => {
                // const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup.vessel_ids?.includes(vessel._id.toString()));
                const vesselsInRegionGroup = vessels.filter((vessel) => { var _a; return regionGroup._id.toString() === ((_a = vessel.region_group_id) === null || _a === void 0 ? void 0 : _a.toString()); });
                return Object.assign(Object.assign({}, regionGroup), { vessels: vesselsInRegionGroup.map((vessel) => ({
                        vessel_id: vessel._id.toString(),
                        unit_id: vessel.unit_id,
                        name: vessel.name,
                    })) });
            });
            return updatedRegionGroups;
        });
    }
    findById(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid region group id");
            const regionGroup = yield RegionGroup_1.default.aggregate([
                {
                    $match: {
                        _id: new mongoose_1.default.Types.ObjectId(id),
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "created_by",
                        foreignField: "_id",
                        as: "created_by",
                        pipeline: [
                            {
                                $project: {
                                    _id: 1,
                                    name: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: "$created_by",
                },
            ]);
            if (!regionGroup.length)
                return null;
            const vessels = (yield Vessel_service_1.default.find({}, { _id: 1, name: 1, unit_id: 1, region_group_id: 1 }));
            const vesselsInRegionGroup = vessels.filter((vessel) => { var _a; return regionGroup[0]._id.toString() === ((_a = vessel.region_group_id) === null || _a === void 0 ? void 0 : _a.toString()); });
            // const vesselsInRegionGroup = vessels.filter((vessel) => regionGroup[0].vessel_ids.includes(vessel._id.toString()) );
            return Object.assign(Object.assign({}, regionGroup[0]), { vessels: vesselsInRegionGroup.map((vessel) => ({
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                })) });
        });
    }
    create(_a) {
        return __awaiter(this, arguments, void 0, function* ({ name, timezone, created_by }) {
            const regionGroup = yield RegionGroup_1.default.create({ name, timezone, created_by });
            Stream_service_1.default.resetCache();
            return (yield this.findById({ id: regionGroup._id.toString() }));
        });
    }
    update(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, name, timezone }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid region group id");
            const data = { name, timezone };
            Object.keys(data).forEach((key) => {
                if (data[key] === undefined)
                    delete data[key];
            });
            data.name = (0, functions_1.normalizeName)(data.name || "");
            const regionGroup = yield RegionGroup_1.default.findByIdAndUpdate(id, data);
            Stream_service_1.default.resetCache();
            return (yield this.findById({ id: (regionGroup === null || regionGroup === void 0 ? void 0 : regionGroup._id.toString()) || id }));
        });
    }
    delete(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id }) {
            const vessels = yield Vessel_service_1.default.find({ region_group_id: id }, { _id: 1 });
            if (vessels.length > 0)
                throw { status: 409, message: "Cannot delete region group: vessels are associated with this group." };
            const data = yield RegionGroup_1.default.findByIdAndDelete(id);
            Stream_service_1.default.resetCache();
            return !!data;
        });
    }
}
const regionGroupService = new RegionGroupService();
exports.default = regionGroupService;
