"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Role_1 = __importDefault(require("../models/Role"));
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const permissions_1 = require("../utils/permissions");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const mongoose_1 = __importStar(require("mongoose"));
const Permission_1 = __importDefault(require("../models/Permission"));
const User_1 = __importDefault(require("../models/User"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ROLES), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const roles = yield Role_1.default.aggregate([
            {
                $lookup: {
                    from: "users",
                    let: { created_by: "$created_by" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$created_by"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "user",
                },
            },
            {
                $addFields: {
                    user: { $arrayElemAt: ["$user", 0] },
                },
            },
            {
                $project: {
                    role_id: 1,
                    role_name: 1,
                    denied_permissions: 1,
                    deletable: 1,
                    editable: 1,
                    hierarchy_number: 1,
                    "user.name": 1,
                    "user.username": 1,
                    creation_timestamp: 1,
                },
            },
        ]);
        res.json(roles);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_ROLE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRoles]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("role_name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { role_name } = req.body;
    try {
        yield Role_1.default.create({ role_name, role_id: -1, hierarchy_number: -1, created_by: req.user._id });
        res.json({ message: `Role has been created` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_ROLE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRoles]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("role_name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("denied_permissions")
        .isArray()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.check)("denied_permissions.*")
        .if((0, express_validator_1.body)("denied_permissions").exists())
        .custom(functions_1.isIntStrict)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { denied_permissions } = req.body;
        const role = yield Role_1.default.findById(req.params.id);
        if (!role)
            return res.status(404).json({ message: `Role does not exist` });
        if (!role.editable)
            return res.status(400).json({ message: `Role cannot be edited` });
        if (req.user.role.hierarchy_number >= role.hierarchy_number)
            return res.status(403).json({ message: "You cannot update this role as it exceeds your hierarchy level" });
        if (denied_permissions) {
            const permissions = yield Permission_1.default.find();
            if (denied_permissions.some((p_id) => !permissions.find((p) => p.permission_id === p_id)))
                return res.status(400).json({ message: `Invalid permissions provided` });
            if (role.denied_permissions.some((p_id) => { var _a; return !((_a = permissions.find((p) => p.permission_id === p_id)) === null || _a === void 0 ? void 0 : _a.assignable) && !denied_permissions.includes(p_id); }) ||
                denied_permissions.some((p_id) => { var _a; return !((_a = permissions.find((p) => p.permission_id === p_id)) === null || _a === void 0 ? void 0 : _a.assignable) && !role.denied_permissions.includes(p_id); }))
                return res.status(403).json({ message: `Cannot edit a forbidden permission` });
        }
        if (req.body.role_name !== undefined)
            role.role_name = req.body.role_name;
        if (req.body.denied_permissions !== undefined)
            role.denied_permissions = req.body.denied_permissions;
        yield role.save();
        return res.json({ message: `Role '${role.role_name}' has been edited` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/permissionUpdate", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.PERMISSION_UPDATE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRoles]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("roles_permissions").isArray({ min: 1 }).withMessage("Roles Permissions must be a non-empty array"),
    (0, express_validator_1.body)("roles_permissions.*._id")
        .notEmpty()
        .withMessage("Roles Permission Id is required")
        .bail()
        .custom(mongoose_1.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { roles_permissions } = req.body;
        const userHierarchyNumber = req.user.role.hierarchy_number;
        const roleIds = roles_permissions.map((role) => role._id);
        const dbRoles = yield Role_1.default.find({ _id: { $in: roleIds } });
        const allowedRoles = dbRoles.filter((role) => role.hierarchy_number > userHierarchyNumber);
        if (allowedRoles.length === 0) {
            return res.status(403).json({ message: "You are not authorized to update any role as it exceeds your hierarchy level" });
        }
        const editableRoles = allowedRoles.filter((role) => (role.editable ? role : false));
        if (editableRoles.length === 0) {
            return res.status(403).json({ message: "No roles are permitted to be edited" });
        }
        const convertedIds = editableRoles.map((role) => (Object.assign(Object.assign({}, role.toObject()), { _id: String(role.toObject()._id) })));
        const updatedConvertedIds = convertedIds
            .map((role) => {
            const matchingRole = roles_permissions.find((r) => r._id === role._id);
            if (matchingRole) {
                return Object.assign(Object.assign({}, role), { denied_permissions: matchingRole.denied_permissions });
            }
            return null;
        })
            .filter((role) => role !== null);
        const permissions = yield Permission_1.default.find();
        const validPermissionIds = permissions.map((p) => p.permission_id);
        const nonAssignableIds = permissions.filter((p) => !p.assignable).map((p) => p.permission_id);
        const rolesWithValidPermissions = updatedConvertedIds.filter((role) => role.denied_permissions.every((p_id) => validPermissionIds.includes(p_id)));
        if (rolesWithValidPermissions.length === 0) {
            return res.status(403).json({ message: "The role(s) have invalid permission IDs" });
        }
        const rolesWithAllNonAssignablePermissions = rolesWithValidPermissions.filter((role) => nonAssignableIds.every((p_id) => role.denied_permissions.includes(p_id)));
        if (rolesWithAllNonAssignablePermissions.length === 0) {
            return res.status(403).json({ message: "The role does not contain non-assignable permission IDs" });
        }
        const validRoles = rolesWithAllNonAssignablePermissions;
        const bulkOps = validRoles.map((role) => ({
            updateOne: {
                filter: { _id: new mongoose_1.default.Types.ObjectId(role._id) },
                update: { $set: { denied_permissions: role.denied_permissions } },
            },
        }));
        const result = yield Role_1.default.bulkWrite(bulkOps);
        validRoles.forEach((role) => {
            const updatedRole = dbRoles.find((r) => String(r._id) === String(role._id));
            // Only emit if the hierarchy number was actually updated
            if (updatedRole) {
                ioEmitter_1.default.emit("notifyAll", {
                    name: "roles/changed",
                    data: role, // Emit only the updated role
                });
            }
        });
        res.status(200).json({
            message: "Role Permissions Updated",
            result,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_ROLE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRoles]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const role = yield Role_1.default.findById(req.params.id);
        if (!role)
            return res.status(404).json({ message: `Role does not exist` });
        if (!role.deletable)
            return res.status(400).json({ message: `Role cannot be deleted` });
        if (req.user.role.hierarchy_number >= role.hierarchy_number)
            return res.status(400).json({ message: "You cannot remove this role as it exceeds your hierarchy level" });
        if (yield User_1.default.findOne({ role_id: role.role_id }))
            return res.status(400).json({ message: `Role cannot be deleted because it is assigned to a user` });
        yield Role_1.default.findOneAndDelete({ _id: role._id });
        return res.json({ message: `Role has been deleted` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
//this is for update the  heirarchy of the roles
router.patch("/reorder", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.REORDER_ROLE), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRoles]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("roles").isArray({ min: 1 }).withMessage("Roles must be a non-empty array"),
    (0, express_validator_1.body)("roles.*._id")
        .notEmpty()
        .withMessage("Role ID is required")
        .bail()
        .custom(mongoose_1.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("roles.*.hierarchy_number")
        .notEmpty()
        .withMessage("Hierarchy number is required")
        .bail()
        .isInt({ min: 1 })
        .withMessage("Hierarchy number must be an integer greater than 0"),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const roles = req.body.roles;
    const userHierarchyNumber = req.user.role.hierarchy_number;
    try {
        const roleIds = roles.map((role) => role._id);
        const dbRoles = yield Role_1.default.find({ _id: { $in: roleIds } });
        const allowedRoles = dbRoles.filter((role) => role.hierarchy_number > userHierarchyNumber);
        if (allowedRoles.length === 0) {
            return res.status(403).json({ message: "You are not authorized to update any roles" });
        }
        const allowedRoleIds = allowedRoles.map((role) => String(role._id));
        const validRoles = roles.filter((role) => allowedRoleIds.includes(String(role._id)));
        if (validRoles.length === 0) {
            return res.status(403).json({ message: "You are not authorized to update these roles" });
        }
        const bulkOps = validRoles.map((role) => ({
            updateOne: {
                filter: { _id: new mongoose_1.default.Types.ObjectId(role._id) },
                update: { $set: { hierarchy_number: role.hierarchy_number } },
            },
        }));
        const result = yield Role_1.default.bulkWrite(bulkOps);
        validRoles.forEach((role) => {
            const updatedRole = dbRoles.find((r) => String(r._id) === String(role._id));
            // Only emit if the hierarchy number was actually updated
            if (updatedRole && updatedRole.hierarchy_number !== role.hierarchy_number) {
                ioEmitter_1.default.emit("notifyAll", {
                    name: "roles/changed",
                    data: role, // Emit only the updated role
                });
            }
        });
        res.status(200).json({
            message: "Hierarchy numbers updated successfully for allowed roles",
            result,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
