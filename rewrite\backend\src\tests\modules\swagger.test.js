// const swaggerJsDoc = require('swagger-jsdoc');
// const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger'); // Update with the correct path
// const fs = require('fs');

// Mocking the dependencies
// jest.mock('swagger-jsdoc');
// jest.mock('fs');

describe('Swagger Module', () => {
    beforeEach(() => {
        jest.resetModules()
    });

    it('should export swaggerUi and swaggerDocs', () => {
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        expect(swaggerUi).toBeDefined()
        expect(swaggerDocs).toBeDefined()
        expect(swaggerConfig).toBeDefined()
    });

    it('should export swaggerUi and swaggerDocs in dev environment', () => {
        process.env.NODE_ENV = 'dev'
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        expect(swaggerUi).toBeDefined()
        expect(swaggerDocs).toBeDefined()
        expect(swaggerConfig).toBeDefined()
    });

    it('should simulate calling the response interceptor with /api/users route', () => {
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        };
        let response = {
            status: 200,
            url: '/api/users',
            body: {
                jwt_token: 'token'
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });

    it('should simulate calling the response interceptor with /api/users/auth route', () => {
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        };
        let response = {
            status: 200,
            url: '/api/users/auth',
            body: {
                jwt_token: 'token'
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });

    it('should simulate calling the response interceptor with /api/users/auth route with undefined jwt_token', () => {
        const { swaggerUi, swaggerDocs, swaggerConfig } = require('../../modules/swagger');
        global.window = {
            ui: {
                preauthorizeApiKey: jest.fn(),
            }
        };
        let response = {
            status: 200,
            url: '/api/users/auth',
            body: {
                jwt_token: undefined
            }
        }
        swaggerConfig.swaggerOptions.responseInterceptor(response)
    });
});
