"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const Permission_1 = __importDefault(require("./Permission"));
const db_1 = __importDefault(require("../modules/db"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const roleSchema = new mongoose_1.default.Schema({
    role_id: { type: Number, required: true },
    role_name: { type: String, required: true, unique: true },
    denied_permissions: { type: [Number], required: true },
    deletable: { type: Boolean, required: true, default: true },
    editable: { type: Boolean, required: true, default: true },
    hierarchy_number: { type: Number, required: true },
    created_by: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: "User",
    },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});
// Pre-save hook to auto-increment role_id
roleSchema.pre("save", function (next) {
    return __awaiter(this, void 0, void 0, function* () {
        if (this.isNew) {
            // Only increment for new documents
            try {
                // Find the document with the highest `role_id` and increment by 1
                const latest = yield this.constructor().findOne().sort({ role_id: -1 });
                this.role_id = latest ? latest.role_id + 1 : 1;
                this.hierarchy_number = this.role_id;
                this.denied_permissions = yield Permission_1.default.find().then((result) => result.map((p) => p.permission_id));
                next();
            }
            catch (err) {
                // next(err);
                console.error(err);
            }
        }
        else {
            next();
        }
    });
});
roleSchema.post("save", (role) => {
    ioEmitter_1.default.emit("notifyAll", { name: `roles/changed`, data: role.toObject() });
});
roleSchema.post("findOneAndDelete", (role) => {
    ioEmitter_1.default.emit("notifyAll", { name: `roles/changed`, data: role.toObject() });
});
// function emitChangedEvent(role) {
//     ioEmitter.emit('notifyAll', { name: `roles/changed`, data: role.toObject() });
// }
const Role = db_1.default.qm.model("Role", roleSchema);
exports.default = Role;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
