import express, { Request, Response } from "express";
import Permission from "../models/Permission";
import { validateError } from "../utils/functions";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_PERMISSIONS), isAuthenticated, async (req: Request, res: Response) => {
    try {
        const permissions = await Permission.find();
        res.json(permissions);
    } catch (err) {
        validateError(err, res);
    }
});

export default router;
