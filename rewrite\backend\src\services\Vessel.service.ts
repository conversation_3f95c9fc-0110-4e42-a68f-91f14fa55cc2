import mongoose, { isValidObjectId } from "mongoose";
import Vessel from "../models/Vessel";
import User from "../models/User";
import NotificationAlert from "../models/NotificationAlert";
import NotificationSummary from "../models/NotificationSummary";
import InviteToken from "../models/InviteToken";
import ApiKey from "../models/ApiKey";
import { uploadFileToS3, deleteFileFromS3, s3Config } from "../modules/awsS3";
import { escapeRegExp } from "../utils/functions";
import sharp from "sharp";
import RegionGroup from "../models/RegionGroup";
import { IVessel, IVesselPaginationResult, IVesselWithUserDetails } from "../interfaces/Vessel";
import { IFile } from "../interfaces/File";
import { IRegionGroup } from "../interfaces/RegionGroup";
import { IUser } from "../interfaces/User";
import { IQueryFilter, IProjection } from "../interfaces/Common";

class VesselService {
    async compressImageToWebp(buffer: Buffer): Promise<Buffer> {
        return sharp(buffer).resize({ width: 512 }).webp({ quality: 90 }).toBuffer();
    }

    async uploadVesselThumbnail(file: IFile): Promise<{ thumbnail_s3_key: string; thumbnail_compressed_s3_key: string } | null> {
        try {
            if (!file || !file.buffer) {
                return null;
            }

            const vesselThumbnailsPath = "vessel-thumbnails";

            const timestamp = Date.now();
            const randomString = Math.random().toString(36).substring(2, 15);
            const baseName = file.originalname ? file.originalname.split(".")[0] : "thumbnail";
            const origExt = file.originalname ? file.originalname.split(".").pop() : "jpg";
            const origFileName = `${vesselThumbnailsPath}/${timestamp}-${randomString}-${baseName}.${origExt}`;
            const origS3Key = await uploadFileToS3(file, s3Config.buckets.assets, origFileName, { ACL: "private" });

            // Save compressed .webp image
            const webpBuffer = await this.compressImageToWebp(file.buffer);
            const compressedFileName = `${vesselThumbnailsPath}/${timestamp}-${randomString}-${baseName}.webp`;
            const compressedS3Key = await uploadFileToS3(
                {
                    buffer: webpBuffer,
                    originalname: `${baseName}.webp`,
                    mimetype: "image/webp",
                },
                s3Config.buckets.assets,
                compressedFileName,
                { ACL: "private" },
            );

            return {
                thumbnail_s3_key: origS3Key,
                thumbnail_compressed_s3_key: compressedS3Key,
            };
        } catch (error) {
            console.error("[VesselService.uploadVesselThumbnail] Error:", error);
            throw new Error("Failed to upload vessel thumbnail to S3");
        }
    }

    async find(query: IQueryFilter = {}, projection?: IProjection): Promise<IVessel[]> {
        try {
            const vessels = await Vessel.find(query, projection);
            return vessels;
        } catch (error) {
            console.error("[VesselService.find] Error:", error);
            throw new Error("Failed to fetch vessels");
        }
    }

    async fetchPaginated({
        page = 1,
        limit = 10,
        search = "",
    }: {
        page?: number;
        limit?: number;
        search?: string;
    }): Promise<IVesselPaginationResult> {
        try {
            const skip = (page - 1) * limit;
            const escapedSearch = escapeRegExp(search);
            const searchQuery = escapedSearch
                ? {
                      $or: [{ name: { $regex: escapedSearch, $options: "i" } }, { unit_id: { $regex: escapedSearch, $options: "i" } }],
                  }
                : {};

            const total = await Vessel.countDocuments(searchQuery);

            const vessels = await Vessel.aggregate([
                { $match: searchQuery },
                { $sort: { creation_timestamp: -1 } },
                { $skip: skip },
                { $limit: limit },
            ]);

            const userIds = [...new Set(vessels.map((v) => v.created_by.toString()))].map((id) => new mongoose.Types.ObjectId(id));
            const regionGroupIds = [
                ...new Set(vessels.map((v) => (v && v.region_group_id ? v.region_group_id.toString() : null)).filter((id) => id)),
            ].map((id) => new mongoose.Types.ObjectId(id));
            const users = await User.aggregate([{ $match: { _id: { $in: userIds } } }, { $project: { _id: 1, name: 1, email: 1 } }]);
            const regionGroups = await RegionGroup.find({ _id: { $in: regionGroupIds } }, { _id: 1, name: 1, timezone: 1 });
            const usersById = users.reduce(
                (acc: Record<string, Pick<IUser, "_id" | "name" | "email">>, user) => {
                    acc[user._id.toString()] = user;
                    return acc;
                },
                {} as Record<string, Pick<IUser, "_id" | "name" | "email">>,
            );
            const regionGroupsById = regionGroups.reduce(
                (acc: Record<string, Pick<IRegionGroup, "_id" | "name" | "timezone">>, group: IRegionGroup) => {
                    acc[group._id.toString()] = {
                        _id: group._id.toString(),
                        name: group.name,
                        timezone: group.timezone,
                    };
                    return acc;
                },
                {} as Record<string, Pick<IRegionGroup, "_id" | "name" | "timezone">>,
            );

            const vesselsWithUser = vessels.map((vessel) => ({
                ...vessel,
                user: usersById[vessel.created_by.toString()] || null,
                regionGroup: vessel && vessel.region_group_id ? regionGroupsById[vessel.region_group_id.toString()] || null : null,
            }));

            return {
                vessels: vesselsWithUser,
                pagination: {
                    total,
                    page,
                    limit,
                    totalPages: Math.ceil(total / limit),
                },
            };
        } catch (error) {
            console.error("[VesselService.fetchPaginated] Error:", error);
            throw new Error("Failed to fetch vessels");
        }
    }

    async findById({ id }: { id: string }): Promise<IVesselWithUserDetails | null> {
        try {
            if (!isValidObjectId(id)) {
                throw new Error("Invalid vessel ID");
            }

            const vessel = await Vessel.aggregate([{ $match: { _id: new mongoose.Types.ObjectId(id) } }]);
            if (!vessel[0]) return null;

            const users = await User.aggregate([{ $match: { _id: vessel[0].created_by } }, { $project: { _id: 1, name: 1, email: 1 } }]);

            return {
                ...vessel[0],
                user: users[0] || null,
            };
        } catch (error) {
            console.error("[VesselService.findById] Error:", error);
            throw error;
        }
    }

    async findByAssignedUnitId({ unitId }: { unitId: string }): Promise<IVessel | null> {
        try {
            if (!unitId) {
                throw new Error("unitId is required");
            }

            const vessel = await Vessel.findOne({ unit_id: unitId });

            return vessel;
        } catch (error: any) {
            throw new Error(error);
        }
    }

    async getAllAssignedUnitIds(): Promise<string[]> {
        try {
            const vessels = await Vessel.find({ unit_id: { $ne: null, $exists: true } }, { unit_id: 1, _id: 0 });
            return vessels
                .map((vessel) => vessel.unit_id)
                .filter((unitId): unitId is string => unitId !== null && unitId !== undefined && unitId.trim() !== "");
        } catch (error) {
            console.error("[VesselService.getAllAssignedUnitIds] Error:", error);
            throw new Error("Failed to fetch assigned unit IDs");
        }
    }

    async create({
        name,
        thumbnail_file,
        unit_id,
        is_active = true,
        created_by,
        region_group_id,
        home_port_location,
    }: {
        name: string;
        thumbnail_file?: IFile;
        unit_id?: string;
        is_active?: boolean;
        created_by: mongoose.Types.ObjectId;
        region_group_id: string;
        home_port_location?: [number, number];
    }): Promise<IVesselWithUserDetails> {
        try {
            if (unit_id && unit_id.trim() !== "") {
                const existingVessel = await Vessel.findOne({ unit_id });
                if (existingVessel) {
                    throw new Error("A vessel with this unit ID already exists");
                }
            }
            if (!region_group_id) {
                throw new Error("Region group ID is required");
            }

            let thumbnailS3Key = null;
            if (thumbnail_file && thumbnail_file.buffer) {
                const s3Key = await this.uploadVesselThumbnail(thumbnail_file);
                if (s3Key) {
                    thumbnailS3Key = s3Key;
                }
            }

            const vessel = await Vessel.create({
                name,
                thumbnail_s3_key: thumbnailS3Key?.thumbnail_s3_key || null,
                thumbnail_compressed_s3_key: thumbnailS3Key?.thumbnail_compressed_s3_key || null,
                unit_id: unit_id && unit_id.trim() !== "" ? unit_id.trim() : null,
                is_active,
                region_group_id: new mongoose.Types.ObjectId(region_group_id),
                home_port_location:
                    home_port_location && home_port_location.length === 2
                        ? {
                              type: "Point",
                              coordinates: home_port_location,
                          }
                        : null,
                created_by,
            });

            const result = await this.findById({ id: vessel._id.toString() });
            if (!result) {
                throw new Error("Failed to retrieve created vessel");
            }
            return result;
        } catch (error: any) {
            console.error("[VesselService.create] Error:", error);
            if (error.message.includes("already exists")) {
                throw error;
            }
            if (error.message.includes("Longitude/latitude is out of bounds")) {
                throw new Error("Location Coordinates are out of bounds");
            }
            throw new Error("Failed to create vessel");
        }
    }

    async update({
        id,
        name,
        thumbnail_file,
        unit_id,
        is_active,
        remove_thumbnail,
        region_group_id,
        home_port_location,
    }: {
        id: string;
        name?: string;
        thumbnail_file?: IFile;
        unit_id?: string;
        is_active?: boolean;
        remove_thumbnail?: string;
        region_group_id?: string;
        home_port_location?: [number, number];
    }): Promise<IVesselWithUserDetails> {
        try {
            if (!isValidObjectId(id)) {
                throw new Error("Invalid vessel ID");
            }

            if (unit_id && unit_id.trim() !== "") {
                const existingVessel = await Vessel.findOne({
                    unit_id: unit_id.trim(),
                    _id: { $ne: id },
                });
                if (existingVessel) {
                    throw new Error("A vessel with this unit ID already exists");
                }
            }

            const vessel = await Vessel.findById(id);
            if (!vessel) {
                throw new Error("Vessel not found");
            }
            if (region_group_id) {
                if (!isValidObjectId(region_group_id)) {
                    throw new Error("Invalid region group ID");
                }
                const regionGroup = await RegionGroup.findById(region_group_id);
                if (!regionGroup) {
                    throw new Error("Region group ID does not exist");
                }
                vessel.region_group_id = new mongoose.Types.ObjectId(region_group_id);
            }

            let thumbnailS3Key = undefined;

            if (thumbnail_file && thumbnail_file.buffer) {
                if (vessel.thumbnail_s3_key) {
                    await deleteFileFromS3(s3Config.buckets.assets, vessel.thumbnail_s3_key);
                }

                const s3Key = await this.uploadVesselThumbnail(thumbnail_file);
                if (s3Key) {
                    thumbnailS3Key = s3Key;
                } else {
                    thumbnailS3Key = null;
                }
            } else if (remove_thumbnail === "true") {
                if (vessel.thumbnail_s3_key) {
                    await deleteFileFromS3(s3Config.buckets.assets, vessel.thumbnail_s3_key);
                }
                if (vessel.thumbnail_compressed_s3_key) {
                    await deleteFileFromS3(s3Config.buckets.assets, vessel.thumbnail_compressed_s3_key);
                }
                thumbnailS3Key = null;
            }

            if (name) vessel.name = name;
            if (thumbnailS3Key !== undefined) {
                vessel.thumbnail_s3_key = thumbnailS3Key?.thumbnail_s3_key || null;
                vessel.thumbnail_compressed_s3_key = thumbnailS3Key?.thumbnail_compressed_s3_key || null;
            }
            if (unit_id !== undefined) vessel.unit_id = unit_id && unit_id.trim() !== "" ? unit_id.trim() : null;
            if (home_port_location !== undefined) {
                vessel.home_port_location =
                    home_port_location && home_port_location.length === 2
                        ? {
                              type: "Point",
                              coordinates: home_port_location,
                          }
                        : undefined;
            }
            if (typeof is_active !== "undefined") {
                vessel.is_active = is_active;

                if (!is_active) {
                    const vesselId = vessel._id;

                    await NotificationAlert.updateMany({ vessel_ids: vesselId }, { $pull: { vessel_ids: vesselId } });
                    await NotificationSummary.updateMany({ vessel_ids: vesselId }, { $pull: { vessel_ids: vesselId } });
                    await InviteToken.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                    await ApiKey.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                    await User.updateMany({ allowed_vessels: vesselId }, { $pull: { allowed_vessels: vesselId } });
                }
            }

            await vessel.save();

            const result = await this.findById({ id: vessel._id.toString() });
            if (!result) {
                throw new Error("Failed to retrieve created vessel");
            }
            return result;
        } catch (error: any) {
            console.error("[VesselService.update] Error:", error);
            if (error.message.includes("already exists") || error.message.includes("not found")) {
                throw error;
            }
            if (error.message.includes("Longitude/latitude is out of bounds")) {
                throw new Error("Location Coordinates are out of bounds");
            }
            throw new Error("Failed to update vessel");
        }
    }
}

const vesselService = new VesselService();

export default vesselService;
