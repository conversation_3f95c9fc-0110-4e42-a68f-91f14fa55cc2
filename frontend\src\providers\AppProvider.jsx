import { useEffect, useMemo, useState } from "react";
import { AppContext } from "../contexts/AppContext";
import { useJsApiLoader } from "@react-google-maps/api";
import { useTheme } from "@emotion/react";
import { useMediaQuery } from "@mui/material";
import environment from "../../environment";
import { defaultValues } from "../utils";
const API_KEY = environment.VITE_GOOGLE_MAPS_API_KEY;
const libraries = ["geometry", "places"];


/**********************************************************************************************
 * ⚠️⚠️⚠️  CRITICAL WARNING FOR GOOGLE CONTEXT USAGE  ⚠️⚠️⚠️
 *
 * DO NOT render any component that relies on the `google` context
 * without FIRST checking that `google` is NOT undefined or null.
 *
 * Example: See Map.jsx for proper usage.
 *
 * Failing to do this will cause runtime errors when the Google Maps API is not yet loaded,
 * espeically when user's internet is slow.
 *
 * Always use a guard like:
 *    if (!google) return <Skeleton />; // or similar fallback
 *
 * Adding a guard before rendering the entire component prevents adding unnecessary multiple guards 
 * within the component itself.
 *********************************************************************************************/

export const AppProvider = ({ children }) => {
    const [timezone, setTimezone] = useState(defaultValues.timezone);
    const [isTabActive, setIsTabActive] = useState(true);
    const [selectedVessel, setSelectedVessel] = useState(null);
    const [deviceHeight, setDeviceHeight] = useState(window.innerHeight);
    const [devMode, setDevMode] = useState(localStorage.getItem("devMode") && Number(localStorage.getItem("devMode")) ? true : false);
    const [showIDs, setShowIDs] = useState(() => {
        const stored = localStorage.getItem("showIDs");
        return stored === null ? false : stored === "true";
    });
    // const [showMGRS, setShowMGRS] = useState(() => {
    //     const savedPreference = localStorage.getItem("showMGRS");
    //     return savedPreference !== null ? JSON.parse(savedPreference) : true;
    // });
    const theme = useTheme();

    const { isLoaded: googleApiLoaded } = useJsApiLoader({
        id: "google-map-script",
        googleMapsApiKey: API_KEY,
        libraries,
    });

    const screenSize = {
        xs: useMediaQuery(theme.breakpoints.down("sm")),
        sm: useMediaQuery(theme.breakpoints.down("md")),
        md: useMediaQuery(theme.breakpoints.down("lg")),
        lg: useMediaQuery(theme.breakpoints.down("xl")),
    };

    const google = useMemo(() => googleApiLoaded && window.google, [googleApiLoaded]);
    const isMobile = screenSize.xs || screenSize.sm || screenSize.md ? true : false;

    useEffect(() => {
        localStorage.setItem("devMode", devMode ? 1 : 0);
    }, [devMode]);

    useEffect(() => {
        const handleResize = () => {
            setDeviceHeight(window.innerHeight);
        };
        const handleVisibilityChange = () => {
            if (document.hidden) {
                setIsTabActive(false);
            } else {
                setIsTabActive(true);
            }
        };
        window.addEventListener("resize", handleResize);
        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            window.removeEventListener("resize", handleResize);
            document.removeEventListener("visibilitychange", handleVisibilityChange);
        };
    }, []);

    return (
        <AppContext.Provider
            value={{
                timezone,
                setTimezone,
                google,
                isMobile,
                screenSize,
                selectedVessel,
                setSelectedVessel,
                deviceHeight,
                isTabActive,
                devMode,
                setDevMode,
                showIDs,
                setShowIDs,
            }}
        >
            {children}
        </AppContext.Provider>
    );
};
