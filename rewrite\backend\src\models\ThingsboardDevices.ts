import mongoose from "mongoose";
import db from "../modules/db";
import { IThingsboardDevice } from "../interfaces/Thingsboard";

const thingsboardDeviceSchema = new mongoose.Schema(
    {
        deviceId: { type: String, required: true, unique: true },
        dashboardId: { type: String, required: false },
        deviceName: { type: String, required: true },
        accessToken: { type: String, required: true },
    },
    { timestamps: true },
);

// Create a partial index that only applies to non-null dashboardId values
thingsboardDeviceSchema.index(
    { dashboardId: 1 },
    {
        unique: true,
        partialFilterExpression: { dashboardId: { $type: "string" } },
    },
);

const ThingsboardDevices = db.qmShared.model<IThingsboardDevice>("ThingsboardDevices", thingsboardDeviceSchema, "thingsboard_devices");

export default ThingsboardDevices;
