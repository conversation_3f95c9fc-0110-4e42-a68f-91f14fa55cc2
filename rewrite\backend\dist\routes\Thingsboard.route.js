"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const endpointIds_1 = require("../utils/endpointIds");
const auth_1 = __importDefault(require("../middlewares/auth"));
const validator_1 = require("../middlewares/validator");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const Thingsboard_service_1 = __importDefault(require("../services/Thingsboard.service"));
const functions_1 = require("../utils/functions");
const router = express_1.default.Router();
router.get("/devices", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_THINGSBOARD_DEVICES), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const devices = yield Thingsboard_service_1.default.getAllDevices();
        res.json(devices);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/device/:unitId", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_THINGSBOARD_DEVICE), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("unitId")
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const device = yield Thingsboard_service_1.default.getDeviceByUnitId(req.params.unitId);
        if (!device) {
            return res.status(404).json({ message: "Device not found" });
        }
        res.json(device);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/reset-dashboards", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.RESET_THINGSBOARD_DASHBOARDS), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        Thingsboard_service_1.default.resetDashboards();
        res.json({ message: "Dashboards reset successful" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
