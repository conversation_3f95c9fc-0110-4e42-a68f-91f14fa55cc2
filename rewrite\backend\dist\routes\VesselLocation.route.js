"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const pLimit_1 = __importDefault(require("../modules/pLimit"));
const mongoose_1 = __importStar(require("mongoose"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const db_1 = __importDefault(require("../modules/db"));
const compression_1 = __importDefault(require("compression"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const VesselLocation_service_1 = __importDefault(require("../services/VesselLocation.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.use((0, compression_1.default)());
router.post("/:vesselName", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_COORDINATES), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    // the below cannot be verified by test cases
    // param('vesselName').isString().notEmpty().withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("lastKnown")
        .customSanitizer((v) => Number(v))
        .isInt({ min: 0, max: 1 })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("startTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("endTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("excludeIds")
        .isArray()
        .bail()
        .customSanitizer((v) => v.map((id) => new mongoose_1.default.Types.ObjectId(id)))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const requestURL = req.get("Referer");
    const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { vesselName } = req.params;
        const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
        console.log(`/vesselLocations ${vesselName}`, startTimestamp, endTimestamp, lastKnown);
        if (endTimestamp && !startTimestamp) {
            return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
        }
        const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: vesselName });
        if (!vessel)
            return res.status(404).json({ message: "Vessel does not exist" });
        if (!(0, functions_1.canAccessVessel)(req, vessel)) {
            return res.status(403).json({ message: `Cannot access coordinates for '${vesselName}'` });
        }
        if (lastKnown) {
            // const lastLocation = await db.lookups.collection("last_locations_lookup").findOne({ vessel_id: vessel._id }, {
            //     projection: {
            //         _id: '$last_location_id',
            //         longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
            //         latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
            //         timestamp: '$data.timestamp',
            //         groundSpeed: '$data.groundSpeed',
            //         isStationary: '$data.isStationary'
            //     }
            // });
            const lastLocation = yield VesselLocation_service_1.default.findLastKnownLocation({
                vesselId: vessel._id,
                projection: {
                    _id: "$last_location_id",
                    longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                    latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                    timestamp: "$data.timestamp",
                    groundSpeed: "$data.groundSpeed",
                    isStationary: "$data.isStationary",
                },
            });
            console.log(`/vesselLocations lastKnown ${vesselName} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/vesselLocations lastKnown ${vesselName} received ${lastLocation ? 1 : 0} coordinates`);
            console.log(`/vesselLocations lastKnown ${vesselName} time taken to respond ${new Date().getTime() - ts}`);
            return res.json(lastLocation);
        }
        const collections = yield (0, functions_1.getLocationsCollections)(db_1.default.locationsOptimized, startTimestamp, endTimestamp);
        if (!collections.length)
            return res.status(400).json({ message: "No location collections found for the specified time range" });
        const query = {
            "metadata.onboardVesselId": new mongoose_1.default.Types.ObjectId(vessel._id),
        };
        if (startTimestamp) {
            const endTime = endTimestamp || Date.now();
            query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
        }
        if (excludeIds)
            query._id = { $nin: excludeIds };
        var locations = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed)
                return res.end();
            console.log(`/vesselLocations ${vesselName} querying DB`);
            const resultPromises = collections.map((collection) => {
                const cursor = collection.aggregate([
                    { $match: query },
                    {
                        $project: {
                            _id: 1,
                            timestamp: 1,
                            groundSpeed: 1,
                            isStationary: 1,
                            latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                            longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                        },
                    },
                ]);
                if (isSwagger) {
                    cursor.limit(20);
                }
                return cursor.toArray();
            });
            const allResults = yield Promise.all(resultPromises);
            const flattenedResults = allResults.flat();
            return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }));
        console.log(`/vesselLocations ${vesselName} time taken to query ${new Date().getTime() - ts}`);
        console.log(`/vesselLocations ${vesselName} received ${(Array.isArray(locations) && locations.length) || 1} coordinates`);
        console.log(`/vesselLocations ${vesselName} time taken to respond ${new Date().getTime() - ts}`);
        if (isClosed)
            return res.end();
        res.json(locations);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
router.get("/bulk", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_COORDINATES_BULK), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("vesselIds")
        .isString()
        .withMessage(`vesselIds is a required string`)
        .notEmpty()
        .withMessage(`vesselIds must be a comma-separated string`)
        .if((0, express_validator_1.query)("vesselIds").exists())
        .customSanitizer((v) => v.split(",").map((v) => v.trim()))
        .custom((v) => v.every((id) => (0, mongoose_1.isValidObjectId)(id)))
        .withMessage(`vesselIds must be valid object IDs`),
    (0, express_validator_1.query)("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    (0, express_validator_1.query)("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // const requestURL = req.get("Referer");
    // const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
        console.log(`/vesselLocations/bulk ${vesselIds}`, startTimestampISO, endTimestampISO);
        if (endTimestampISO && !startTimestampISO) {
            return res.status(400).json({ message: "startTimestampISO is required when endTimestamp is provided" });
        }
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vesselIds } });
        const assignedVessels = vessels.filter((vessel) => (0, functions_1.canAccessVessel)(req, vessel));
        const query = {};
        if (startTimestampISO) {
            const endTime = endTimestampISO || Date.now();
            query.timestamp = { $gt: new Date(startTimestampISO), $lt: new Date(endTime) };
        }
        const locations = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            // const allLocations = {};
            console.log(`/vesselLocations/bulk querying DB`);
            const ts = new Date().getTime();
            const vesselIds = assignedVessels.map((vessel) => vessel._id.toString());
            const dateRanges = (0, functions_1.splitByMonthsUTC)(startTimestampISO, endTimestampISO);
            const locations = (yield Promise.all(dateRanges.map((range) => __awaiter(void 0, void 0, void 0, function* () {
                return VesselLocation_service_1.default.findByDateRange({
                    dateRange: [range.start, range.end],
                    vesselIds,
                    projection: { _id: 1, location: 1, isStationary: 1, timestamp: 1, "metadata.onboardVesselId": 1 },
                });
            })))).flat();
            const groupedLocations = locations.reduce((acc, loc) => {
                const vesselId = loc.metadata.onboardVesselId.toString();
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push([loc._id, loc.timestamp, loc.location.coordinates[1], loc.location.coordinates[0], loc.isStationary]);
                return acc;
            }, {});
            assignedVessels.forEach((vessel) => {
                const vesselId = vessel._id.toString();
                if (!groupedLocations[vesselId]) {
                    groupedLocations[vesselId] = [];
                }
            });
            // await Promise.all(
            //     assignedVessels.map(async (vessel) => {
            //         const currentVesselId = vessel._id.toString();
            //         // const historicalUnitIds = getUnitIdsFromVessel(vessel);
            //         // if (historicalUnitIds.length === 0) {
            //         //     allLocations[currentVesselId] = [];
            //         //     return;
            //         // }
            //         // console.log(`/vesselLocations/bulk historicalUnitIds ${historicalUnitIds}`);
            //         const collections =[ db.locationsOptimized.collection(`2025-07`)];
            //         if (isClosed) return res.end();
            //         const vesselLocations = (
            //             await Promise.all(
            //                 collections.map(async (collection) => {
            //                     const ts = new Date().getTime();
            //                     const cursor = collection.find(
            //                         { ...query, 'metadata.onboardVesselId': new mongoose.Types.ObjectId(currentVesselId) },
            //                         {
            //                             projection: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 },
            //                         },
            //                     );
            //                     if (isSwagger) {
            //                         cursor.limit(20);
            //                     }
            //                     const result = (await cursor.toArray()).map(obj => ([obj._id, obj.timestamp, obj.latitude, obj.longitude, obj.isStationary]));
            //                     // const explain = await cursor.explain();
            //                     // console.log(explain);
            //                     console.log(
            //                         `/vesselLocations/bulk ${currentVesselId} time taken to query collection ${collection.name} ${new Date().getTime() - ts}`,
            //                     );
            //                     return result;
            //                 }),
            //             )
            //         )
            //             .flat()
            //             .sort((a, b) => new Date(a[1]).getTime() - new Date(b[1]).getTime());
            //         allLocations[currentVesselId] = vesselLocations;
            //     }),
            // );
            console.log(`/vesselLocations/bulk time taken to query ${new Date().getTime() - ts}`);
            return groupedLocations;
        }));
        // console.log(`/vesselLocations/bulk received ${(locations && locations.length) || 1} coordinates`);
        console.log(`/vesselLocations/bulk time taken to respond ${new Date().getTime() - ts}`);
        if (isClosed)
            return res.end();
        res.json(locations);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
router.get("/latest", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_COORDINATES_ALL_LATEST), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("vesselIds")
        .isString()
        .withMessage(`vesselIds is a required string`)
        .notEmpty()
        .withMessage(`vesselIds must be a comma-separated string`)
        .if((0, express_validator_1.query)("vesselIds").exists())
        .customSanitizer((v) => v.split(",").map((v) => v.trim()))
        .custom((v) => v.every((id) => (0, mongoose_1.isValidObjectId)(id)))
        .withMessage(`vesselIds must be valid object IDs`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const ts = new Date().getTime();
        const { vesselIds } = req.query;
        console.log(`/vesselLocations/latest ${vesselIds}`);
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vesselIds } });
        const assignedVessels = vessels.filter((vessel) => (0, functions_1.canAccessVessel)(req, vessel));
        const locations = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            console.log(`/vesselLocations/latest querying DB`);
            const ts = new Date().getTime();
            const lastLocations = yield VesselLocation_service_1.default.findLastKnownLocation({
                vesselIds: assignedVessels.map((vessel) => vessel._id),
                projection: {
                    _id: "$last_location_id",
                    location: "$data.location",
                    timestamp: "$data.timestamp",
                    groundSpeed: "$data.groundSpeed",
                    isStationary: "$data.isStationary",
                    unitId: "$data.metadata.unitId",
                    vessel_id: 1,
                },
            });
            console.log(`/vesselLocations/latest time taken to query ${new Date().getTime() - ts}`);
            return lastLocations;
        }));
        console.log(`/vesselLocations/latest time taken to respond ${new Date().getTime() - ts}`);
        res.json(locations);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: The ground speed of the vessel in knots
 *           example: 0.023
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the location data was recorded
 *           example: "2024-09-15T22:29:20.555Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */
/**
 * @swagger
 * /vesselLocations/{vesselName}:
 *   post:
 *     summary: Fetch vessel location data. (This route is deprecated, use v2 instead)
 *     description: Fetch vessel location data for a given vessel, with optional parameters for filtering by timestamp range, excluding specific IDs, and fetching the last known location.<br/>Rate limited to 20 requests every 5 seconds
 *     tags: [Vessel Locations]
 *     deprecated: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: vesselName
 *         required: true
 *         description: Name of the vessel to fetch location data for
 *         schema:
 *           type: string
 *           example: prototype-37
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 required: false
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           text/html:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/VesselLocation'
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */
router.post("/:vesselId/closest", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_COORDINATES_CLOSEST), auth_1.default, validator_1.validateData.bind(this, [(0, express_validator_1.body)("timestampISO").isISO8601().withMessage("timestampISO must be a valid ISO 8601 date")]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { vesselId } = req.params;
        const { timestampISO } = req.body;
        const vessel = yield Vessel_service_1.default.findById({ id: vesselId });
        if (!vessel)
            return res.status(404).json({ message: "Vessel does not exist" });
        if (!(0, functions_1.canAccessVessel)(req, vessel)) {
            return res.status(403).json({ message: "Cannot access coordinates for this vessel" });
        }
        const closestLocation = yield VesselLocation_service_1.default.findClosestLocation({
            vesselId,
            timestampISO,
            timeWindowMs: 60000,
        });
        if (!closestLocation) {
            return res.status(404).json({ message: "No coordinate found" });
        }
        res.json(closestLocation);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
