"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const functions_1 = require("../utils/functions");
const regionGroupSchema = new mongoose_1.default.Schema({
    name: { type: String, required: true, unique: true },
    timezone: { type: String, required: true },
    // unit_ids: { type: Array, required: true },
    // vessel_ids: [{ type: mongoose.Schema.Types.ObjectId, required: true }],
    created_by: { type: mongoose_1.default.Schema.Types.ObjectId, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});
// Normalize name before saving
regionGroupSchema.pre("save", function (next) {
    if (this.name) {
        this.name = (0, functions_1.normalizeName)(this.name);
    }
    next();
});
// Ensure case-insensitive uniqueness
regionGroupSchema.index({ name: 1 }, { unique: true, collation: { locale: "en", strength: 2 } });
const RegionGroup = db_1.default.qm.model("RegionGroup", regionGroupSchema, "regions_groups");
exports.default = RegionGroup;
