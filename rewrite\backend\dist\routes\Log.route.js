"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const functions_1 = require("../utils/functions");
const mongoose_1 = __importStar(require("mongoose"));
const permissions_1 = require("../utils/permissions");
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const SessionLog_1 = __importDefault(require("../models/SessionLog"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
const buildQuery = (req, sepSearch = false) => {
    const query = {};
    const sorting = req.query.sorting;
    const search = {};
    if (req.query.status) {
        if (req.query.status === "online") {
            query.disconnect_timestamp = { $eq: null };
        }
        else if (req.query.status === "offline") {
            query.disconnect_timestamp = { $ne: null };
        }
    }
    if (req.query.created_after) {
        const date = new Date(Number(req.query.created_after));
        query.connect_timestamp = { $gte: date };
    }
    if (req.query.full_name_or_browser_or_device) {
        if (sepSearch) {
            search.$or = [
                // Use $ifNull to prevent errors if fields are missing, and to make empty regex match
                { $regexMatch: { input: { $ifNull: ["$browser", ""] }, regex: req.query.full_name_or_browser_or_device, options: "i" } },
                { $regexMatch: { input: { $ifNull: ["$device", ""] }, regex: req.query.full_name_or_browser_or_device, options: "i" } },
                {
                    $regexMatch: {
                        input: { $ifNull: ["$user.name", ""] },
                        regex: req.query.full_name_or_browser_or_device,
                        options: "i",
                    },
                },
            ];
        }
        else {
            query.$or = [
                { browser: { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
                { device: { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
                { "user.name": { $regex: req.query.full_name_or_browser_or_device, $options: "i" } },
            ];
        }
    }
    if (sorting) {
        for (let key in sorting) {
            if (sorting[key] === "NONE") {
                delete sorting[key];
                continue;
            }
            sorting[key] = sorting[key] === "ASC" ? 1 : -1;
        }
    }
    return { query, sorting: sorting && Object.keys(sorting).length > 0 ? sorting : null, search };
};
router.get("/sessions", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_SESSION_LOGS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.viewSessionLogs]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let page = Math.max(1, Number(req.query.page) || 1);
        let limit = Math.min(50, Number(req.query.rowsPerPage) || 10);
        const { query, sorting } = buildQuery(req);
        const skip = (page - 1) * limit;
        const pipeline = [
            {
                $match: {
                    environment: process.env.NODE_ENV,
                },
            },
            {
                $project: {
                    user_id: 1,
                    connect_timestamp: 1,
                    device: 1,
                    browser: 1,
                    disconnect_timestamp: 1,
                },
            },
            {
                $sort: { connect_timestamp: -1 }, // Ensure most recent sessions come first
            },
            {
                $group: {
                    _id: "$user_id",
                    lastLog: { $first: "$$ROOT" }, // Pick the latest log per user
                },
            },
            {
                $replaceRoot: { newRoot: "$lastLog" }, // Flatten the structure
            },
            // Now join user info for only the latest log per user
            {
                $lookup: {
                    from: "users",
                    localField: "user_id",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                },
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                },
            },
        ];
        if (query && Object.keys(query).length > 0) {
            pipeline.push({ $match: query });
        }
        if (sorting) {
            pipeline.push({ $sort: sorting });
        }
        pipeline.push({
            $facet: {
                metadata: [{ $count: "total" }],
                data: [
                    { $skip: skip },
                    { $limit: limit },
                    {
                        $project: {
                            "user.password": 0,
                            "user.reset_password_token": 0,
                            "user.reset_password_expire": 0,
                        },
                    },
                ],
            },
        });
        pipeline.push({
            $project: {
                total: { $ifNull: [{ $arrayElemAt: ["$metadata.total", 0] }, 0] },
                data: "$data",
            },
        });
        const logs = (yield SessionLog_1.default.aggregate(pipeline, {
            collation: { locale: "en", caseFirst: "off" },
        }));
        const totalCount = (logs[0] && logs[0].total) || 0;
        const totalPages = Math.ceil(totalCount / limit);
        res.json({ logs: (logs[0] && logs[0].data) || [], totalPages, currentPage: page, totalCount });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/sessions/user", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_SESSION_LOGS_BY_USER), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.viewSessionLogs]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userId = req.query.userId;
        if (!userId) {
            return res.status(400).send({ message: "User ID is required" });
        }
        const { query } = buildQuery(req);
        const logs = yield SessionLog_1.default.aggregate([
            {
                $match: {
                    user_id: new mongoose_1.default.Types.ObjectId(userId),
                    environment: process.env.NODE_ENV,
                },
            },
            {
                $sort: { createdAt: -1 },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "user_id",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                },
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $match: query,
            },
            {
                $project: {
                    "user.password": 0,
                    "user.reset_password_token": 0,
                    "user.reset_password_expire": 0,
                },
            },
        ]);
        res.json(logs);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/sessions/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_SESSION_LOG_BY_ID), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.viewSessionLogs]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const logs = yield SessionLog_1.default.aggregate([
            {
                $match: {
                    _id: req.params.id,
                    environment: process.env.NODE_ENV,
                },
            },
            {
                $lookup: {
                    from: "users",
                    localField: "user_id",
                    foreignField: "_id",
                    as: "user",
                    pipeline: [{ $project: { name: 1, username: 1, _id: 0 } }],
                },
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    "user.password": 0,
                    "user.reset_password_token": 0,
                    "user.reset_password_expire": 0,
                },
            },
        ]);
        if (logs.length === 0)
            return res.status(404).send({ message: "Log does not exist with that id" });
        const log = logs[0];
        res.json(log);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
