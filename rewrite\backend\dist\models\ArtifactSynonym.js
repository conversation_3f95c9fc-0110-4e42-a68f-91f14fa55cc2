"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const ArtifactSynonymSchema = new mongoose_1.default.Schema({
    type: { type: String, required: true },
    word: { type: String, required: true },
    synonyms: { type: [String], required: true },
});
const ArtifactSynonym = db_1.default.qm.model("ArtifactSynonym", ArtifactSynonymSchema, "artifact_synonyms");
exports.default = ArtifactSynonym;
