import nodemailer from "nodemailer";
import { google } from "googleapis";

const oAuth2Client = new google.auth.OAuth2(process.env.CLIENT_ID, process.env.CLIENT_SECRET, process.env.REDIRECT_URI);

oAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });

async function sendEmail({ to, subject, html }: { to: string; subject: string; html: string }) {
    return new Promise(async (resolve, reject) => {
        try {
            const { token: accessToken } = await oAuth2Client.getAccessToken();

            const transport = nodemailer.createTransport({
                service: "gmail",
                auth: {
                    type: "OAuth2",
                    user: process.env.MAIL_USER,
                    clientId: process.env.CLIENT_ID,
                    clientSecret: process.env.CLIENT_SECRET,
                    refreshToken: process.env.REFRESH_TOKEN,
                    accessToken: accessToken,
                },
                connectionTimeout: 60000,
                socketTimeout: 60000,
            } as nodemailer.TransportOptions);

            await transport.sendMail({
                to,
                subject,
                html,
            });

            resolve("Email sent");
        } catch (err) {
            console.error(`Error sending email: ${err}`);
            reject(err);
        }
    });
}

export { sendEmail };
