"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_1 = __importDefault(require("express"));
const InAppNotification_1 = __importDefault(require("../models/InAppNotification"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const functions_1 = require("../utils/functions");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const db_1 = __importDefault(require("../modules/db"));
const awsS3_1 = require("../modules/awsS3");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_IN_APP_NOTIFICATIONS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("is_read")
        .optional()
        .isIn(["0", "1"])
        .withMessage((value, { path }) => `Invalid value ${value} provided for ${path}`)
        .toBoolean(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const page = parseInt(req.query.page) || 1;
        const pageSize = Math.min(parseInt(req.query.page_size) || 50, 50);
        const skip = (page - 1) * pageSize;
        const totalRecords = yield InAppNotification_1.default.countDocuments({ receiver: req.user._id });
        const inAppNotifications = yield InAppNotification_1.default.find({ receiver: req.user._id }).sort({ created_at: -1 }).limit(pageSize).skip(skip);
        const artifactIds = inAppNotifications.map((notification) => notification.artifact_id);
        const artifacts = yield db_1.default.qmai
            .collection("analysis_results")
            .find({ _id: { $in: artifactIds } })
            .toArray();
        const notificationsWithArtifacts = inAppNotifications
            .filter((notification) => notification.artifact_id && artifacts.some((artifact) => artifact._id.equals(notification.artifact_id)))
            .map((notification) => {
            const artifact = artifacts.find((artifact) => notification.artifact_id && artifact._id.equals(notification.artifact_id));
            const artifactWithUrls = Object.assign({}, artifact);
            if (artifact === null || artifact === void 0 ? void 0 : artifact.image_path) {
                artifactWithUrls.image_url = (0, awsS3_1.processBatchItem)({
                    bucketName: artifact.bucket_name,
                    key: artifact.image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact === null || artifact === void 0 ? void 0 : artifact.thumbnail_image_path) {
                artifactWithUrls.thumbnail_url = (0, awsS3_1.processBatchItem)({
                    bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                    key: artifact.thumbnail_image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            return Object.assign(Object.assign({}, notification.toObject()), { artifact_details: artifactWithUrls });
        });
        const totalPages = Math.ceil(totalRecords / pageSize);
        const nextPage = page < totalPages ? page + 1 : null;
        const previousPage = page > 1 ? page - 1 : null;
        res.json({
            data: notificationsWithArtifacts,
            pagination: {
                totalRecords,
                totalPages,
                currentPage: page,
                nextPage,
                previousPage,
            },
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/markRead/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .isMongoId()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const inAppNotification = yield InAppNotification_1.default.findByIdAndUpdate(req.params.id, { is_read: true }, { new: true });
        res.json({
            data: inAppNotification,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/bulkMarkRead", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("ids")
        .isArray()
        .withMessage((value, { path }) => {
        return `Invalid value ${value} provided for ${path}`;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { ids } = req.body;
    try {
        const inAppNotifications = yield InAppNotification_1.default.updateMany({ _id: { $in: ids } }, { is_read: true });
        res.json({
            data: inAppNotifications,
        });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
