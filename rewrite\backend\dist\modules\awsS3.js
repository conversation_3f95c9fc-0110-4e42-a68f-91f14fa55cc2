"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.s3Config = exports.s3 = void 0;
exports.getObjectStream = getObjectStream;
exports.checkKeyExists = checkKeyExists;
exports.buildThumbnailImage = buildThumbnailImage;
exports.uploadFileToS3 = uploadFileToS3;
exports.deleteFileFromS3 = deleteFileFromS3;
exports.getS3Object = getItemObject;
exports.getItemObjectPart = getItemObjectPart;
exports.getCloudfrontSignedUrl = getCloudfrontSignedUrl;
exports.generateS3FallbackUrl = generateS3FallbackUrl;
exports.processBatchItem = processBatchItem;
const aws_sdk_1 = __importDefault(require("aws-sdk"));
const sharp_1 = __importDefault(require("sharp"));
const db_1 = __importDefault(require("../modules/db"));
const node_stream_1 = require("node:stream");
const cloudfront_signer_1 = require("@aws-sdk/cloudfront-signer");
const client_cloudfront_1 = require("@aws-sdk/client-cloudfront");
const functions_1 = require("../utils/functions");
if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    throw new Error("AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY must be set in env variables");
}
const s3 = new aws_sdk_1.default.S3({
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});
exports.s3 = s3;
const cloudFrontClient = new client_cloudfront_1.CloudFrontClient({
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
    region: "us-east-1", // CloudFront is global but requires a region
});
const distributionCache = new Map();
const CACHE_REFRESH_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds
const s3Config = {
    buckets: {
        assets: {
            name: "quartermaster-assets",
            region: "us-east-1",
        },
        compressedItems: {
            name: process.env.AWS_COMPRESSED_ITEMS_BUCKET,
            region: process.env.AWS_COMPRESSED_ITEMS_REGION,
        },
    },
    settings: {
        signatureVersion: "v4",
    },
};
exports.s3Config = s3Config;
aws_sdk_1.default.config.update({
    maxRetries: 3,
    httpOptions: {
        timeout: 30000,
        connectTimeout: 10000,
    },
});
// const rawRegions = {
//     '': 'us-east-1',
//     'EU': 'eu-west-1'
// }
// const buckets = {}
const bucketKeys = {};
// s3.listBuckets().promise().then(({ Buckets }) => {
//     Promise.all(Buckets.map(b => (
//         s3.getBucketLocation({ Bucket: b.Name }).promise()
//     ))).then(locations => {
//         locations.forEach((loc, i) => {
//             const region = rawRegions[loc.LocationConstraint] || loc.LocationConstraint
//             const bucket = Buckets[i]
//             buckets[bucket.Name] = {
//                 ...bucket,
//                 region,
//                 object_url_prefix: `https://${bucket.Name}.s3.${region}.amazonaws.com/`
//             }
//         })
//     }).catch(console.error)
// }).catch(console.error)
// async function getBucketKeys(bucket) {
//     let isTruncated = true; // Flag to check if there are more keys to retrieve
//     let continuationToken; // Token for pagination
//     bucketKeys[bucket] = []
//     try {
//         while (isTruncated) {
//             // Fetch objects from S3 bucket
//             const params = {
//                 Bucket: bucket,
//                 MaxKeys: 1000, // Maximum number of keys to return (default is 1000)
//                 ContinuationToken: continuationToken, // Token for next set of results
//             };
//             const data = await s3.listObjectsV2(params).promise();
//             // Add keys to the array
//             data.Contents.forEach(item => bucketKeys[bucket].push(item.Key));
//             console.log(bucket, 'bucket length', bucketKeys[bucket].length)
//             // Check if there are more keys to fetch
//             isTruncated = data.IsTruncated;
//             continuationToken = data.NextContinuationToken; // Update token for the next batch
//         }
//         console.log(`Finished getting keys for bucket ${bucket}`)
//         return bucketKeys[bucket]; // Return all keys
//     } catch (error) {
//         console.error(`Error listing keys: ${error}`);
//         throw error;
//     }
// }
function getItemObject(bucketName_1, region_1, key_1) {
    return __awaiter(this, arguments, void 0, function* (bucketName, region, key, sync = false) {
        s3.config.update({ region });
        const params = {
            Bucket: bucketName,
            Key: key,
        };
        try {
            const obj = s3.getObject(params);
            return sync ? obj.promise() : obj; // No await here
        }
        catch (err) {
            console.error("Error getting object from S3:", err);
            throw err; // Re-throw to be handled by caller
        }
    });
}
function getItemObjectPart(req, res, bucketName, region, key) {
    return __awaiter(this, void 0, void 0, function* () {
        s3.config.update({ region });
        const params = {
            Bucket: bucketName,
            Key: key,
        };
        try {
            const headData = yield s3.headObject(params).promise();
            const fileSize = headData.ContentLength;
            const contentType = "video/mp4"; //headData.ContentType || "application/octet-stream";
            const etag = headData.ETag;
            res.setHeader("Accept-Ranges", "bytes");
            res.setHeader("Content-Type", contentType);
            const range = req.headers.range;
            let responseStatusCode = 200; // Default to 200 OK
            let responseHeaders = {
                "Content-Type": contentType,
                "Accept-Ranges": "bytes",
                "Content-Length": fileSize, // Default to full size
            };
            if (etag) {
                responseHeaders["ETag"] = etag;
            }
            if (range && fileSize) {
                const parts = range.replace(/bytes=/, "").split("-");
                const start = parseInt(parts[0], 10);
                const end = parts[1] ? parseInt(parts[1], 10) : start > 0 ? fileSize - 1 : Math.round(fileSize / 4); //fileSize - 1;
                if (start >= fileSize || end >= fileSize || start > end) {
                    console.warn(`v2: Invalid range requested: ${range} for file size ${fileSize}`);
                    res.setHeader("Content-Range", `bytes */${fileSize}`);
                    res.status(416).send("Range Not Satisfiable");
                    return;
                }
                const chunkSize = end - start + 1;
                params.Range = `bytes=${start}-${end}`; // Set Range for S3 GetObject
                console.log(`Serving S3 chunk: bytes ${start}-${end}/${fileSize}, size: ${chunkSize}`);
                responseStatusCode = 206; // 206 Partial Content
                responseHeaders["Content-Range"] = `bytes ${start}-${end}/${fileSize}`;
                responseHeaders["Content-Length"] = chunkSize;
            }
            else {
                console.log(`Serving full S3 file: ${key}, size: ${fileSize}`);
                // Headers already set for 200 OK defaults
            }
            console.log("v2: Calling s3.getObject(params).createReadStream()...");
            const s3Stream = s3.getObject(params).createReadStream();
            if (!(s3Stream instanceof node_stream_1.Readable)) {
                throw new Error("S3 getObject().createReadStream() did not return a readable stream.");
            }
            // Write status code and headers *before* piping
            res.writeHead(responseStatusCode, responseHeaders);
            // Abort S3 download if client disconnects (Same logic)
            res.on("close", () => {
                console.log(`Client disconnected for ${key}. Aborting S3 stream.`);
                // createReadStream returns a standard Node Readable stream which has destroy()
                if (typeof s3Stream.destroy === "function") {
                    s3Stream.destroy();
                }
                else {
                    console.warn("S3 stream does not have a destroy method.");
                }
            });
            // Handle errors during the S3 stream (Same logic)
            s3Stream.on("error", (streamErr) => {
                console.error(`v2: Error during S3 stream for ${key}:`, streamErr);
                if (!res.headersSent) {
                    // Check common v2 error codes
                    if (streamErr.code === "NoSuchKey") {
                        res.status(404).send("File not found in S3");
                    }
                    else if (streamErr.code === "AccessDenied") {
                        res.status(403).send("Access denied to S3 resource");
                    }
                    else {
                        res.status(500).send("Error streaming file from S3");
                    }
                }
                else {
                    res.end();
                }
            });
            // 7. Pipe the S3 stream to the client response (Same logic)
            console.log("v2: Piping S3 stream to response.");
            s3Stream.pipe(res);
        }
        catch (err) {
            console.error(`v2: Error processing S3 request for ${key}:`, err);
            if (!res.headersSent) {
                // Check common v2 error codes from headObject or initial getObject call
                if (err.code === "NotFound" || err.code === "NoSuchKey") {
                    // headObject might return NotFound
                    res.status(404).send("File not found in S3");
                }
                else if (err.code === "NoSuchBucket") {
                    res.status(500).send("Server configuration error (Bucket not found)");
                }
                else if (err.code === "AccessDenied") {
                    res.status(403).send("Access denied to S3 resource");
                }
                else {
                    res.status(500).send("Failed to retrieve file from S3");
                }
            }
            else {
                res.end();
            }
        }
    });
}
function getObjectStream(bucketName, region, key) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const result = yield getItemObject(bucketName, region, key, false);
            if ("createReadStream" in result) {
                return result.createReadStream();
            }
            else {
                throw new Error("Expected AWS Request object with createReadStream method");
            }
        }
        catch (err) {
            console.error("Error getting object from S3:", err);
            throw err; // Re-throw to be handled by caller
        }
    });
}
function checkKeyExists(bucket, key) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
            console.log("checkKeyExists has actually been called");
            const ts = new Date().getTime();
            try {
                if (!bucketKeys[bucket])
                    bucketKeys[bucket] = [];
                if (bucketKeys[bucket].includes(key)) {
                    resolve(true);
                }
                else {
                    yield s3.headObject({ Bucket: bucket, Key: key }).promise();
                    bucketKeys[bucket].push(key);
                    console.log(`added key ${key} to bucket ${bucket}. time taken ${new Date().getTime() - ts}`);
                    resolve(true);
                }
            }
            catch (err) {
                if (err.statusCode === 404)
                    resolve(false);
                else
                    reject(err);
            }
        }));
    });
}
/**
 * Uploads a file buffer to the specified AWS S3 bucket.
 *
 * @param {Object} file - The file object containing a buffer and metadata.
 * @param {Buffer} file.buffer - The file content as a Buffer.
 * @param {string} file.originalname - The original file name (used for extension).
 * @param {Object} bucketType - The bucket configuration object with name and region properties.
 * @param {string} bucketType.name - The S3 bucket name.
 * @param {string} bucketType.region - The AWS region for the bucket.
 * @param {string} filePath - The destination path/key in the S3 bucket.
 * @param {import("aws-sdk/clients/s3").PutObjectRequest} [options] - Optional settings for the upload.
 */
function uploadFileToS3(file_1, bucketType_1, filePath_1) {
    return __awaiter(this, arguments, void 0, function* (file, bucketType, filePath, options = {}) {
        try {
            if (!file || !file.buffer) {
                throw new Error("File buffer is required");
            }
            if (!bucketType || typeof bucketType !== "object") {
                throw new Error("bucketType must be an object with name and region properties");
            }
            if (!bucketType.name || !bucketType.region) {
                throw new Error("bucketType must have both name and region properties");
            }
            const { name: bucket, region } = bucketType;
            let finalPath = filePath;
            if (!filePath.includes(".")) {
                const timestamp = Date.now();
                const randomString = Math.random().toString(36).substring(2, 15);
                const extension = file.originalname ? file.originalname.split(".").pop() : "dat";
                finalPath = `${filePath}/${timestamp}-${randomString}.${extension}`;
            }
            s3.config.update({
                region: region,
                signatureVersion: s3Config.settings.signatureVersion,
            });
            const uploadParams = Object.assign({ Bucket: bucket, Key: finalPath, Body: file.buffer, ContentType: file.mimetype || "application/octet-stream" }, options);
            const result = yield s3.upload(uploadParams).promise();
            return result.Key;
        }
        catch (error) {
            console.error("[awsS3.uploadFileToS3] Error:", error);
            throw new Error(`Failed to upload file to S3: ${error.message}`);
        }
    });
}
function deleteFileFromS3(bucketType, key) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            if (!key) {
                console.log("[awsS3.deleteFileFromS3] No key provided, skipping deletion");
                return;
            }
            if (!bucketType || typeof bucketType !== "object") {
                throw new Error("bucketType must be an object with name and region properties");
            }
            if (!bucketType.name || !bucketType.region) {
                throw new Error("bucketType must have both name and region properties");
            }
            const { name: bucket, region } = bucketType;
            s3.config.update({
                region: region,
                signatureVersion: s3Config.settings.signatureVersion,
            });
            const deleteParams = {
                Bucket: bucket,
                Key: key,
            };
            yield s3.deleteObject(deleteParams).promise();
            console.log(`[awsS3.deleteFileFromS3] Successfully deleted file: ${key} from bucket: ${bucket}`);
        }
        catch (error) {
            console.error("[awsS3.deleteFileFromS3] Error:", error);
            console.warn(`[awsS3.deleteFileFromS3] Failed to delete file ${key}, but continuing with database update`);
        }
    });
}
function buildThumbnailImage(bucketName, region, key, unitName) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const result = yield getItemObject(bucketName, region, key, true);
            const originalBuffer = result.Body;
            const resizedBuffer = yield (0, sharp_1.default)(originalBuffer).resize(null, 320).jpeg({ quality: 100 }).toBuffer();
            const keyParts = key.split("/");
            const name = keyParts.pop();
            const uploadKey = `images/${unitName}/${name}`;
            const { name: bucket, region: bucketRegion } = s3Config.buckets.compressedItems;
            if (!bucket || !bucketRegion) {
                throw new Error("Compressed items bucket configuration is missing");
            }
            s3.config.update({ region: bucketRegion });
            const putObjectParams = {
                Bucket: bucket,
                Key: uploadKey,
                Body: resizedBuffer,
                ContentType: "image/jpeg",
            };
            const uploadResult = yield s3.putObject(putObjectParams).promise();
            if (uploadResult.ETag && uploadResult.ETag.length > 0) {
                yield db_1.default.qmai.collection("analysis_results").updateMany({ image_path: key }, { $set: { thumbnail_image_path: uploadKey } });
                return {
                    createReadStream: () => node_stream_1.Readable.from(resizedBuffer),
                    ContentLength: resizedBuffer.length,
                    ContentType: "image/jpeg",
                };
            }
            else {
                return undefined;
            }
        }
        catch (error) {
            console.error("Error processing image:", error);
            throw error;
        }
    });
}
const CLOUDFRONT_KEY_PAIR_ID = process.env.CLOUDFRONT_KEY_PAIR_ID;
const CLOUDFRONT_PRIVATE_KEY = process.env.CLOUDFRONT_PRIVATE_KEY;
if (!CLOUDFRONT_KEY_PAIR_ID || !CLOUDFRONT_PRIVATE_KEY) {
    throw new Error("[awsS3.js] CloudFront configuration missing in .env");
}
function refreshDistributionCache() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log("[awsS3] Refreshing CloudFront distribution cache...");
            const command = new client_cloudfront_1.ListDistributionsCommand({});
            const response = yield cloudFrontClient.send(command);
            if (response.DistributionList && response.DistributionList.Items) {
                distributionCache.clear();
                for (const distribution of response.DistributionList.Items) {
                    if (distribution.Origins && distribution.Origins.Items) {
                        for (const origin of distribution.Origins.Items) {
                            if (origin.DomainName && origin.DomainName.includes(".s3.")) {
                                const bucketName = origin.DomainName.split(".s3.")[0];
                                const distributionDomain = `https://${distribution.DomainName}/`;
                                distributionCache.set(bucketName, distributionDomain);
                                console.log(`[awsS3] Mapped bucket '${bucketName}' to distribution '${distributionDomain}'`);
                            }
                        }
                    }
                }
                console.log(`[awsS3] Distribution cache refreshed with ${distributionCache.size} mappings`);
            }
        }
        catch (error) {
            console.error("[awsS3] Error refreshing distribution cache:", error);
        }
        finally {
            setTimeout(refreshDistributionCache, CACHE_REFRESH_INTERVAL);
        }
    });
}
function getCloudfrontSignedUrl({ fileName, bucketName }) {
    if (!fileName) {
        throw new Error("fileName is required");
    }
    if (!bucketName) {
        throw new Error("bucketName is required");
    }
    let distributionUrl = null;
    const cachedDomain = distributionCache.get(bucketName);
    if (cachedDomain) {
        distributionUrl = cachedDomain;
        // console.log(`[awsS3] Using cached distribution domain for bucket '${bucketName}': ${cachedDomain}`);
    }
    else {
        // console.warn(`[awsS3] No distribution found for bucket '${bucketName}', returning null for fallback`);
        return null;
    }
    const signedUrl = (0, cloudfront_signer_1.getSignedUrl)({
        url: distributionUrl + fileName,
        keyPairId: CLOUDFRONT_KEY_PAIR_ID,
        privateKey: CLOUDFRONT_PRIVATE_KEY,
        dateLessThan: new Date(Date.now() + 3600 * 1000),
    });
    // console.log("signedUrl", signedUrl);
    return signedUrl;
}
function generateS3FallbackUrl(bucketName, key, region) {
    const s3Region = region || "us-east-1";
    s3.config.update({ region: s3Region, signatureVersion: "v4" });
    const fileType = key.split(".").pop();
    if (!fileType)
        throw new Error("fileType not found");
    const ResponseContentType = (0, functions_1.getContentTypeFromFileExtension)(fileType);
    return s3.getSignedUrl("getObject", {
        Bucket: bucketName,
        Key: key,
        Expires: 3600,
        ResponseContentType,
    });
}
function processBatchItem({ bucketName, key, region }) {
    try {
        const cloudfrontFileName = bucketName.includes(s3Config.buckets.assets.name) ||
            (s3Config.buckets.compressedItems.name && bucketName.includes(s3Config.buckets.compressedItems.name))
            ? key
            : bucketName + "/" + region + "/" + key;
        const signedUrl = getCloudfrontSignedUrl({ fileName: cloudfrontFileName, bucketName });
        if (signedUrl === null) {
            return { bucketName, key, signedUrl: generateS3FallbackUrl(bucketName, key, region) };
        }
        return { bucketName, key, signedUrl };
    }
    catch (error) {
        console.error(`[S3 Batch] Error for ${bucketName}/${key}:`, error);
        return { bucketName, key, signedUrl: generateS3FallbackUrl(bucketName, key, region) };
    }
}
(() => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield refreshDistributionCache();
    }
    catch (error) {
        console.error("[awsS3] Failed to initialize CloudFront distribution cache:", error);
    }
}))();
// db.qmai.once('open', async () => {
//     try {
//         const buckets = []
//         const result = await db.qmai.collection('artifact_processor_results').find().toArray()
//         result.forEach(artifact => {
//             if (!buckets.includes(artifact.bucket_name))
//                 buckets.push(artifact.bucket_name)
//         })
//         console.log(`getting keys for buckets ${buckets}`)
//         buckets.forEach(bucket => {
//             getBucketKeys(bucket)
//         })
//     } catch (err) {
//         console.log(`(FATAL ERROR) in S3 module ${err?.message || err}`)
//         console.error('(FATAL ERROR) in S3 module', err)
//     }
// })
setInterval(() => {
    (0, functions_1.consoleLogObjectSize)(distributionCache, "awsS3.distributionCache");
}, 60000); // 1 minute
