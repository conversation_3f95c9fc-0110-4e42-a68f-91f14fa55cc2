const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const { vesselLocationsList } = require('../data/VesselLocations');
const db = require('../../modules/db');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Vessel Locations API', () => {

    describe('POST /api/vesselLocations/:vesselName', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/vesselLocations/vesselName');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if lastKnown is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ lastKnown: 'invalid' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if startTimestamp is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ startTimestamp: 'invalid' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if endTimestamp is invalid', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ endTimestamp: 'invalid' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if excludeIds not an array', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ excludeIds: 'invalid' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 400 if vesselName does not exist', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.locations.collection.mockReturnValueOnce(null);

                    const res = await request(app)
                        .post('/api/vesselLocations/invalidVessel')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch last known location successfully', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

                    db.locations.collection.mockReturnValueOnce({ findOne: jest.fn().mockResolvedValue(vesselLocationsList[0]) });

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ lastKnown: 1 })
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    ['_id', 'latitude', 'longitude', 'timestamp'].forEach(prop => {
                        expect(res.body).toHaveProperty(prop);
                    });
                });

                it('should return 200 and fetch all locations within the specified timestamp range', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.locations.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(vesselLocationsList)
                        })
                    });

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ startTimestamp: 1609459200, endTimestamp: 1609459400, excludeIds: ['66e7739280f445d4f062db31'] })
                        .set('Authorization', authToken)
                        .set('Referer', '/docs');

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'latitude', 'longitude', 'timestamp'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should return 200 and fetch limited locations for swagger', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.locations.collection.mockReturnValue({
                        findOne: jest.fn().mockReturnValue(null)
                    });

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ lastKnown: 1 })
                        .set('Authorization', authToken)
                        .set('Referer', '/docs');

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    db.locations.collection.mockReturnValueOnce({
                        findOne: jest.fn().mockRejectedValueOnce(new Error('Something went wrong'))
                    });

                    const res = await request(app)
                        .post('/api/vesselLocations/testVessel')
                        .send({ lastKnown: 1 })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
