import mongoose from "mongoose";

export interface IStreamTags {
    Name: string;
    Thumbnail: string | null;
}

export interface IStreamInfo {
    StreamName: string;
    StreamARN?: string;
    DeviceName?: string;
    MediaType?: string;
    KmsKeyId?: string;
    Version?: string;
    Status?: string;
    CreationTime?: string;
    DataRetentionInHours?: number;
    Tags?: IStreamTags;
    IsLive?: boolean;
    Region?: string;
    RegionGroupId?: string;
    VesselId?: string;
    VesselIsActive?: boolean;
    ThumbnailS3Key?: string;
    VesselName?: string;
}

export interface IDataEndpoint {
    DataEndpoint: string;
}

export interface IClipResponse {
    Payload: Buffer;
    ContentType: string;
}

export interface IFragment {
    ServerTimestamp: string;
    ProducerTimestamp: string;
    FragmentNumber: string;
    FragmentSizeInBytes: number;
}

export interface IStreamServiceInfo {
    unit_id: string;
    name: string | null;
    thumbnail: string | null;
    region: string;
    is_live: boolean;
    timezone: string | null;
    region_group_id: mongoose.Types.ObjectId | string | null;
}
