import express from "express";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import isAuthenticated from "../middlewares/auth";
import { validateData } from "../middlewares/validator";
import { body } from "express-validator";
import mongoose from "mongoose";
import { addFavouriteArtifact, deleteFavouriteArtifact, getUserFavouriteArtifacts } from "../services/ArtifactFavourites.service";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("artifact_id")
                    .isString()
                    .withMessage("artifact_id must be a string")
                    .notEmpty()
                    .withMessage("artifact_id must not be an empty string")
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage("artifact_id must be a valid object id"),
            ],
            req,
            res,
            next,
        ),
    addFavouriteArtifact,
);

// there is currently no usecase for this endpoint
// router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_FAVOURITE_ARTIFACTS), isAuthenticated, getAllFavouriteArtifacts);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_USER_FAVOURITE_ARTIFACTS), isAuthenticated, getUserFavouriteArtifacts);

router.delete(
    "/",
    assignEndpointId.bind(this, endpointIds.DELETE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("artifact_id")
                    .isString()
                    .withMessage("artifact_id must be a string")
                    .notEmpty()
                    .withMessage("artifact_id must not be an empty string")
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage("artifact_id must be a valid object id"),
            ],
            req,
            res,
            next,
        ),
    deleteFavouriteArtifact,
);

export default router;
