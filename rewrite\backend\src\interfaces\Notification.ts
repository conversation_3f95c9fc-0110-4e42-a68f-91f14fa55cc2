import mongoose from "mongoose";

// Base interface for notification preferences
export interface INotificationPreference {
    preference: "daily" | "weekly" | "monthly";
    receivers: string[];
    title: string[];
    is_enabled: boolean;
    created_at: Date;
    updated_at: Date;
    created_by: mongoose.Types.ObjectId;
}

export interface INotificationAlert extends INotificationPreference {
    _id: mongoose.Types.ObjectId;
    super_category: string[];
    sub_category: string[];
    country_flags: string[];
    type: "email" | "app" | "both" | "not given";
    unit_id?: string[];
    vessel_ids: (string | mongoose.Types.ObjectId)[];
}

export interface INotificationSummary extends INotificationPreference {
    _id: mongoose.Types.ObjectId;
    unit_id?: string[];
    vessel_ids: (string | mongoose.Types.ObjectId)[];
}
