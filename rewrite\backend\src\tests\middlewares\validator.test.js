const { validationResult, query, body } = require("express-validator");
const { validateData } = require("../../middlewares/validator");

// jest.mock('express-validator', () => ({
//     validationResult: jest.fn(),
// }));

describe('validateData middleware', () => {
    let req, res, next;

    beforeEach(() => {
        req = {};
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
        };
        next = jest.fn();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should call next if there are no validation errors', async () => {
        // Mock no validation errors
        // validationResult.mockReturnValue({
        //     isEmpty: () => true,
        // });
        req.body = {
            streamName: 'prototype-33',
            region: 'ap-southeast-1'
        };

        const rules = [
            body('streamName').isString().notEmpty(),
            body('region').isString().notEmpty(),
        ];

        console.log('before validate data')
        await validateData(rules, req, res, next);
        console.log('after validate data')

        expect(next).toHaveBeenCalled();
        expect(res.status).not.toHaveBeenCalled();
        expect(res.json).not.toHaveBeenCalled();
    });

    it('should respond with 400 if there are validation errors', async () => {
        // Mock validation errors

        req.body = {
            streamName: '',
            region: 123
        };

        const rules = [
            body('streamName').isString().notEmpty(),
            body('region').isString().notEmpty(),
        ];

        await validateData(rules, req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalled();
        expect(next).not.toHaveBeenCalled();
    });
});
