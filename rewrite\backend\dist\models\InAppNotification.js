"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const InAppNotificationSchema = new mongoose_1.default.Schema({
    title: { type: String, required: true },
    message: { type: String, required: true },
    receiver: { type: mongoose_1.default.Schema.Types.ObjectId, ref: "User", required: true },
    is_read: { type: Boolean, default: false },
    artifact_id: { type: mongoose_1.default.Schema.Types.ObjectId, required: false },
    created_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});
const InAppNotification = db_1.default.qm.model("InAppNotification", InAppNotificationSchema, "in_app_notifications");
exports.default = InAppNotification;
