"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupTimeout = exports.stopCleanupTimeout = exports.startCleanupTimeout = exports.otpStore = exports.cleanupExpiredOtps = exports.verifyOtp = exports.sendOtp = void 0;
const Email_1 = require("../utils/Email");
let otpStore = [];
exports.otpStore = otpStore;
const OTP_EXPIRATION_TIME = 10 * 60 * 1000;
let cleanupTimeout = null;
exports.cleanupTimeout = cleanupTimeout;
const generateOtp = () => ({
    otp: Math.floor(100000 + Math.random() * 900000),
    expiresAt: Date.now() + OTP_EXPIRATION_TIME,
});
const startCleanupTimeout = (useTimer = setTimeout) => {
    if (!cleanupTimeout) {
        exports.cleanupTimeout = cleanupTimeout = useTimer(() => {
            if (otpStore.length === 0) {
                stopCleanupTimeout();
            }
            cleanupExpiredOtps();
        }, 60000);
    }
};
exports.startCleanupTimeout = startCleanupTimeout;
const stopCleanupTimeout = (useClearTimeout = clearTimeout) => {
    if (cleanupTimeout) {
        useClearTimeout(cleanupTimeout);
        exports.cleanupTimeout = cleanupTimeout = null;
    }
};
exports.stopCleanupTimeout = stopCleanupTimeout;
const cleanupExpiredOtps = () => {
    const now = Date.now();
    exports.otpStore = otpStore = otpStore.filter((entry) => entry.expiresAt >= now);
};
exports.cleanupExpiredOtps = cleanupExpiredOtps;
const sendOtp = (email, name, sendEmail) => __awaiter(void 0, void 0, void 0, function* () {
    const { otp, expiresAt } = generateOtp();
    otpStore.push({ email, otp, expiresAt });
    const date = new Date();
    const content = (0, Email_1.OTP_EMAIL_CONTENT)(otp, name, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
    try {
        yield sendEmail({
            to: email,
            subject: "Your OTP Code",
            html: content,
        });
        startCleanupTimeout();
        return { message: "OTP sent successfully" };
    }
    catch (error) {
        console.error(`Failed to send OTP to ${email}:`, error);
        throw new Error("Failed to send OTP");
    }
});
exports.sendOtp = sendOtp;
const verifyOtp = (email, otp) => {
    const otpEntryIndex = otpStore.findIndex((entry) => entry.email === email && entry.otp === otp);
    if (otpEntryIndex === -1) {
        return { valid: false, message: "Invalid OTP or email" };
    }
    const otpEntry = otpStore[otpEntryIndex];
    if (otpEntry.expiresAt < Date.now()) {
        otpStore.splice(otpEntryIndex, 1);
        return { valid: false, message: "OTP expired" };
    }
    otpStore.splice(otpEntryIndex, 1);
    return { valid: true };
};
exports.verifyOtp = verifyOtp;
