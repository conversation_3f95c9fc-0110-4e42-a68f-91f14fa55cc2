import mongoose from "mongoose";
import db from "../modules/db";
import { getSimplifiedCoords, getLocationsCollections } from "../utils/functions";
import { IClosestLocation, ILocationOptimized, ILastLocation } from "../interfaces/VesselLocation";

class VesselLocationService {
    async findByDateRange({
        dateRange,
        vesselIds,
        projection,
    }: {
        dateRange: [string, string];
        vesselIds: string[];
        projection: Record<string, number | string | object>;
    }): Promise<ILocationOptimized[]> {
        const start = new Date(dateRange[0]);
        const end = new Date(dateRange[1]);
        console.log("dateRange", dateRange);
        if (start.getUTCMonth() !== end.getUTCMonth()) throw new Error("Date range must be within the same month");

        const isoSplit = start.toISOString().split("-");
        const yearMonth = isoSplit[0] + "-" + isoSplit[1];

        console.log("querying collection", `${yearMonth}`);

        const query = {
            timestamp: { $gte: start, $lte: end },
            "metadata.onboardVesselId": { $in: vesselIds.map((id: string) => new mongoose.Types.ObjectId(id)) },
        };

        console.log("query", query);

        let cursor;

        const optimizedCollectionExists = (await db.locationsOptimized.db.listCollections({ name: `${yearMonth}` }).toArray()).length;
        if (optimizedCollectionExists) {
            cursor = db.locationsOptimized.collection(`${yearMonth}`).find(query, { projection });
        } else {
            cursor = db.locationsRaw.collection(`${yearMonth}`).find(query, { projection });
        }

        cursor.hint({ timestamp: 1, "metadata.onboardVesselId": 1 });
        cursor.batchSize(this.calculateOptimalBatchSize(dateRange, optimizedCollectionExists));

        const ts = new Date().getTime();

        let locations: ILocationOptimized[] = [];

        for await (const doc of cursor) {
            locations.push(doc as ILocationOptimized);
        }

        console.log(
            `time taken to query DB ${optimizedCollectionExists ? db.locationsOptimized.name : db.locationsRaw.name} and collection ${yearMonth}: ${new Date().getTime() - ts}ms`,
        );

        if (!optimizedCollectionExists) {
            console.log("optimizing ungrouped coords");
            console.log("prev locations.length", locations.length);
            locations = this.optimizeUngroupedCoords(locations);
            console.log("post locations.length", locations.length);
        }

        return locations;
    }

    async findLastKnownLocation({
        vesselId,
        vesselIds,
        projection,
    }: {
        vesselId?: string | mongoose.Types.ObjectId;
        vesselIds?: (string | mongoose.Types.ObjectId)[];
        projection: Record<string, string | number | object>;
    }): Promise<ILastLocation | Record<string, ILastLocation | null> | null> {
        if (vesselId) {
            const targetVesselId = typeof vesselId === "string" ? new mongoose.Types.ObjectId(vesselId) : vesselId;
            const lastLocation = await db.lookups.collection("last_locations_lookup").findOne({ vessel_id: targetVesselId }, { projection });
            return lastLocation;
        } else if (vesselIds) {
            if (!projection.vessel_id) throw new Error("vessel_id must be provided in projection");
            const targetVesselIds = vesselIds.map((vesselId: string | mongoose.Types.ObjectId) =>
                typeof vesselId === "string" ? new mongoose.Types.ObjectId(vesselId) : vesselId,
            );
            const lastLocations = await db.lookups
                .collection("last_locations_lookup")
                .find({ vessel_id: { $in: targetVesselIds } }, { projection })
                .toArray();
            const groupedLastLocations: Record<string, ILastLocation | null> = lastLocations.reduce(
                (acc: Record<string, ILastLocation | null>, lastLocation: any) => {
                    acc[lastLocation.vessel_id.toString()] = lastLocation as ILastLocation;
                    return acc;
                },
                {},
            );
            // Ensure all requested vessels are included in response
            targetVesselIds.forEach((vId: mongoose.Types.ObjectId) => {
                const vesselId = vId.toString();
                if (!groupedLastLocations[vesselId]) {
                    groupedLastLocations[vesselId] = null;
                }
            });
            return groupedLastLocations;
        } else {
            throw new Error("Either vesselId or vesselIds must be provided");
        }
    }

    calculateOptimalBatchSize(dateRange: [string, string], optimizedCollectionExists: number): number {
        let batchPerDay = 2000;

        if (optimizedCollectionExists) {
            batchPerDay = 1000;
        }

        const timeDiffDays = (new Date(dateRange[1]).getTime() - new Date(dateRange[0]).getTime()) / (1000 * 60 * 60 * 24);

        console.log("timeDiffDays", timeDiffDays);

        const batchSize = Math.floor(timeDiffDays * batchPerDay);

        console.log("batchSize", batchSize);

        return batchSize;
    }

    optimizeUngroupedCoords(locationsSorted: ILocationOptimized[]): ILocationOptimized[] {
        const groupedLocations: Record<string, ILocationOptimized[]> = locationsSorted.reduce(
            (acc: Record<string, ILocationOptimized[]>, loc: ILocationOptimized) => {
                const vesselId = loc.metadata.onboardVesselId.toString();
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push(loc);
                return acc;
            },
            {},
        );

        const optimizedGroupedLocations = Object.values(groupedLocations)
            .flatMap((locations) => getSimplifiedCoords(locations))
            .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        return optimizedGroupedLocations as ILocationOptimized[];
    }

    async findClosestLocation({
        vesselId,
        timestampISO,
        timeWindowMs = 60000,
    }: {
        vesselId: string;
        timestampISO: string;
        timeWindowMs?: number;
    }): Promise<IClosestLocation | null> {
        try {
            const targetTimestamp = new Date(timestampISO).getTime();
            const collections = await getLocationsCollections(db.locationsRaw, targetTimestamp - timeWindowMs, targetTimestamp + timeWindowMs);

            if (collections.length === 0) {
                return null;
            }

            const searchQuery = {
                "metadata.onboardVesselId": new mongoose.Types.ObjectId(vesselId),
                timestamp: {
                    $gte: new Date(targetTimestamp - timeWindowMs),
                    $lte: new Date(targetTimestamp + timeWindowMs),
                },
            };

            const results = await Promise.all(
                collections.map((collection: mongoose.Collection) =>
                    collection
                        .aggregate([
                            { $match: searchQuery },
                            { $addFields: { timeDiff: { $abs: { $subtract: [{ $toLong: "$timestamp" }, targetTimestamp] } } } },
                            { $sort: { timeDiff: 1 } },
                            { $limit: 1 },
                            {
                                $project: {
                                    _id: 1,
                                    timestamp: 1,
                                    groundSpeed: 1,
                                    isStationary: 1,
                                    latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                                    longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                                },
                            },
                        ])
                        .toArray(),
                ),
            );

            const flatResults = results.flat();
            const closestLocation =
                flatResults.length > 0 && flatResults[0].latitude && flatResults[0].longitude ? (flatResults[0] as IClosestLocation) : null;

            return closestLocation;
        } catch (error) {
            console.error("[VesselService.findClosestLocation] Error:", error);
            throw new Error("Failed to find closest location");
        }
    }
}

const vesselLocationService = new VesselLocationService();

export default vesselLocationService;
