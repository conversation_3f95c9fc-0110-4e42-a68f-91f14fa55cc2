import { Readable } from "node:stream";
import { <PERSON><PERSON>pi<PERSON>ey } from "src/interfaces/ApiKey";
import { IArtifact } from "src/interfaces/Artifact";
import { IPermission } from "src/interfaces/Permission";
import { IAuthUser } from "src/interfaces/User";
import { IVessel } from "src/interfaces/Vessel";
import jwt from "jsonwebtoken";
import { permissions } from "./permissions";
import JSZip from "jszip";
import simplify from "simplify-js";
import mongoose from "mongoose";
import { IQueryFilter } from "src/interfaces/Common";

const isURL = (value: string) => {
    const urlRegex = /^(ftp|http|https):\/\/[^ "]+$/;

    if (urlRegex.test(value)) return true;

    return false;
};

const isBase64 = (value: string) => {
    const base64Regex = /^(data:image\/[a-zA-Z]+;base64,){1}[^\s]+$/;

    if (base64Regex.test(value)) return true;

    return false;
};

const isBase64OrURL = (value: string | string[]) => {
    if (Array.isArray(value)) {
        if (value.every((v) => isBase64(v) || isURL(v))) {
            return true;
        }
    } else {
        if (isBase64(value) || isURL(value)) {
            return true;
        }
    }

    return false;
};

const isIntStrict = (value: string | number) => {
    if (typeof value !== "number" || !Number.isInteger(value)) return false;
    else return true;
};

const validateError = (err: any, res: any) => {
    if (err.name === "MongoServerError") {
        if (err.code === 11000) {
            res.status(400).json({
                message: `Value already exists: ${Object.keys(err.keyValue)
                    .map((key) => `${key} = ${err.keyValue[key]}`)
                    .join(", ")}`,
            });
        } else {
            console.error(err);
            res.status(500).json({ message: `Unexpected error occured: ${err?.message || JSON.stringify(err)}` });
        }
    } else if (err.code === "ResourceNotFoundException" || err.message === "No streams found in the specified timestamp range.") {
        res.status(404).json({ message: err.message });
    } else {
        console.error(err);
        res.status(500).json({ message: `Unexpected error occured: ${err?.message || JSON.stringify(err)}` });
    }
};

// the coordinates must be sorted by timestamp in ascending order
const getSessionsByCoordinates = (coordinates: { timestamp: string | number }[]) => {
    const sessions = [];

    let currentSession = [coordinates[0]];

    for (let i = 1; i < coordinates.length; i++) {
        if (new Date(coordinates[i].timestamp).getTime() - new Date(coordinates[i - 1].timestamp).getTime() < 300000) {
            currentSession.push(coordinates[i]);
        } else {
            if (currentSession.length > 1) sessions.push(currentSession);
            currentSession = [coordinates[i]];
        }
    }

    // Add the last session
    if (currentSession.length && currentSession.length > 1) {
        sessions.push(currentSession);
    }

    return sessions;
};

function generateInvitationLink(
    email: string,
    role_id: string,
    allowed_vessels: string[],
    admin_id: string,
    role: string,
    organization_name: string,
    organization_id: string,
) {
    const payload = { email, role_id, allowed_vessels, admin_id, role, organization_name, organization_id, timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    if (!secretKey) throw new Error("JWT_SECRET is not set");
    const token = jwt.sign(payload, secretKey, { expiresIn: "72h" });
    return token;
}

function generateUnsubscribeToken(email: string, notification_id: string) {
    const payload = { email, notification_id, timestamp: Date.now() };
    const secretKey = process.env.JWT_SECRET;
    if (!secretKey) throw new Error("JWT_SECRET is not set");
    const token = jwt.sign(payload, secretKey, { expiresIn: "90d" });
    return token;
}

const UNALLOWED_REGION_GROUPS = ["681c253f9f43051a7748b2c1"];
function canAccessVessel(
    { user, api_key }: { user?: Pick<IAuthUser, "permissions" | "allowed_vessels">; api_key?: Pick<IApiKey, "allowed_vessels"> },
    { _id: vessel_id, is_active, region_group_id }: IVessel,
) {
    if (!vessel_id) throw new Error("vessel _id is required");
    if (is_active === undefined) throw new Error("vessel is_active is required");
    if (!user && !api_key) throw new Error("User or api_key is a required object");

    // console.log("canAccessVessel", vessel_id, is_active);

    if (user) {
        if (!user.permissions || !Array.isArray(user.permissions)) throw new Error("User permissions is a required array");
        if (userHasPermissions(user, [permissions.accessAllVessels])) return true;
    }

    if (!is_active) return false;

    if (api_key && (!region_group_id || (region_group_id && UNALLOWED_REGION_GROUPS.includes(region_group_id.toString())))) {
        return false;
    }

    const requester = user || api_key;

    if (!requester) throw new Error("User or api_key is a required object");

    if (!requester.allowed_vessels || !Array.isArray(requester.allowed_vessels)) throw new Error("allowed_vessels is a required array");

    const allowedVessels = requester.allowed_vessels;

    // console.log("allowedVessels", allowedVessels);
    // console.log("vessel_id", vessel_id);

    const isAllowed = allowedVessels.find((v) => v.toString() === vessel_id.toString()) ? true : false;

    return isAllowed;
}

const generateZip = function (filesData: { name: string; content: Buffer | string }[]) {
    const zip = new JSZip();

    filesData.forEach((file) => {
        zip.file(removeSpecialCharsFromFilename(file.name), file.content);
    });

    return zip;
    //return await zip.generateAsync({ type: "blob" }); //.then(function(content) {
};

const fileNameTimestamp = () => {
    const date = new Date();

    return `${date.getUTCFullYear()}${date.getUTCMonth() + 1}${date.getUTCDate()}_${date.getUTCHours()}${date.getUTCMinutes()}${date.getUTCSeconds()}`;
};

const removeSpecialCharsFromFilename = function (filename: string) {
    const regex = /[^\w.\-_\p{L}\p{N}]/gu;
    const cleanedFilename = filename.replace(regex, "");
    const trimmedFilename = cleanedFilename.replace(/^[.\-_]+|[.\-_]+$/g, "");
    const normalizedFilename = trimmedFilename.replace(/([.\-_])\1+/g, "$1");
    if (!normalizedFilename) {
        return "unnamed_file";
    }

    return normalizedFilename;
};

const userHasPermissions = ({ permissions }: { permissions: IPermission[] }, permissionIds: number[], op = "AND") => {
    if (!permissions || !Array.isArray(permissions)) throw new Error("User permissions is a required array");
    if (!permissionIds || !Array.isArray(permissionIds)) throw new Error("Permission IDs is a required array");

    if (op === "AND") {
        return permissionIds.every((p_id) => permissions.find((p) => p.permission_id === p_id));
    } else {
        return permissionIds.some((p_id) => permissions.find((p) => p.permission_id === p_id));
    }
};

const streamToBuffer = async (readStream: Readable) => {
    return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];

        readStream.on("data", (chunk) => {
            chunks.push(chunk);
        });

        readStream.on("end", () => {
            resolve(Buffer.concat(chunks).buffer);
        });

        readStream.on("error", (err) => {
            reject(err);
        });
    });
};

async function getStaticMapOld(
    markers: string[] = [],
    center: { latitude: number; longitude: number } | null = null,
    format = "png",
): Promise<{ mimeType: string; source: Response } | {}> {
    try {
        const baseUrl = "https://maps.googleapis.com/maps/api/staticmap";

        const args: Record<string, string> = {
            // zoom: "3",
            scale: "2",
            size: "800x400",
            maptype: "terrain", // satellite // roadmap // hybrid
            format: format || "png",
            key: process.env.GOOGLE_API_KEY as string,
        };

        // set center point for map or use auto adjust of the borders basing on the markers
        if (center) {
            args.center = `${center.latitude},${center.longitude}`;
        } else {
            args.auto = "";
        }

        const params = new URLSearchParams(args);

        const url = `${baseUrl}?${params.toString()}&${markers.map((marker) => `markers=${encodeURIComponent(marker)}`).join("&")}`;
        const response = await fetch(url);

        if (!response.ok) {
            // Handle HTTP errors (e.g., 400, 403, 500)
            const errorText = await response.text(); // Get error message from response
            throw new Error(`Static Map API request failed: ${response.status} - ${errorText}`);
        }
        return {
            mimeType: "image/png",
            source: response,
        };
    } catch (error) {
        console.error("Error fetching static map:", error);
        return {};
    }
}
// https://staticmapmaker.com/google/

// sizes: tiny / small / mid /
// colors: black, brown, green, purple, yellow, blue, gray, orange, red, white or hex representation
function buildStaticMarkerSignature(latitude: number, longitude: number, label = "1", size = "small", color = "red") {
    return `size:${size}|color:${color}|label:${label}|${latitude}, ${longitude}`;
    // icon: XXX
}

function generateTimeSeries(startTimestamp: number, endTimestamp: number, interval = 1) {
    let start = new Date(startTimestamp).setSeconds(0, 0);
    let end = new Date(endTimestamp).setSeconds(0, 0);
    const result: Record<string, number> = {};

    let current = new Date(start);
    while (current.getTime() <= end) {
        result[current.toISOString()] = 0;
        current.setUTCMinutes(current.getUTCMinutes() + interval);
    }

    return result;
}

const escapeRegExp = (str: string) => {
    return str.replace(/[-[\]/{}()*+?.\\^$|]/g, "\\$&");
};

const getUnitIdsFromVessel = (vessel: IVessel) => {
    if (!vessel) throw new Error("Vessel is required");
    if (!vessel.units_history || !Array.isArray(vessel.units_history)) throw new Error("Vessel units_history is required and must be an array");
    const unitIds = vessel.units_history.map((v) => v.unit_id).filter(Boolean);
    const removeDuplicates = [...new Set(unitIds)];
    return removeDuplicates;
};

function getContentTypeFromFileExtension(fileExtension: string) {
    const fileType = fileExtension;
    if (
        fileType === "jpg" ||
        fileType === "jpeg" ||
        fileType === "png" ||
        fileType === "gif" ||
        fileType === "bmp" ||
        fileType === "tiff" ||
        fileType === "ico" ||
        fileType === "webp"
    ) {
        return `image/${fileType}`;
    } else if (
        fileType === "mp4" ||
        fileType === "mov" ||
        fileType === "avi" ||
        fileType === "wmv" ||
        fileType === "flv" ||
        fileType === "mkv" ||
        fileType === "webm"
    ) {
        return `video/${fileType}`;
    }
    return "application/octet-stream";
}
// This is used for capitalizing names in the database for group region name
function normalizeName(name: string) {
    return name.trim().replace(/\s+/g, " ");
}

function findVesselByUnitHistory(vessels: IVessel[], from: number | Date, to: number | Date, unit_id: string) {
    const fromTs = typeof from === "number" ? from : new Date(from).getTime();
    const toTs = typeof to === "number" ? to : new Date(to).getTime();
    for (const vessel of vessels) {
        if (!Array.isArray(vessel.units_history)) continue;
        for (const history of vessel.units_history) {
            const historyFrom = typeof history.mount_timestamp === "number" ? history.mount_timestamp : new Date(history.mount_timestamp).getTime();
            const historyTo =
                history.unmount_timestamp === undefined || history.unmount_timestamp === null
                    ? null
                    : typeof history.unmount_timestamp === "number"
                      ? history.unmount_timestamp
                      : new Date(history.unmount_timestamp).getTime();
            if (history.unit_id === unit_id && historyFrom <= toTs && (historyTo === null || historyTo >= fromTs)) {
                return { vessel, unit_id: history.unit_id };
            }
        }
    }
    return null;
}

const groupByImage = (artifacts: IArtifact[]) => {
    const uniqueArtifactsMap = new Map();
    if (artifacts && artifacts.length > 0) {
        artifacts.forEach((artifact) => {
            if (artifact.image_path && !uniqueArtifactsMap.has(artifact.image_path)) {
                uniqueArtifactsMap.set(artifact.image_path, { ...artifact, duplications: [] });
            } else if (artifact.image_path && uniqueArtifactsMap.has(artifact.image_path)) {
                const existingArtifact = uniqueArtifactsMap.get(artifact.image_path);
                if (existingArtifact && existingArtifact.duplications) {
                    existingArtifact.duplications.push(artifact);
                }
            }
        });
    }
    return Array.from(uniqueArtifactsMap.values());
};

const groupArtifactsByDuplicateIndex = (artifacts: IArtifact[], threshold = 0.7) => {
    const processed = new Set();
    const groups = [];

    for (let i = 0; i < artifacts.length; i++) {
        const current = artifacts[i];
        const currentId = current._id.toString();

        if (processed.has(currentId)) continue;

        const group = [currentId];
        processed.add(currentId);

        const vesselId = current.onboard_vessel_id.toString();
        if (current.portal?.duplication_index == null) continue;
        if (current.portal.duplication_index === 0 || current.portal.duplication_index < threshold) {
            groups.push(group);
            continue;
        }

        for (let j = i + 1; j < artifacts.length; j++) {
            const next = artifacts[j];
            const nextId = next._id.toString();
            if (processed.has(nextId) || next.onboard_vessel_id.toString() !== vesselId) continue;
            if (!next.portal?.duplication_index || next.portal.duplication_index === 0) break;
            group.push(nextId);
            processed.add(nextId);
            if (next.portal.duplication_index < threshold) break;
        }
        groups.push(group);
    }

    return groups;
};

function splitByMonthsUTC(startDate: string, endDate: string) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const ranges = [];
    let current = new Date(Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), start.getUTCDate()));

    while (current <= end) {
        // Start of the range
        const rangeStart =
            current.getTime() === Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), start.getUTCDate())
                ? start
                : new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth(), 1, 0, 0, 0, 0));

        // End of the month in UTC
        const monthEnd = new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth() + 1, 0, 23, 59, 59, 999));

        // End of the range (clip to given end)
        const rangeEnd = monthEnd > end ? end : monthEnd;

        ranges.push({
            start: rangeStart.toISOString(),
            end: rangeEnd.toISOString(),
        });

        // Move to the 1st of the next month in UTC
        current = new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth() + 1, 1));
    }

    return ranges;
}

type SimplifiedPoint = {
    x: number;
    y: number;
    _index: number;
};

const getSimplifiedCoords = (coords: { location: { coordinates: [number, number] } }[]) => {
    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.location.coordinates[0],
        y: c.location.coordinates[1],
        _index: i, // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true) as SimplifiedPoint[];

    // Step 3: Map back to original objects
    const result = simplified.map((p) => coords[p._index]);

    return result;
};

function cleanSuggestion(str: string) {
    return str
        .replace(/[^a-zA-Z0-9\s]/g, " ") // only letters, numbers, spaces
        .replace(/\s+/g, " ") // collapse whitespace
        .trim();
}

const getMonthsFromDateRangeUTC = (startDate: string | number, endDate: string | number) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const months = [];
    let current = new Date(Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), 1));

    while (current <= end) {
        // Format as YYYY-MM for collection name
        const year = current.getUTCFullYear();
        const month = String(current.getUTCMonth() + 1).padStart(2, "0");
        months.push(`${year}-${month}`);

        // Move to the 1st of the next month in UTC
        current = new Date(Date.UTC(current.getUTCFullYear(), current.getUTCMonth() + 1, 1));
    }

    return months;
};

const getLocationsCollections = async (locations: mongoose.Connection, startTimestamp: string | number, endTimestamp: string | number) => {
    if (!locations || !locations.db) throw new Error("Invalid locations provided");
    const listCollections = (await locations.db.listCollections().toArray())
        .filter((c: { name: string }) => !c.name.startsWith("system.") && !isNaN(new Date(c.name).getTime()))
        .map((c: { name: string }) => c.name)
        .sort();

    if (startTimestamp && endTimestamp) {
        const start = new Date(startTimestamp);
        const end = new Date(endTimestamp);

        if (isNaN(start.getTime()) || isNaN(end.getTime())) throw new Error("startTimestamp and endTimestamp must be valid dates");
        if (start > end) throw new Error("startTimestamp must be before endTimestamp");

        const months = getMonthsFromDateRangeUTC(startTimestamp, endTimestamp);
        const collections = [];

        for (const month of months) {
            const collectionExists = listCollections.includes(month);
            if (collectionExists) collections.push(locations.collection(month));
        }

        return collections;
    } else {
        return listCollections.map((c: string) => locations.collection(c));
    }
};

function consoleLogObjectSize(obj: IQueryFilter, label = "Object") {
    const size = Buffer.byteLength(JSON.stringify(obj), "utf8") / (1024 * 1024);
    console.log(`[CACHE_METRICS] ${label} size=${size.toFixed(2)} MB`);
}

export {
    isURL,
    isBase64,
    isBase64OrURL,
    validateError,
    isIntStrict,
    getSessionsByCoordinates,
    generateInvitationLink,
    generateUnsubscribeToken,
    canAccessVessel,
    generateZip,
    removeSpecialCharsFromFilename,
    fileNameTimestamp,
    userHasPermissions,
    streamToBuffer,
    getStaticMapOld,
    buildStaticMarkerSignature,
    generateTimeSeries,
    escapeRegExp,
    getUnitIdsFromVessel,
    getContentTypeFromFileExtension,
    normalizeName,
    findVesselByUnitHistory,
    groupArtifactsByDuplicateIndex,
    cleanSuggestion,
    splitByMonthsUTC,
    getSimplifiedCoords,
    getLocationsCollections,
    consoleLogObjectSize,
    groupByImage,
};
