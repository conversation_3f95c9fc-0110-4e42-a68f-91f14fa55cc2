"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const User_route_1 = __importDefault(require("./User.route"));
const Artifact_route_1 = __importDefault(require("./Artifact.route"));
const NotificationSummary_route_1 = __importDefault(require("./NotificationSummary.route"));
const Kinesis_route_1 = __importDefault(require("./Kinesis.route"));
const VesselLocation_route_1 = __importDefault(require("./VesselLocation.route"));
const router = express_1.default.Router();
router.use("/users", User_route_1.default);
router.use("/artifacts", Artifact_route_1.default);
router.use("/summaryReports", NotificationSummary_route_1.default);
router.use("/kinesis", Kinesis_route_1.default);
router.use("/vesselLocations", VesselLocation_route_1.default);
exports.default = router;
