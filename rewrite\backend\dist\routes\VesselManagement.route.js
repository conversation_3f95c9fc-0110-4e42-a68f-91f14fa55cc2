"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const restrictEndpointByUser_1 = __importDefault(require("../middlewares/restrictEndpointByUser"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const permissions_1 = require("../utils/permissions");
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const Stream_service_1 = __importDefault(require("../services/Stream.service"));
const multerConfig_1 = require("../middlewares/multerConfig");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_PAGINATED_VESSEL_MANAGEMENT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("page").optional().isInt({ min: 1 }).toInt(),
    (0, express_validator_1.query)("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
    (0, express_validator_1.query)("search").optional().isString().trim(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { page = 1, limit = 10, search = "" } = req.query;
        const result = yield Vessel_service_1.default.fetchPaginated({ page, limit, search });
        res.json(result);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/unitIds", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_VESSEL_UNIT_IDS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("regions")
        .isString()
        .optional()
        .customSanitizer((v) => v.split(",").map((v) => v.trim())),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { regions } = req.query;
        const vesselsList = yield Stream_service_1.default.fetchAll({ regions });
        const unitDetails = vesselsList.map((vessel) => ({
            unit_id: vessel.unit_id,
            name: vessel.name,
            region: vessel.region,
        }));
        res.json(unitDetails);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/assignedUnitIds", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ASSIGNED_UNIT_IDS), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const assignedUnitIds = yield Vessel_service_1.default.getAllAssignedUnitIds();
        res.json(assignedUnitIds);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/all", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ALL_VESSEL_MANAGEMENT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const vessels = yield Vessel_service_1.default.find();
        res.json(vessels);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_VESSEL_MANAGEMENT_BY_ID), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), validator_1.validateData.bind(this, [(0, express_validator_1.param)("id").isMongoId().withMessage("Invalid vessel ID")]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const vessel = yield Vessel_service_1.default.findById({ id });
        if (!vessel) {
            return res.status(404).json({ message: "Vessel not found" });
        }
        res.json(vessel);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_VESSEL_MANAGEMENT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), restrictEndpointByUser_1.default, multerConfig_1.upload.single("thumbnail_file"), multerConfig_1.handleMulterError, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("name").isString().trim().notEmpty().withMessage("Name is required and must be a non-empty string"),
    (0, express_validator_1.body)("unit_id").optional().isString().withMessage("Unit ID must be a string"),
    (0, express_validator_1.body)("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
    (0, express_validator_1.body)("region_group_id").isMongoId().withMessage("Region group ID must be a valid Object ID"),
    (0, express_validator_1.body)("home_port_location")
        .optional()
        .customSanitizer((value) => {
        if (!value)
            return null;
        try {
            return JSON.parse(value);
        }
        catch (error) {
            console.error("Error parsing home port location:", error);
            throw new Error("Invalid home port coordinates format");
        }
    })
        .isArray({ min: 2, max: 2 })
        .withMessage("Home port location must be an array with exactly 2 numbers"),
    (0, express_validator_1.body)("home_port_location.*").optional().isFloat().withMessage("Home port location must be valid numbers"),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, unit_id, is_active, region_group_id, home_port_location, } = req.body;
        const created_by = req.user._id;
        const thumbnail_file = req.file;
        const vessel = yield Vessel_service_1.default.create({
            name,
            thumbnail_file,
            unit_id,
            is_active,
            created_by,
            region_group_id,
            home_port_location,
        });
        res.status(201).json(vessel);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.put("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_VESSEL_MANAGEMENT), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageVessels]), restrictEndpointByUser_1.default, multerConfig_1.upload.single("thumbnail_file"), multerConfig_1.handleMulterError, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id").isMongoId().withMessage("Invalid vessel ID"),
    (0, express_validator_1.body)("name").optional().isString().trim().notEmpty().withMessage("Name must be a non-empty string"),
    (0, express_validator_1.body)("unit_id").optional().isString().withMessage("Unit ID must be a string"),
    (0, express_validator_1.body)("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
    (0, express_validator_1.body)("remove_thumbnail").optional().isBoolean().withMessage("remove_thumbnail must be a boolean"),
    (0, express_validator_1.body)("region_group_id").optional().isMongoId().withMessage("Region group ID must be a valid Object ID"),
    (0, express_validator_1.body)("home_port_location")
        .optional({ nullable: true })
        .customSanitizer((value) => {
        if (value === null || value === undefined || value === "")
            return null;
        try {
            return typeof value === "string" ? JSON.parse(value) : value;
        }
        catch (error) {
            console.error("Error parsing home port location:", error);
            throw new Error("Invalid home port coordinates format");
        }
    })
        .custom((value) => {
        if (value === null)
            return true;
        if (!Array.isArray(value)) {
            throw new Error("Home port location must be an array");
        }
        if (value.length !== 2) {
            throw new Error("Home port location must be an array with exactly 2 numbers");
        }
        if (!value.every((num) => typeof num === "number" && !isNaN(num))) {
            throw new Error("Home port location must contain valid numbers");
        }
        return true;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { name, unit_id, is_active, remove_thumbnail, region_group_id, home_port_location, } = req.body;
        const thumbnail_file = req.file;
        const vessel = yield Vessel_service_1.default.update({
            id,
            name,
            thumbnail_file,
            unit_id,
            is_active,
            remove_thumbnail: remove_thumbnail ? "true" : undefined,
            region_group_id,
            home_port_location: home_port_location || undefined,
        });
        res.json(vessel);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
