import rateLimit from "express-rate-limit";
import express, { Request, Response } from "express";
import isAuthenticated from "../middlewares/auth";
import TourGuide from "../models/TourGuide";
import { validateError } from "../utils/functions";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import { validateData } from "../middlewares/validator";
import { body } from "express-validator";
import mongoose from "mongoose";
import { ITourGuide } from "../interfaces/TourGuide";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_TOUR_GUIDE), isAuthenticated, async (req: Request, res: Response) => {
    try {
        const tourGuide: ITourGuide[] = await TourGuide.find({ user_id: req.user._id });
        res.json(tourGuide);
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_TOUR_GUIDE),
    isAuthenticated,
    validateData.bind(this, [
        body("maps")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("streams")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("events")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("notifications")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body().custom((value, { req }) => {
            if (Object.keys(req.body).length === 0) {
                throw new Error("At least one field must be provided");
            }
            return true;
        }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { _id: user_id } = req.user._id as mongoose.Types.ObjectId;
            const tourGuide = await TourGuide.findOne({
                user_id: user_id,
            });
            if (tourGuide) {
                return res.status(409).json({ message: `Tour Guide already exists for this user` });
            }
        } catch (err) {
            validateError(err, res);
        }
        try {
            const { maps, streams, events, notifications } = req.body;
            const { _id: user_id } = req.user._id as mongoose.Types.ObjectId;
            await TourGuide.create({ user_id, maps, streams, events, notifications });
            res.status(201).json({ message: `Tour Guide created successfully` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/",
    assignEndpointId.bind(this, endpointIds.UPDATE_TOUR_GUIDE),
    isAuthenticated,
    validateData.bind(this, [
        body("maps")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("streams")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("events")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("notifications")
            .optional()
            .isBoolean()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body().custom((value, { req }) => {
            if (Object.keys(req.body).length === 0) {
                throw new Error("At least one field must be provided");
            }
            return true;
        }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { maps, streams, events, notifications } = req.body;

            const { _id: user_id } = req.user._id as mongoose.Types.ObjectId;
            const tourGuide = await TourGuide.findOneAndUpdate({ user_id: user_id }, { maps, streams, events, notifications }, { new: true });
            res.json({ message: `Tour Guide has been updated`, tourGuide });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
