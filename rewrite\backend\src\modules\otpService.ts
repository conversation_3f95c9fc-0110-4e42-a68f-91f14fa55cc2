import { OTP_EMAIL_CONTENT } from "../utils/Email";

interface IOtpStore {
    email: string;
    otp: number;
    expiresAt: number;
}

let otpStore: IOtpStore[] = [];
const OTP_EXPIRATION_TIME = 10 * 60 * 1000;
let cleanupTimeout: null | NodeJS.Timeout = null;

const generateOtp = () => ({
    otp: Math.floor(100000 + Math.random() * 900000),
    expiresAt: Date.now() + OTP_EXPIRATION_TIME,
});

const startCleanupTimeout = (useTimer = setTimeout) => {
    if (!cleanupTimeout) {
        cleanupTimeout = useTimer(() => {
            if (otpStore.length === 0) {
                stopCleanupTimeout();
            }
            cleanupExpiredOtps();
        }, 60000);
    }
};

const stopCleanupTimeout = (useClearTimeout = clearTimeout) => {
    if (cleanupTimeout) {
        useClearTimeout(cleanupTimeout);
        cleanupTimeout = null;
    }
};

const cleanupExpiredOtps = () => {
    const now = Date.now();
    otpStore = otpStore.filter((entry) => entry.expiresAt >= now);
};

const sendOtp = async (email: string, name: string, sendEmail: typeof import("./email").sendEmail) => {
    const { otp, expiresAt } = generateOtp();
    otpStore.push({ email, otp, expiresAt });
    const date = new Date();
    const content = OTP_EMAIL_CONTENT(otp, name, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
    try {
        await sendEmail({
            to: email,
            subject: "Your OTP Code",
            html: content,
        });
        startCleanupTimeout();
        return { message: "OTP sent successfully" };
    } catch (error) {
        console.error(`Failed to send OTP to ${email}:`, error);
        throw new Error("Failed to send OTP");
    }
};

const verifyOtp = (email: string, otp: number) => {
    const otpEntryIndex = otpStore.findIndex((entry) => entry.email === email && entry.otp === otp);

    if (otpEntryIndex === -1) {
        return { valid: false, message: "Invalid OTP or email" };
    }

    const otpEntry = otpStore[otpEntryIndex];
    if (otpEntry.expiresAt < Date.now()) {
        otpStore.splice(otpEntryIndex, 1);
        return { valid: false, message: "OTP expired" };
    }

    otpStore.splice(otpEntryIndex, 1);
    return { valid: true };
};

export { sendOtp, verifyOtp, cleanupExpiredOtps, otpStore, startCleanupTimeout, stopCleanupTimeout, cleanupTimeout };
