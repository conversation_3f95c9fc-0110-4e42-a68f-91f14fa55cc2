"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const functions_1 = require("../utils/functions");
class VesselLocationService {
    findByDateRange(_a) {
        return __awaiter(this, arguments, void 0, function* ({ dateRange, vesselIds, projection, }) {
            var _b, e_1, _c, _d;
            const start = new Date(dateRange[0]);
            const end = new Date(dateRange[1]);
            console.log("dateRange", dateRange);
            if (start.getUTCMonth() !== end.getUTCMonth())
                throw new Error("Date range must be within the same month");
            const isoSplit = start.toISOString().split("-");
            const yearMonth = isoSplit[0] + "-" + isoSplit[1];
            console.log("querying collection", `${yearMonth}`);
            const query = {
                timestamp: { $gte: start, $lte: end },
                "metadata.onboardVesselId": { $in: vesselIds.map((id) => new mongoose_1.default.Types.ObjectId(id)) },
            };
            console.log("query", query);
            let cursor;
            const optimizedCollectionExists = (yield db_1.default.locationsOptimized.db.listCollections({ name: `${yearMonth}` }).toArray()).length;
            if (optimizedCollectionExists) {
                cursor = db_1.default.locationsOptimized.collection(`${yearMonth}`).find(query, { projection });
            }
            else {
                cursor = db_1.default.locationsRaw.collection(`${yearMonth}`).find(query, { projection });
            }
            cursor.hint({ timestamp: 1, "metadata.onboardVesselId": 1 });
            cursor.batchSize(this.calculateOptimalBatchSize(dateRange, optimizedCollectionExists));
            const ts = new Date().getTime();
            let locations = [];
            try {
                for (var _e = true, cursor_1 = __asyncValues(cursor), cursor_1_1; cursor_1_1 = yield cursor_1.next(), _b = cursor_1_1.done, !_b; _e = true) {
                    _d = cursor_1_1.value;
                    _e = false;
                    const doc = _d;
                    locations.push(doc);
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_e && !_b && (_c = cursor_1.return)) yield _c.call(cursor_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
            console.log(`time taken to query DB ${optimizedCollectionExists ? db_1.default.locationsOptimized.name : db_1.default.locationsRaw.name} and collection ${yearMonth}: ${new Date().getTime() - ts}ms`);
            if (!optimizedCollectionExists) {
                console.log("optimizing ungrouped coords");
                console.log("prev locations.length", locations.length);
                locations = this.optimizeUngroupedCoords(locations);
                console.log("post locations.length", locations.length);
            }
            return locations;
        });
    }
    findLastKnownLocation(_a) {
        return __awaiter(this, arguments, void 0, function* ({ vesselId, vesselIds, projection, }) {
            if (vesselId) {
                const targetVesselId = typeof vesselId === "string" ? new mongoose_1.default.Types.ObjectId(vesselId) : vesselId;
                const lastLocation = yield db_1.default.lookups.collection("last_locations_lookup").findOne({ vessel_id: targetVesselId }, { projection });
                return lastLocation;
            }
            else if (vesselIds) {
                if (!projection.vessel_id)
                    throw new Error("vessel_id must be provided in projection");
                const targetVesselIds = vesselIds.map((vesselId) => typeof vesselId === "string" ? new mongoose_1.default.Types.ObjectId(vesselId) : vesselId);
                const lastLocations = yield db_1.default.lookups
                    .collection("last_locations_lookup")
                    .find({ vessel_id: { $in: targetVesselIds } }, { projection })
                    .toArray();
                const groupedLastLocations = lastLocations.reduce((acc, lastLocation) => {
                    acc[lastLocation.vessel_id.toString()] = lastLocation;
                    return acc;
                }, {});
                // Ensure all requested vessels are included in response
                targetVesselIds.forEach((vId) => {
                    const vesselId = vId.toString();
                    if (!groupedLastLocations[vesselId]) {
                        groupedLastLocations[vesselId] = null;
                    }
                });
                return groupedLastLocations;
            }
            else {
                throw new Error("Either vesselId or vesselIds must be provided");
            }
        });
    }
    calculateOptimalBatchSize(dateRange, optimizedCollectionExists) {
        let batchPerDay = 2000;
        if (optimizedCollectionExists) {
            batchPerDay = 1000;
        }
        const timeDiffDays = (new Date(dateRange[1]).getTime() - new Date(dateRange[0]).getTime()) / (1000 * 60 * 60 * 24);
        console.log("timeDiffDays", timeDiffDays);
        const batchSize = Math.floor(timeDiffDays * batchPerDay);
        console.log("batchSize", batchSize);
        return batchSize;
    }
    optimizeUngroupedCoords(locationsSorted) {
        const groupedLocations = locationsSorted.reduce((acc, loc) => {
            const vesselId = loc.metadata.onboardVesselId.toString();
            if (!acc[vesselId]) {
                acc[vesselId] = [];
            }
            acc[vesselId].push(loc);
            return acc;
        }, {});
        const optimizedGroupedLocations = Object.values(groupedLocations)
            .flatMap((locations) => (0, functions_1.getSimplifiedCoords)(locations))
            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        return optimizedGroupedLocations;
    }
    findClosestLocation(_a) {
        return __awaiter(this, arguments, void 0, function* ({ vesselId, timestampISO, timeWindowMs = 60000, }) {
            try {
                const targetTimestamp = new Date(timestampISO).getTime();
                const collections = yield (0, functions_1.getLocationsCollections)(db_1.default.locationsRaw, targetTimestamp - timeWindowMs, targetTimestamp + timeWindowMs);
                if (collections.length === 0) {
                    return null;
                }
                const searchQuery = {
                    "metadata.onboardVesselId": new mongoose_1.default.Types.ObjectId(vesselId),
                    timestamp: {
                        $gte: new Date(targetTimestamp - timeWindowMs),
                        $lte: new Date(targetTimestamp + timeWindowMs),
                    },
                };
                const results = yield Promise.all(collections.map((collection) => collection
                    .aggregate([
                    { $match: searchQuery },
                    { $addFields: { timeDiff: { $abs: { $subtract: [{ $toLong: "$timestamp" }, targetTimestamp] } } } },
                    { $sort: { timeDiff: 1 } },
                    { $limit: 1 },
                    {
                        $project: {
                            _id: 1,
                            timestamp: 1,
                            groundSpeed: 1,
                            isStationary: 1,
                            latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                            longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                        },
                    },
                ])
                    .toArray()));
                const flatResults = results.flat();
                const closestLocation = flatResults.length > 0 && flatResults[0].latitude && flatResults[0].longitude ? flatResults[0] : null;
                return closestLocation;
            }
            catch (error) {
                console.error("[VesselService.findClosestLocation] Error:", error);
                throw new Error("Failed to find closest location");
            }
        });
    }
}
const vesselLocationService = new VesselLocationService();
exports.default = vesselLocationService;
