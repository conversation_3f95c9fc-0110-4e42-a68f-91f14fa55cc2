// Common interfaces used across multiple services

export interface IPaginationInfo {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

export interface IPaginatedResponse<T> {
    data?: T[];
    pagination: IPaginationInfo;
}

export interface IQueryFilter extends Record<string, any> {}

export interface IProjection {
    [key: string]: 0 | 1;
}

export interface IBucketType {
    name: string;
    region: string;
}
