"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ApiEndpoint_1 = __importDefault(require("../models/ApiEndpoint"));
const ApiKey_1 = __importDefault(require("../models/ApiKey"));
const Vessel_1 = __importDefault(require("../models/Vessel"));
const mongoose_1 = require("mongoose");
class ApiKeyService {
    fetchAll() {
        return __awaiter(this, void 0, void 0, function* () {
            const apiKeys = yield ApiKey_1.default.aggregate([
                {
                    $match: { is_deleted: false },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "created_by",
                        foreignField: "_id",
                        as: "created_by",
                        pipeline: [
                            {
                                $project: {
                                    name: 1,
                                    username: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $addFields: {
                        created_by: { $arrayElemAt: ["$created_by", 0] },
                    },
                },
            ]);
            return apiKeys;
        });
    }
    findById(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid API key id");
            const apiKey = yield ApiKey_1.default.findOne({
                _id: id,
                is_deleted: false,
            });
            if (!apiKey)
                return null;
            return apiKey;
        });
    }
    create(_a) {
        return __awaiter(this, arguments, void 0, function* ({ description, email, created_by }) {
            const apiKey = yield ApiKey_1.default.create({ description, email, created_by });
            return yield this.findById({ id: apiKey._id.toString() });
        });
    }
    update(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, description, email }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid API key id");
            const data = { description, email };
            Object.keys(data).forEach((el) => {
                const key = el;
                if (data[key] === undefined)
                    delete data[key];
            });
            const apiKey = yield ApiKey_1.default.findOneAndUpdate({ _id: id, is_deleted: false }, data, { new: true });
            if (!apiKey)
                return null;
            return yield this.findById({ id: apiKey._id.toString() });
        });
    }
    updateAllowedEndpoints(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, allowed_endpoints }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid API key id");
            const apiEndpoints = yield ApiEndpoint_1.default.find();
            if (allowed_endpoints.some((e_id) => !apiEndpoints.find((e) => e.endpoint_id === e_id)))
                throw new Error("Invalid endpoint provided");
            const apiKey = yield ApiKey_1.default.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_endpoints }, { new: true });
            if (!apiKey)
                return null;
            return yield this.findById({ id: apiKey._id.toString() });
        });
    }
    updateAllowedVessels(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, allowed_vessels }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid API key id");
            const inActiveVessels = yield Vessel_1.default.find({ _id: { $in: allowed_vessels }, is_active: false });
            if (inActiveVessels.length > 0) {
                throw new Error("Cannot assign inactive vessels to API key");
            }
            const apiKey = yield ApiKey_1.default.findOneAndUpdate({ _id: id, is_deleted: false }, { allowed_vessels }, { new: true });
            if (!apiKey)
                return null;
            return yield this.findById({ id: apiKey._id.toString() });
        });
    }
    updateRevocationStatus(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id, is_revoked }) {
            if (!(0, mongoose_1.isValidObjectId)(id))
                throw new Error("Invalid API key id");
            const apiKey = yield ApiKey_1.default.findOneAndUpdate({ _id: id, is_deleted: false }, { is_revoked }, { new: true });
            if (!apiKey)
                return null;
            return yield this.findById({ id: apiKey._id.toString() });
        });
    }
    delete(_a) {
        return __awaiter(this, arguments, void 0, function* ({ id }) {
            const apiKey = yield ApiKey_1.default.findOneAndUpdate({ _id: id, is_deleted: false }, { is_deleted: true }, { new: true });
            return apiKey !== null;
        });
    }
}
const apiKeyService = new ApiKeyService();
exports.default = apiKeyService;
