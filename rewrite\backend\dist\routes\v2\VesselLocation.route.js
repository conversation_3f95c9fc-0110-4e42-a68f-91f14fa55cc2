"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const validator_1 = require("../../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../../utils/functions");
const pLimit_1 = __importDefault(require("../../modules/pLimit"));
const mongoose_1 = __importDefault(require("mongoose"));
const auth_1 = __importDefault(require("../../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../../middlewares/assignEndpointId"));
const endpointIds_1 = require("../../utils/endpointIds");
const db_1 = __importDefault(require("../../modules/db"));
const compression_1 = __importDefault(require("compression"));
const Vessel_service_1 = __importDefault(require("../../services/Vessel.service"));
const VesselLocation_service_1 = __importDefault(require("../../services/VesselLocation.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.use((0, compression_1.default)());
router.post("/:vesselId", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_COORDINATES_V2), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    // the below cannot be verified by test cases
    // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
    (0, express_validator_1.body)("lastKnown")
        .customSanitizer((v) => Number(v))
        .isInt({ min: 0, max: 1 })
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("startTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("endTimestamp")
        .isInt()
        .customSanitizer((v) => Number(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("excludeIds")
        .isArray()
        .bail()
        .customSanitizer((v) => v.map((id) => new mongoose_1.default.Types.ObjectId(id)))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const requestURL = req.get("Referer");
    const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const { vesselId } = req.params;
        const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body;
        console.log(`/v2/vesselLocations ${vesselId}`, startTimestamp, endTimestamp, lastKnown);
        const ts = new Date().getTime();
        if (endTimestamp && !startTimestamp) {
            return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
        }
        const vessel = yield Vessel_service_1.default.findById({ id: vesselId });
        if (!vessel)
            return res.status(404).json({ message: "Vessel does not exist" });
        if (!(0, functions_1.canAccessVessel)(req, vessel)) {
            return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
        }
        if (lastKnown) {
            // const lastLocation = await db.lookups.collection("last_locations_lookup").findOne({ vessel_id: vessel._id }, {
            //     projection: {
            //         _id: '$last_location_id',
            //         longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
            //         latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
            //         timestamp: '$data.timestamp',
            //         groundSpeed: '$data.groundSpeed',
            //         isStationary: '$data.isStationary'
            //     }
            // });
            const lastLocation = yield VesselLocation_service_1.default.findLastKnownLocation({
                vesselId,
                projection: {
                    _id: "$last_location_id",
                    longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                    latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                    timestamp: "$data.timestamp",
                    groundSpeed: "$data.groundSpeed",
                    isStationary: "$data.isStationary",
                },
            });
            console.log(`/v2/vesselLocations ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/v2/vesselLocations ${vesselId} received ${lastLocation ? 1 : 0} coordinates`);
            console.log(`/v2/vesselLocations ${vesselId} time taken to respond ${new Date().getTime() - ts}`);
            return res.json(lastLocation);
        }
        const collections = yield (0, functions_1.getLocationsCollections)(db_1.default.locationsOptimized, startTimestamp || 0, endTimestamp || Date.now());
        const query = { "metadata.onboardVesselId": new mongoose_1.default.Types.ObjectId(vesselId) };
        if (startTimestamp) {
            const endTime = endTimestamp || Date.now();
            query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
        }
        if (excludeIds)
            query._id = { $nin: excludeIds };
        const locations = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed)
                return res.end();
            console.log(`/v2/vesselLocations ${vesselId} querying DB`);
            const resultPromises = collections.map((collection) => {
                const cursor = collection.aggregate([
                    { $match: query },
                    {
                        $project: {
                            _id: 1,
                            timestamp: 1,
                            groundSpeed: 1,
                            isStationary: 1,
                            latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                            longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                        },
                    },
                ]);
                if (isSwagger) {
                    cursor.limit(20);
                }
                return cursor.toArray();
            });
            const allResults = yield Promise.all(resultPromises);
            const flattenedResults = allResults.flat();
            return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }));
        console.log(`/v2/vesselLocations ${vesselId} time taken to query ${new Date().getTime() - ts}`);
        console.log(`/v2/vesselLocations ${vesselId} received ${(Array.isArray(locations) && locations.length) || 1} coordinates`);
        console.log(`/v2/vesselLocations ${vesselId} time taken to respond ${new Date().getTime() - ts}`);
        if (isClosed)
            return res.end();
        res.json(locations);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: Ground speed in knots
 *           example: 12.5
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the location record
 *           example: "2023-06-16T12:00:00.000Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */
/**
 * @swagger
 * /v2/vesselLocations/{vesselId}:
 *   post:
 *     summary: Fetch vessel location data
 *     description: Fetches location data for a specific vessel, with options for historical data or last known position
 *     tags: [Vessel Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: vesselId
 *         in: path
 *         required: true
 *         description: The ID of the vessel to fetch locations for
 *         schema:
 *           type: string
 *           example: 683df46b073245cf0fd62bb9
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 default: 0
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - description: Array of locations when lastKnown=0 or not provided
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/VesselLocation'
 *                 - allOf:
 *                   - description: Single location object when lastKnown=1
 *                   - $ref: '#/components/schemas/VesselLocation'
 *                 - description: When no locations are found
 *                   type: "null"
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */
exports.default = router;
