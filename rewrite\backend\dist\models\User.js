"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const db_1 = __importDefault(require("../modules/db"));
const timezonesList_1 = require("../utils/timezonesList");
const userSchema = new mongoose_1.default.Schema({
    name: { type: String, required: true },
    email: { type: String },
    username: { type: String },
    date_time_format: { type: String, required: false, default: timezonesList_1.defaultDateTimeFormat },
    use_MGRS: { type: Boolean, required: false, default: false },
    password: { type: String, required: true },
    jwt_tokens: { type: [String], required: false, default: [] },
    reset_password_token: { type: String, required: false, default: null },
    reset_password_expire: { type: Number, required: false, default: null },
    email_verification_enabled: { type: Boolean, required: true, default: false },
    email_verified_device_ids: { type: [String], required: false, default: [] },
    role_id: { type: Number, required: true },
    home_port_filter_mode: { type: String, default: "ONLY_NON_HOME_PORTS", enum: ["ALL", "ONLY_HOME_PORTS", "ONLY_NON_HOME_PORTS"] },
    // region: { type: String, required: true, default: 'us-east-1' }, @depreated, remove from DB after merge into production
    deletable: { type: Boolean, required: true, default: true },
    is_deleted: { type: Boolean, required: true, default: false },
    allowed_vessels: { type: Array, required: true, default: [] },
    organization_id: { type: mongoose_1.default.Schema.Types.ObjectId, required: true },
    created_by: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: "User",
    },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});
userSchema.index({ email: 1, username: 1 }, { unique: true });
userSchema.post("save", (user) => {
    ioEmitter_1.default.emit("notifyAll", { name: `users/changed`, data: user.toObject() });
});
userSchema.post("findOneAndDelete", (user) => {
    ioEmitter_1.default.emit("notifyAll", { name: `users/changed`, data: user.toObject() });
});
// function emitChangedEvent(user) {
//     ioEmitter.emit('notifyAll', { name: `users/changed`, data: user.toObject() });
// }
const User = db_1.default.qm.model("User", userSchema);
exports.default = User;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
