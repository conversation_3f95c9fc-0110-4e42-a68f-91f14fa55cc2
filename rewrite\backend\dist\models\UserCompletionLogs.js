"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("./User"));
const db_1 = __importDefault(require("../modules/db"));
const userCompletionLogsSchema = new mongoose_1.default.Schema({
    user_id: { type: mongoose_1.default.Schema.Types.ObjectId, ref: User_1.default, required: true },
    command: { type: String, required: true },
    response: { type: String, required: true },
    completion_type: { type: String, required: true, enum: ["events_filter", "video_filters", "other"] },
    completed_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});
const UserCompletionLogs = db_1.default.qm.model("UserCompletionLogs", userCompletionLogsSchema, "logs_users_completions");
exports.default = UserCompletionLogs;
