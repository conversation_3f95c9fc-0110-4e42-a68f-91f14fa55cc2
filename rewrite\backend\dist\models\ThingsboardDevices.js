"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const thingsboardDeviceSchema = new mongoose_1.default.Schema({
    deviceId: { type: String, required: true, unique: true },
    dashboardId: { type: String, required: false },
    deviceName: { type: String, required: true },
    accessToken: { type: String, required: true },
}, { timestamps: true });
// Create a partial index that only applies to non-null dashboardId values
thingsboardDeviceSchema.index({ dashboardId: 1 }, {
    unique: true,
    partialFilterExpression: { dashboardId: { $type: "string" } },
});
const ThingsboardDevices = db_1.default.qmShared.model("ThingsboardDevices", thingsboardDeviceSchema, "thingsboard_devices");
exports.default = ThingsboardDevices;
