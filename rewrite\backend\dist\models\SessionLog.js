"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const sessionLogSchema = new mongoose_1.default.Schema({
    socket_id: { type: String, required: true },
    device: { type: String, required: true },
    browser: { type: String, required: true },
    user_id: { type: mongoose_1.default.Schema.Types.ObjectId, required: true },
    disconnect_timestamp: { type: Date, default: null },
    connect_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
    environment: { type: String, required: true, default: () => process.env.NODE_ENV },
});
sessionLogSchema.post("save", (log) => {
    ioEmitter_1.default.emit("notifyAll", { name: `logs/changed`, data: log.toObject() });
});
sessionLogSchema.post("findOneAndDelete", (log) => {
    ioEmitter_1.default.emit("notifyAll", { name: `logs/changed`, data: log.toObject() });
});
// function emitChangedEvent(log) {
//     ioEmitter.emit('notifyAll', { name: `logs/changed`, data: log.toObject() });
// }
const SessionLog = db_1.default.qm.model("SessionLog", sessionLogSchema, "logs_sessions");
exports.default = SessionLog;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
