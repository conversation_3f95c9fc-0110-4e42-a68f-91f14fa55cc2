"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const apiEndpointSchema = new mongoose_1.default.Schema({
    endpoint_id: { type: Number, required: true, unique: true },
    name: { type: String, required: true },
    category: { type: String, required: true },
});
const ApiEndpoint = db_1.default.qm.model("ApiEndpoint", apiEndpointSchema, "api_endpoints");
exports.default = ApiEndpoint;
