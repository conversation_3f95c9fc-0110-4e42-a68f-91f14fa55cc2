"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.io = exports.server = void 0;
require("dotenv/config");
// import "aws-sdk/lib/maintenance_mode_message".suppress = true;
require("./modules/processLogs");
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const cors_1 = __importDefault(require("cors"));
const path_1 = __importDefault(require("path"));
const socket_io_1 = __importDefault(require("socket.io"));
const http_1 = __importDefault(require("http"));
const ioEmitter_1 = __importDefault(require("./modules/ioEmitter"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const SessionLog_1 = __importDefault(require("./models/SessionLog"));
const swagger_1 = require("./modules/swagger");
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const uuid_1 = require("uuid");
const index_1 = __importDefault(require("./routes/index"));
const index_v2_1 = __importDefault(require("./routes/v2/index.v2"));
if (!process.env.JWT_SECRET) {
    throw new Error("JWT_SECRET is not defined");
}
const app = (0, express_1.default)();
const server = http_1.default.createServer(app);
exports.server = server;
const io = new socket_io_1.default.Server(server, { cors: { origin: "*" } });
exports.io = io;
app.use((0, cors_1.default)({
    exposedHeaders: ["RateLimit-Reset", "Content-Disposition", "Content-Type"],
    origin: true,
    credentials: true,
}));
app.use((0, cookie_parser_1.default)());
app.use(express_1.default.json({ limit: "20mb" }));
app.use("/api", (req, res, next) => {
    if (!req.headers.authorization) {
        console.log(`[${req.method}: ${req.originalUrl}] user: public, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `);
    }
    if (!req.cookies.deviceId) {
        const deviceId = (0, uuid_1.v4)();
        res.cookie("deviceId", deviceId, {
            expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
        });
    }
    next();
});
app.use("/api/docs", swagger_1.swaggerUi.serve, swagger_1.swaggerUi.setup(swagger_1.swaggerDocs, swagger_1.swaggerConfig));
app.use("/api", index_1.default); // this is a v1 version route
app.use("/api/v2", index_v2_1.default); // this is a v2 version route
app.use("/api/*", (req, res) => res.status(404).send(`< h3 > Sorry, that route does not exist.</h3 > `));
app.use("/api", (req, res) => res.send(`< h3 > Welcome to Quartermaster API</h3 > `));
const frontendDistPath = path_1.default.resolve(__dirname, "../../../frontend/dist");
app.use(express_1.default.static(frontendDistPath));
app.get("*", (req, res) => {
    res.sendFile(path_1.default.join(frontendDistPath, "index.html"));
});
const PORT = Number(process.env.PORT);
// mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
//     .then(() => {
//         server.listen(PORT, () => {
//             console.log(`Server running on port ${ PORT } `);
//         });
//     })
//     .catch(err => console.log(err));
if (process.env.NODE_ENV !== "test") {
    const rServer = server.listen(PORT, () => {
        console.log(`Server running at http://localhost:${PORT}`);
    });
    rServer.setTimeout(600000);
}
io.use((socket, next) => {
    try {
        const { jwt_token } = socket.handshake.auth;
        if (!jwt_token)
            return next(new Error("Authentication error: No token provided"));
        const { user_id } = jsonwebtoken_1.default.verify(jwt_token, process.env.JWT_SECRET);
        if (!user_id)
            return next(new Error("Authentication error: Invalid token"));
        socket.handshake.auth.user_id = user_id;
        next();
    }
    catch (err) {
        next(new Error(err.message));
    }
});
io.on("connection", (socket) => __awaiter(void 0, void 0, void 0, function* () {
    console.log(`User connected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
    socket.on("disconnect", () => __awaiter(void 0, void 0, void 0, function* () {
        console.log(`User disconnected ${socket.id}. Total connections: ${io.sockets.sockets.size}`);
        yield SessionLog_1.default.updateOne({ socket_id: socket.id }, { $set: { disconnect_timestamp: new Date().toISOString() } });
    }));
    SessionLog_1.default.create({
        socket_id: socket.id,
        device: socket.handshake.auth.device,
        browser: socket.handshake.auth.browser,
        user_id: new mongoose_1.default.Types.ObjectId(socket.handshake.auth.user_id),
    });
}));
ioEmitter_1.default.on("notifyAll", (event) => {
    io.emit(event.name, event.data);
});
process.on("uncaughtException", (err) => {
    console.error("(FATAL ERROR) Uncaught Exception:", err);
    console.error(err);
});
exports.default = app;
