import { Slider } from "@mui/material";
import { useEffect, useState } from "react";
import theme from "../../../theme";

const DatapointFilterSlider = ({ setDatapointsDistance , datapointsDistance }) => {

    const [temp, setTemp] = useState(datapointsDistance);

    useEffect(() => {
        setTemp(datapointsDistance);
    }, [datapointsDistance]);
    // console.count("DatapointFilterSlider Rendered");
    return (

        <Slider
            valueLabelFormat={(v) => `${v} meters`}
            valueLabelDisplay="auto"
            sx={{
                padding: 0,
                color: theme.palette.custom.mediumGrey,
            }}
            value={temp}
            onChange={(e, v) => setTemp(v)}
            onChangeCommitted={() => {
                setDatapointsDistance(temp);
            }}
            min={50}
            max={10000}
        />

    )
}
export default DatapointFilterSlider;