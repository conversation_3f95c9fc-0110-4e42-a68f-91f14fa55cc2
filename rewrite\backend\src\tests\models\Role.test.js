const mockingoose = require('mockingoose');
const ioEmitter = require('../../modules/ioEmitter');
const { rolesList } = require('../data/Roles');
const Role = require('../../models/Role');
const Permission = require('../../models/Permission');
const { permissionsList } = require('../data/Permissions');

jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn(),
}));

describe('Role Model', () => {
    it('should create the document', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Role).toReturn(doc, 'create');

        const result = await Role.create({
            role_name: 'test-name',
            role_id: -1,
            hierarchy_number: 1,
        });

        expect(result).toBeInstanceOf(Object);
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop);
        });
    });

    it('should return the doc with findById', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Role).toReturn(doc, 'findOne');

        const result = await Role.findById({ _id: doc._id });

        expect(result).toBeInstanceOf(Object);
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop);
        });
    });

    it('should return the doc with find', async () => {
        const docs = rolesList.map(role => ({ ...role, hierarchy_number: 1 }));

        mockingoose(Role).toReturn(docs, 'find');

        const result = await Role.find();

        expect(result).toBeInstanceOf(Array);
        Object.keys(docs[0]).forEach(prop => {
            expect(result[0]).toHaveProperty(prop);
        });
    });

    it('should trigger pre save hook and assign a role_id', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Permission).toReturn(permissionsList, 'find');
        mockingoose(Role).toReturn(doc, 'save');
        mockingoose(Role).toReturn(doc, 'findOne');

        const role = new Role(doc);
        role.isNew = true;
        await role.save();
    });

    it('should trigger pre save hook and assign a role_id even if table is empty', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Permission).toReturn(permissionsList, 'find');
        mockingoose(Role).toReturn(doc, 'save');
        mockingoose(Role).toReturn(null, 'findOne');

        const role = new Role(doc);
        role.isNew = true;
        await role.save();
    });

    it('should trigger pre save hook and handle error', async () => {
        try {
            const doc = { ...rolesList[0], hierarchy_number: 1 };

            mockingoose(Role).toReturn(doc, 'save');
            mockingoose(Role).toReturn(doc, 'findOne');
            mockingoose(Permission).toReturn(new Error('Database Error'), 'find');

            const role = new Role(doc);
            role.isNew = true;
            await role.save();
        } catch (err) { }
    });

    it('should trigger post save hook and emit an event', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Role).toReturn(doc, 'save');

        const role = new Role(doc);
        role.isNew = false;
        await role.save();

        expect(ioEmitter.emit).toHaveBeenCalled();
    });

    it('should trigger post findOneAndDelete hook and emit an event', async () => {
        const doc = { ...rolesList[0], hierarchy_number: 1 };

        mockingoose(Role).toReturn(doc, 'findOneAndDelete');

        const deletedDoc = await Role.findOneAndDelete({ _id: doc._id });

        Role.emitChangedEvent(deletedDoc);

        expect(ioEmitter.emit).toHaveBeenCalled();
    });
});
