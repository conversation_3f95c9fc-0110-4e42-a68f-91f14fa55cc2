"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const TourGuideSchema = new mongoose_1.default.Schema({
    user_id: { type: mongoose_1.default.Schema.Types.ObjectId, ref: "User", required: true },
    maps: { type: mongoose_1.default.Schema.Types.Boolean, default: false },
    streams: { type: mongoose_1.default.Schema.Types.Boolean, default: false },
    events: { type: mongoose_1.default.Schema.Types.Boolean, default: false },
    notifications: { type: mongoose_1.default.Schema.Types.Boolean, default: false },
});
const TourGuide = db_1.default.qm.model("TourGuide", TourGuideSchema);
exports.default = TourGuide;
