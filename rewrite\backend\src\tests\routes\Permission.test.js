const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const request = require("supertest");
const app = require('../../server');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Permissions API', () => {

    describe('GET /api/permissions', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/permissions');
                    expect(res.status).toBe(401);
                });

                it('should return the permissions list', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Permission.find.mockResolvedValue(permissionsList);
                    const res = await request(app).get('/api/permissions').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'permission_id', 'permission_name', 'permission_description', 'assignable'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should handle internal server errors', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Permission.find.mockRejectedValueOnce(new Error('Something went wrong')); // Simulate error
                    const res = await request(app).get('/api/permissions').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);

    })

});
