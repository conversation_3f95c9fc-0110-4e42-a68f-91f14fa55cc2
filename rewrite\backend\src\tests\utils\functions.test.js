const {
    isURL,
    isBase64,
    isBase64<PERSON>rURL,
    validateError,
    isIntStrict,
    generateInvitationLink,
} = require('../../utils/functions');

const jwt = require('jsonwebtoken');

jest.mock('jsonwebtoken');

describe('Utility Functions', () => {
    describe('isURL', () => {
        it('should return true for valid URLs', () => {
            expect(isURL('http://example.com')).toBe(true);
            expect(isURL('https://example.com')).toBe(true);
            expect(isURL('ftp://example.com')).toBe(true);
        });

        it('should return false for invalid URLs', () => {
            expect(isURL('invalid-url')).toBe(false);
            expect(isURL('ftp://')).toBe(false);
            expect(isURL('http:/example.com')).toBe(false);
        });
    });

    describe('isBase64', () => {
        it('should return true for valid Base64 strings', () => {
            expect(isBase64('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA')).toBe(true);
        });

        it('should return false for invalid Base64 strings', () => {
            expect(isBase64('invalid-base64')).toBe(false);
            expect(isBase64('data:image/png;base64,')).toBe(false);
        });
    });

    describe('isBase64OrURL', () => {
        it('should return true for a valid URL', () => {
            expect(isBase64OrURL('http://example.com')).toBe(true);
        });

        it('should return true for a valid Base64 string', () => {
            expect(isBase64OrURL('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA')).toBe(true);
        });

        it('should return true for an array containing valid URLs and Base64', () => {
            expect(isBase64OrURL(['http://example.com', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA'])).toBe(true);
        });

        it('should return false for an invalid URL and Base64 string', () => {
            expect(isBase64OrURL('invalid')).toBe(false);
            expect(isBase64OrURL(['invalid-url', 'invalid-base64'])).toBe(false);
        });
    });

    describe('isIntStrict', () => {
        it('should return true for strict integers', () => {
            expect(isIntStrict(5)).toBe(true);
            expect(isIntStrict(-10)).toBe(true);
            expect(isIntStrict(0)).toBe(true);
        });

        it('should return false for non-integers', () => {
            expect(isIntStrict(5.5)).toBe(false);
            expect(isIntStrict('5')).toBe(false);
            expect(isIntStrict(NaN)).toBe(false);
            expect(isIntStrict(null)).toBe(false);
            expect(isIntStrict(undefined)).toBe(false);
        });
    });

    describe('validateError', () => {
        let res;

        beforeEach(() => {
            res = {
                status: jest.fn().mockReturnThis(),
                json: jest.fn(),
            };
        });

        it('should handle MongoServerError with code 11000', () => {
            const err = {
                name: 'MongoServerError',
                code: 11000,
                keyValue: { email: '<EMAIL>' },
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(400);
            expect(res.json).toHaveBeenCalledWith({ message: 'Value already exists: email = <EMAIL>' });
        });

        it('should handle MongoServerError with other codes', () => {
            const err = {
                name: 'MongoServerError',
                code: 12345,
                message: 'Some error occurred',
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unexpected error occured: Some error occurred' });
        });

        it('should handle MongoServerError with other codes having no message', () => {
            const err = {
                name: 'MongoServerError',
                code: 12345,
            };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalled();
        });

        it('should handle ResourceNotFoundException', () => {
            const err = { code: 'ResourceNotFoundException', message: 'Resource not found' };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(404);
            expect(res.json).toHaveBeenCalledWith({ message: 'Resource not found' });
        });

        it('should handle other errors', () => {
            const err = new Error('An unknown error occurred');

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalledWith({ message: 'Unexpected error occured: An unknown error occurred' });
        });

        it('should handle other errors with no message', () => {
            const err = { error: 'Something went wrong' };

            validateError(err, res);
            expect(res.status).toHaveBeenCalledWith(500);
            expect(res.json).toHaveBeenCalled();
        });
    });

    describe('generateInvitationLink', () => {
        const mockEmail = '<EMAIL>';
        const mockRoleId = 'role123';
        const mockAdminId = 'admin456';
        const mockRole = 'user';

        const mockJwtSecret = 'mockSecret';
        const mockApiUrl = 'http://api.example.com';

        beforeAll(() => {
            process.env.JWT_SECRET = mockJwtSecret;
            process.env.API_URL = mockApiUrl;
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('should generate a valid invitation link with JWT token', () => {
            const mockToken = 'mockedToken';
            jwt.sign.mockImplementationOnce(() => mockToken);

            const link = generateInvitationLink(mockEmail, mockRoleId, mockAdminId, mockRole);

            expect(link).toBe(`${mockApiUrl}/users/verify-invite?token=${encodeURIComponent(mockToken)}`);
            expect(jwt.sign).toHaveBeenCalledWith(
                { email: mockEmail, role_id: mockRoleId, admin_id: mockAdminId, role: mockRole, timestamp: expect.any(Number) },
                mockJwtSecret,
                { expiresIn: '24h' }
            );
        });

        it('should throw an error if JWT signing fails', () => {
            const error = new Error('JWT sign failed');
            jwt.sign.mockImplementationOnce(() => { throw error; });

            expect(() => {
                generateInvitationLink(mockEmail, mockRoleId, mockAdminId, mockRole);
            }).toThrow('JWT sign failed');
        });
    });

});
