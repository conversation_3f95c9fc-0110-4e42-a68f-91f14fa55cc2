"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const permissions_1 = require("../utils/permissions");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const mongoose_1 = __importDefault(require("mongoose"));
const Organization_1 = __importDefault(require("../models/Organization"));
const mongoose_2 = require("mongoose");
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const User_1 = __importDefault(require("../models/User"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ORGANIZATIONS), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let hasManageOrgPermission = false;
        if (req.user && req.user.role && req.user.permissions) {
            hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.manageOrganizations);
        }
        const matchCondition = {};
        if (!hasManageOrgPermission && req.user.organization_id) {
            matchCondition._id = new mongoose_1.default.Types.ObjectId(req.user.organization_id);
        }
        const organizations = yield Organization_1.default.aggregate([
            { $match: matchCondition },
            {
                $lookup: {
                    from: "users",
                    let: { orgId: "$_id" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$organization_id", "$$orgId"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "users",
                },
            },
            {
                $lookup: {
                    from: "users",
                    let: { createdBy: "$created_by" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$createdBy"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "user",
                },
            },
            {
                $addFields: {
                    user: { $arrayElemAt: ["$user", 0] },
                    user_count: { $size: "$users" },
                },
            },
            {
                $project: {
                    name: 1,
                    domain: 1,
                    creation_timestamp: 1,
                    is_miscellaneous: 1,
                    "user.name": 1,
                    "user.username": 1,
                    user_count: 1,
                },
            },
        ]);
        res.json(organizations);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_ORGANIZATION_BY_ID), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_2.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let hasManageOrgPermission = false;
        if (req.user && req.user.permissions) {
            hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions_1.permissions.manageOrganizations);
        }
        if (!hasManageOrgPermission && req.user.organization_id && req.user.organization_id.toString() !== req.params.id) {
            return res.status(403).json({ message: "Forbidden: You can only access your own organization" });
        }
        const organization = yield Organization_1.default.aggregate([
            {
                $match: { _id: new mongoose_1.default.Types.ObjectId(req.params.id) },
            },
            {
                $lookup: {
                    from: "users",
                    let: { orgId: "$_id" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$organization_id", "$$orgId"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "users",
                },
            },
            {
                $lookup: {
                    from: "users",
                    let: { createdBy: "$created_by" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$createdBy"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "user",
                },
            },
            {
                $addFields: {
                    user: { $arrayElemAt: ["$user", 0] },
                    user_count: { $size: "$users" },
                },
            },
            {
                $project: {
                    name: 1,
                    domain: 1,
                    creation_timestamp: 1,
                    is_miscellaneous: 1,
                    "user.name": 1,
                    "user.username": 1,
                    user_count: 1,
                },
            },
        ]);
        if (!organization.length)
            return res.status(404).json({ message: "Organization not found" });
        res.json(organization[0]);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_ORGANIZATION), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageOrganizations]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("domain")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, domain } = req.body;
        const existingOrganization = yield Organization_1.default.findOne({ domain });
        if (existingOrganization) {
            return res.status(400).json({ message: "An organization with this domain already exists" });
        }
        const organization = yield Organization_1.default.create({
            name,
            domain,
            created_by: req.user._id,
        });
        res.status(201).json({ message: "Organization created successfully", organization });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_ORGANIZATION), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageOrganizations]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_2.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("name").isString().optional(),
    (0, express_validator_1.body)("domain").isString().optional(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const updates = req.body;
        const organization = yield Organization_1.default.findByIdAndUpdate(req.params.id, updates, { new: true });
        if (!organization)
            return res.status(404).json({ message: "Organization not found" });
        res.json({ message: "Organization updated successfully", organization });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_ORGANIZATION), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageOrganizations]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_2.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const organizationId = req.params.id;
        const userCount = yield User_1.default.countDocuments({ organization_id: organizationId });
        if (userCount > 0) {
            return res.status(400).json({ message: "Cannot delete organization as it is assigned to one or more users." });
        }
        const organization = yield Organization_1.default.findByIdAndDelete(organizationId);
        if (!organization)
            return res.status(404).json({ message: "Organization not found" });
        res.json({ message: "Organization deleted successfully" });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
