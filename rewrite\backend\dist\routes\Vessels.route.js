"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const functions_1 = require("../utils/functions");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const Stream_service_1 = __importDefault(require("../services/Stream.service"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const Vessel_1 = __importDefault(require("../models/Vessel"));
const RegionGroup_1 = __importDefault(require("../models/RegionGroup"));
const mongoose_1 = __importDefault(require("mongoose"));
const router = express_1.default.Router();
const authUserApiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    }
    else {
        apiLimiter(req, res, next);
    }
}
router.use("/", conditionalRateLimiter);
router.get("/info", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_VESSELS_INFO), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("regions")
        .isString()
        .optional()
        .customSanitizer((v) => v.split(",").map((v) => v.trim())),
    (0, express_validator_1.query)("region_groups")
        .optional()
        .customSanitizer((v) => {
        if (!v || v.trim() === "")
            return [];
        return v
            .split(",")
            .map((id) => id.trim())
            .filter((id) => id.length > 0 && mongoose_1.default.Types.ObjectId.isValid(id));
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { regions, region_groups } = req.query;
        let vessels = yield Vessel_1.default.find();
        const streams = yield Stream_service_1.default.fetchAll();
        const regionGroups = yield RegionGroup_1.default.find();
        // filter provisioned units
        vessels = vessels.filter((vessel) => (0, functions_1.canAccessVessel)(req, vessel));
        // filter by regions
        if (regions && regions.length > 0) {
            vessels = vessels.filter((vessel) => {
                const stream = streams.find((s) => s.unit_id === vessel.unit_id);
                if (!stream)
                    return false;
                return regions.includes(stream.region);
            });
        }
        // filter by region groups
        // if (region_groups && region_groups.length > 0) {
        //     vessels = vessels.filter((vessel) => {
        //         const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
        //         if (!regionGroup) return false;
        //         return region_groups.includes(regionGroup._id.toString());
        //     });
        // }
        if (region_groups && region_groups.length > 0) {
            vessels = vessels.filter((vessel) => {
                if (!vessel.region_group_id)
                    return false;
                return region_groups.includes(vessel.region_group_id.toString());
            });
        }
        // generate response data
        const data = vessels.map((vessel) => {
            const stream = streams.find((s) => s.unit_id === vessel.unit_id);
            // const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
            const regionGroup = vessel
                ? regionGroups.find((rg) => { var _a; return rg._id.toString() === ((_a = vessel.region_group_id) === null || _a === void 0 ? void 0 : _a.toString()); })
                : undefined;
            return {
                vessel_id: vessel._id.toString(),
                unit_id: vessel.unit_id,
                name: vessel.name,
                thumbnail_s3_key: vessel.thumbnail_s3_key,
                is_active: vessel.is_active,
                region: (stream === null || stream === void 0 ? void 0 : stream.region) || null,
                is_live: (stream === null || stream === void 0 ? void 0 : stream.is_live) || false,
                timezone: (regionGroup === null || regionGroup === void 0 ? void 0 : regionGroup.timezone) || null,
                // region_group_id: regionGroup?._id.toString() || null,
                region_group_id: (vessel === null || vessel === void 0 ? void 0 : vessel.region_group_id) || null,
                home_port_location: (vessel === null || vessel === void 0 ? void 0 : vessel.home_port_location) || null,
            };
        });
        res.json(data);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   name: Vessels
 *   description: Fetch vessel information
 */
/**
 * @swagger
 * /vessels/info:
 *   get:
 *     summary: Fetch list of vessels information
 *     description: Fetches a list of vessels with their respective details including vessel ID, unit ID, name, and other metadata.
 *     tags: [Vessels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: regions
 *         in: query
 *         required: false
 *         description: Comma-separated AWS regions to fetch vessels for
 *         schema:
 *           type: string
 *           example: ap-southeast-1,us-east-1
 *       - name: region_groups
 *         in: query
 *         required: false
 *         description: Comma-separated region group IDs to fetch vessels for
 *         schema:
 *           type: string
 *           example: 67db2068a64a865006d065f7, 67dc2e586a89af04d143d471, 681c253f9f43051a7748b2c1
 *     responses:
 *       200:
 *         description: An array of vessels information
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   vessel_id:
 *                     type: string
 *                     description: "The unique identifier of the vessel."
 *                     example: "507f1f77bcf86cd799439011"
 *                   unit_id:
 *                     type: string
 *                     description: "The unit identifier of the vessel."
 *                     example: "prototype-30"
 *                   name:
 *                     type: string
 *                     nullable: true
 *                     description: "The name of the unit. Can be null if not provided."
 *                     example: "BRP Teresa Magbanua MRRV-9701"
 *                   thumbnail_s3_key:
 *                     type: string
 *                     nullable: true
 *                     description: "The S3 key of the vessel's thumbnail image."
 *                     example: "vessels/thumbnail/507f1f77bcf86cd799439011.jpg"
 *                   is_active:
 *                     type: boolean
 *                     description: "Whether the vessel is active."
 *                     example: true
 *                   region:
 *                     type: string
 *                     nullable: true
 *                     description: "AWS region the vessel is listed in."
 *                     example: "ap-southeast-1"
 *                   is_live:
 *                     type: boolean
 *                     description: "Whether the vessel's camera is currently live."
 *                     example: true
 *                   timezone:
 *                     type: string
 *                     nullable: true
 *                     description: "The timezone of the vessel from its region group."
 *                     example: "+08:00"
 *                   region_group_id:
 *                     type: string
 *                     nullable: true
 *                     description: "The ID of the region group this vessel belongs to."
 *                     example: "507f1f77bcf86cd799439011"
 *                   home_port_location:
 *                     type: object
 *                     nullable: true
 *                     description: "The home port location as a GeoJSON Point."
 *                     properties:
 *                       type:
 *                         type: string
 *                         example: "Point"
 *                       coordinates:
 *                         type: array
 *                         items:
 *                           type: number
 *                         example: [120.9842, 14.5995]
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       500:
 *         description: "Internal server error"
 */
