import express from "express";
import userRoute from "./User.route";
import kinesisRoute from "./Kinesis.route";
import regionRoute from "./Region.route";
import vesselLocationRoute from "./VesselLocation.route";
import roleRoute from "./Role.route";
import permissionRoute from "./Permission.route";
import artifactRoute from "./Artifact.route";
import artifactFavouriteRoute from "./ArtifactFavourites.route";
import artifactSuggestionRoute from "./ArtifactSuggestions.route";
import logRoute from "./Log.route";
import s3Route from "./S3.route";
import apiKeyRoute from "./ApiKey.route";
import apiEndpointRoute from "./ApiEndpoint.route";
import statisticsRoute from "./Statistics.route";
import vesselRoute from "./Vessels.route";
import geolocationRoute from "./Geolocation.route";
import regionGroupRoute from "./RegionGroup.route";
import tourGuideRoute from "./TourGuide.route";
import notificationAlertRoute from "./NotificationAlert.route";
import inAppNotificationRoute from "./InAppNotification.route";
import notificationSummaryRoute from "./NotificationSummary.route";
import artifactCompletionsRoute from "./ArtifactCompletions.route";
import emailDomainsRoute from "./EmailDomains.route";
import homePortsRoute from "./HomePorts.route";
import organizationRoute from "./Organization.route";
import vesselManagementRoute from "./VesselManagement.route";
import thingsboardRoute from "./Thingsboard.route";
import vesselAisRoute from "./VesselAis.route";
import audioRoute from "./Audio.route";

const router = express.Router();

router.use("/users", userRoute);
router.use("/kinesis", kinesisRoute);
router.use("/regions", regionRoute);
router.use("/vesselLocations", vesselLocationRoute);
router.use("/roles", roleRoute);
router.use("/permissions", permissionRoute);
router.use("/artifacts", artifactRoute);
router.use("/artifactFavourites", artifactFavouriteRoute);
router.use("/suggestions", artifactSuggestionRoute);
router.use("/logs", logRoute);
router.use("/s3", s3Route);
router.use("/apiKeys", apiKeyRoute);
router.use("/apiEndpoints", apiEndpointRoute);
router.use("/statistics", statisticsRoute);
router.use("/vessels", vesselRoute);
router.use("/geolocations", geolocationRoute);
router.use("/regionGroups", regionGroupRoute);
router.use("/tourGuides", tourGuideRoute);
router.use("/notificationsAlerts", notificationAlertRoute);
router.use("/inAppNotifications", inAppNotificationRoute);
router.use("/summaryReports", notificationSummaryRoute);
router.use("/completions", artifactCompletionsRoute);
router.use("/emailsDomain", emailDomainsRoute);
router.use("/organizations", organizationRoute);
router.use("/homePorts", homePortsRoute);
router.use("/vesselManagement", vesselManagementRoute);
router.use("/thingsboard", thingsboardRoute);
router.use("/vesselAis", vesselAisRoute);
router.use("/audios", audioRoute);

export default router;
