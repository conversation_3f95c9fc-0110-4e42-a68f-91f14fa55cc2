// const supertest = require('supertest');
// const app = require('../server');

import { NextFunction, Request, Response } from "express";

// const { default: mongoose } = require("mongoose");

// jest.mock('mongoose')

jest.mock('express-rate-limit', () => jest.fn(() => (req: Request, res: Response, next: NextFunction) => next()));

// jest.mock('../models/User', () => ({
//     create: jest.fn().mockResolvedValue({}),
//     find: jest.fn().mockResolvedValue([]),
//     findOne: jest.fn().mockResolvedValue({}),
//     findById: jest.fn().mockResolvedValue({}),
//     aggregate: jest.fn().mockResolvedValue([]),
// }));

// jest.mock('../models/ApiKey', () => ({
//     find: jest.fn().mockResolvedValue([]),
//     findOne: jest.fn().mockResolvedValue({}),
//     findById: jest.fn().mockResolvedValue({}),
//     create: jest.fn().mockResolvedValue({}),
// }));

// jest.mock('../models/Role', () => ({
//     create: jest.fn().mockResolvedValue({}),
//     find: jest.fn().mockResolvedValue([]),
//     findOne: jest.fn().mockResolvedValue({}),
//     findById: jest.fn().mockResolvedValue({}),
//     findOneAndDelete: jest.fn().mockResolvedValue({}),
// }));

// jest.mock('../models/Permission', () => ({
//     create: jest.fn().mockResolvedValue({}),
//     find: jest.fn().mockResolvedValue([]),
//     findOne: jest.fn().mockResolvedValue({}),
//     findById: jest.fn().mockResolvedValue({}),
// }));

// jest.mock('../models/SessionLog', () => ({
//     aggregate: jest.fn(),
// }));

// jest.mock('../models/Region', () => ({
//     find: jest.fn().mockResolvedValue([]),
// }));

// jest.mock('../modules/awsIot', () => ({
//     listThings: jest.fn().mockResolvedValue([]),
// }));

// jest.mock('../modules/awsS3', () => ({
//     checkKeyExists: jest.fn(),
//     s3: {
//         getSignedUrl: jest.fn(),
//     }
// }));

// jest.mock('../modules/awsKinesis', () => ({
//     listStreams: jest.fn().mockResolvedValue([]),
//     getHlsStreamingSessionURL: jest.fn().mockResolvedValue('url')
// }));

// if (process.env.TEST_ENV === 'middlewares.auth') {
//     jest.mock('../queries/User');
// }

// jest.mock('../modules/db', () => ({
//     qm: {
//         model: jest.fn().mockReturnValue({
//             find: jest.fn(),
//             aggregate: jest.fn(),
//             create: jest.fn(),
//             findOne: jest.fn(),
//             findById: jest.fn(),
//             findOneAndDelete: jest.fn(),
//         }),
//         collection: jest.fn(),
//     },
//     qmai: {
//         model: jest.fn(),
//         collection: jest.fn(),
//     },
// }));

// jest.spyOn(ApiEndpoint, 'find')00000
//     .mockImplementation(() => jest.fn())

// Export the supertest request to use in individual test files
// global.request = supertest(app);
beforeEach(() => {
    jest.spyOn(console, 'log').mockImplementation(() => { });
    jest.spyOn(console, 'error').mockImplementation(() => { });
    jest.spyOn(console, 'info').mockImplementation(() => { });
    jest.spyOn(console, 'warn').mockImplementation(() => { });
});


// Setup global cleanup after each test
afterEach(() => {
    jest.restoreAllMocks();
});