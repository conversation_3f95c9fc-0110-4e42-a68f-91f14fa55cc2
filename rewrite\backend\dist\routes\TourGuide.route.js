"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_1 = __importDefault(require("express"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const TourGuide_1 = __importDefault(require("../models/TourGuide"));
const functions_1 = require("../utils/functions");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_TOUR_GUIDE), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const tourGuide = yield TourGuide_1.default.find({ user_id: req.user._id });
        res.json(tourGuide);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_TOUR_GUIDE), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("maps")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("streams")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("events")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("notifications")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)().custom((value, { req }) => {
        if (Object.keys(req.body).length === 0) {
            throw new Error("At least one field must be provided");
        }
        return true;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { _id: user_id } = req.user._id;
        const tourGuide = yield TourGuide_1.default.findOne({
            user_id: user_id,
        });
        if (tourGuide) {
            return res.status(409).json({ message: `Tour Guide already exists for this user` });
        }
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    try {
        const { maps, streams, events, notifications } = req.body;
        const { _id: user_id } = req.user._id;
        yield TourGuide_1.default.create({ user_id, maps, streams, events, notifications });
        res.status(201).json({ message: `Tour Guide created successfully` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_TOUR_GUIDE), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("maps")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("streams")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("events")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("notifications")
        .optional()
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)().custom((value, { req }) => {
        if (Object.keys(req.body).length === 0) {
            throw new Error("At least one field must be provided");
        }
        return true;
    }),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { maps, streams, events, notifications } = req.body;
        const { _id: user_id } = req.user._id;
        const tourGuide = yield TourGuide_1.default.findOneAndUpdate({ user_id: user_id }, { maps, streams, events, notifications }, { new: true });
        res.json({ message: `Tour Guide has been updated`, tourGuide });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
