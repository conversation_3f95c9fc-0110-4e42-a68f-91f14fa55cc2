import mongoose from "mongoose";

export interface IOrganization {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    domain: string;
    is_internal: boolean;
    is_miscellaneous: boolean;
    created_by: mongoose.Types.ObjectId;
    creation_timestamp: Date;
}

export interface IOrganizationWithUser extends IOrganization {
    user: {
        name: string;
        username: string;
    };
    user_count: number;
}
