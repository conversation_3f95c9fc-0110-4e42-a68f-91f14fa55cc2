import express from "express";
const router = express.Router();

// import userRouter from "./User";
import testRouter from "./Test.route";
// import kinesisRouter from "./Kinesis";
// import regionRouter from "./Region";
// import vesselLocationRouter from "./VesselLocation";
// import roleRouter from "./Role";
// import permissionRouter from "./Permission";
// import artifactRouter from "./Artifact";
// import artifactFavouriteRouter from "./ArtifactFavourites";
// import artifactSuggestionRouter from "./ArtifactSuggestions";
// import logRouter from "./Log";
// import s3Router from "./S3";
import apiKeyRouter from "./ApiKey";
// import apiEndpointRouter from "./ApiEndpoint";
// import statisticsRouter from "./Statistics";
// import vesselsRouter from "./Vessels";
// import geolocationRouter from "./Geolocation";
// import regionGroupRouter from "./RegionGroup";
// import tourGuideRouter from "./TourGuide";
// import notificationAlertRouter from "./NotificationAlert";
// import inAppNotificationRouter from "./InAppNotification";
// import notificationSummaryRouter from "./NotificationSummary";
// import artifactCompletionsRouter from "./ArtifactCompletions";
// import emailDomainsRouter from "./EmailDomains";
// import organizationRouter from "./Organization";
// import homePortsRouter from "./HomePorts";
// import vesselManagementRouter from "./VesselManagement";
// import thingsboardRouter from "./Thingsboard";

// router.use("/users", userRouter);
// router.use("/kinesis", kinesisRouter);
// router.use("/regions", regionRouter);
// router.use("/vesselLocations", vesselLocationRouter);
// router.use("/roles", roleRouter);
// router.use("/permissions", permissionRouter);
// router.use("/artifacts", artifactRouter);
// router.use("/artifactFavourites", artifactFavouriteRouter);
// router.use("/suggestions", artifactSuggestionRouter);
// router.use("/logs", logRouter);
// router.use("/s3", s3Router);
router.use("/apiKeys", apiKeyRouter);
// router.use("/apiEndpoints", apiEndpointRouter);
// router.use("/statistics", statisticsRouter);
// router.use("/vessels", vesselsRouter);
// router.use("/geolocations", geolocationRouter);
// router.use("/regionGroups", regionGroupRouter);
// router.use("/tourGuides", tourGuideRouter);
// router.use("/notificationsAlerts", notificationAlertRouter);
// router.use("/inAppNotifications", inAppNotificationRouter);
// router.use("/summaryReports", notificationSummaryRouter);
// router.use("/completions", artifactCompletionsRouter);
// router.use("/emailsDomain", emailDomainsRouter);
// router.use("/organizations", organizationRouter);
// router.use("/homePorts", homePortsRouter);
// router.use("/vesselManagement", vesselManagementRouter);
// router.use("/thingsboard", thingsboardRouter);
router.use("/test", testRouter);

export default router;