"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const dayjs_1 = __importDefault(require("dayjs"));
const express_1 = __importDefault(require("express"));
const awsKinesis_1 = __importDefault(require("../modules/awsKinesis"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const functions_1 = require("../utils/functions");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const Region_1 = __importDefault(require("../models/Region"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
// import regionGroupService from "../services/RegionGroup.service";
const db_1 = __importDefault(require("../modules/db"));
const timezonesList_1 = require("../utils/timezonesList");
const permissions_1 = require("../utils/permissions");
const Stream_service_1 = __importDefault(require("../services/Stream.service"));
const router = express_1.default.Router();
// const { Readable } = require("node:stream");
// const { getObjectStream } = require("../modules/awsS3");
const authUserApiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    }
    else {
        apiLimiter(req, res, next);
    }
}
router.use("/", conditionalRateLimiter);
router.get("/listStreams", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_STREAMS_LIST), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { region } = req.query;
        let streams = [];
        if (region && typeof region === "string") {
            streams = yield awsKinesis_1.default.listStreams({ region });
        }
        else {
            const awsRegions = (yield Region_1.default.find()).filter((r) => r.is_live);
            const allStreams = yield Promise.all(awsRegions.map((region) => __awaiter(void 0, void 0, void 0, function* () {
                try {
                    return yield awsKinesis_1.default.listStreams({ region: region.value });
                }
                catch (_a) {
                    return [];
                }
            })));
            streams = allStreams.flat();
        }
        // const [regionGroups, vessels] = await Promise.all([
        //     regionGroupService.find(),
        //     vesselService.find({}, { unit_id: 1, name: 1, _id: 1, is_active: 1, thumbnail_compressed_s3_key: 1 }),
        // ]);
        const vessels = yield Vessel_service_1.default.find({}, { unit_id: 1, name: 1, _id: 1, is_active: 1, thumbnail_compressed_s3_key: 1, region_group_id: 1 });
        // filter provisioned streams
        streams = streams.filter((stream) => {
            const vessel = vessels.find((v) => v.unit_id === stream.StreamName);
            if (vessel)
                return (0, functions_1.canAccessVessel)(req, vessel);
            else if (req.user && (0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))
                return true;
            else
                return false;
        });
        streams = streams
            .map((stream) => {
            var _a;
            const vessel = vessels.find((v) => v.unit_id === stream.StreamName);
            // const regionGroup = vessel ? regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString())) : null;
            return Object.assign(Object.assign({}, stream), { 
                // RegionGroupId: regionGroup ? regionGroup._id.toString() : null,
                RegionGroupId: vessel && vessel.region_group_id ? vessel.region_group_id.toString() : undefined, VesselId: vessel ? (_a = vessel._id) === null || _a === void 0 ? void 0 : _a.toString() : undefined, VesselIsActive: vessel ? vessel.is_active : undefined, ThumbnailS3Key: vessel ? vessel.thumbnail_compressed_s3_key || undefined : undefined, VesselName: vessel ? vessel.name : undefined });
        })
            .filter(Boolean);
        res.json(streams);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.get("/dashStreamingSessionURL", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_STREAM_DASH_URL), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("streamName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("streamMode")
        .isString()
        .notEmpty()
        .toUpperCase()
        .isIn(["LIVE", "ON_DEMAND"])
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("minutes")
        .isNumeric()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { streamName, region, streamMode, minutes } = req.query;
        if (!streamName ||
            typeof streamName !== "string" ||
            !region ||
            typeof region !== "string" ||
            !streamMode ||
            typeof streamMode !== "string") {
            return res.status(400).json({ message: "Missing required parameters" });
        }
        const stream = yield Stream_service_1.default.fetchSingle({ unitId: streamName });
        if (!stream)
            return res.status(404).json({ message: "Stream does not exist" });
        if (req.api_key || (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))) {
            const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: stream.unit_id });
            if (!vessel)
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            if (!(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }
        }
        const url = yield awsKinesis_1.default.getDashStreamingSessionURL({
            streamName,
            region,
            streamMode,
            minutes: minutes ? parseInt(minutes, 10) : undefined,
        });
        res.json({ url: url });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
const responseWithZipMediaData = (res, region, streamName, startTime, endTime, targetTimestamp, extension, data, user) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // TODO: tags are legacy. need to use vessel collection
        const tags = yield awsKinesis_1.default.getStreamTags({ streamName });
        const filenameMask = `${(0, functions_1.fileNameTimestamp)()} - ${(0, functions_1.removeSpecialCharsFromFilename)(((tags === null || tags === void 0 ? void 0 : tags.Name) || streamName).replace(/ /g, "_"))}`;
        const collections = yield (0, functions_1.getLocationsCollections)(db_1.default.locationsRaw, startTime, endTime);
        const locationPromises = yield Promise.all(collections.map((collection) => collection
            .aggregate([
            { $match: { timestamp: { $gt: new Date(startTime), $lt: new Date(endTime) } } },
            {
                $project: {
                    _id: 1,
                    latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                    longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                    timestamp: 1,
                    groundSpeed: 1,
                    isStationary: 1,
                },
            },
            { $sort: { timestamp: 1 } },
            { $limit: 1 },
        ])
            .toArray()));
        const allLocations = locationPromises.flat();
        let vesselLocation = allLocations.find((loc) => loc.timestamp >= targetTimestamp);
        // if we can't found the location in the target time span we will get the last one
        if (!vesselLocation) {
            for (const collection of collections.reverse()) {
                const lastLocations = yield collection
                    .aggregate([
                    {
                        $project: {
                            _id: 1,
                            latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                            longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                            timestamp: 1,
                            groundSpeed: 1,
                            isStationary: 1,
                        },
                    },
                    { $sort: { timestamp: -1 } },
                    { $limit: 1 },
                ])
                    .toArray();
                if (lastLocations.length > 0) {
                    vesselLocation = lastLocations[0];
                    break; // Found a location, stop searching
                }
            }
        }
        const metadata = `
Vessel Name: ${tags === null || tags === void 0 ? void 0 : tags.Name}
Location: ${vesselLocation === null || vesselLocation === void 0 ? void 0 : vesselLocation.latitude}${(vesselLocation === null || vesselLocation === void 0 ? void 0 : vesselLocation.latitude) ? "," : ""}${vesselLocation === null || vesselLocation === void 0 ? void 0 : vesselLocation.longitude}
Timestamp: ${(0, dayjs_1.default)(Number(targetTimestamp)).format(user.date_time_format || timezonesList_1.defaultDateTimeFormat)}
Ground Speed: ${vesselLocation === null || vesselLocation === void 0 ? void 0 : vesselLocation.groundSpeed} Knots
                    `;
        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: data, //clipResponse.Payload,
            },
            {
                name: `${filenameMask}.txt`,
                content: metadata,
            },
        ];
        const zip = (0, functions_1.generateZip)(filesData);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });
        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);
        yield new Promise((resolve, reject) => {
            stream.on("error", (err) => {
                console.error("stream error:", err);
                reject(err);
            });
            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                reject(e);
            });
        });
    }
    catch (error) {
        if (!res.headersSent) {
            console.error(`PROC MEDIA ERROR: ${error.message}`);
            res.status(500).json({ message: "Internal server error" });
        }
        res.end();
    }
});
router.get("/getScreenShot", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.GET_SCREENSHOT), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("streamName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("timestamp")
        .isISO8601()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { streamName, region, timestamp: qTimestamp } = req.query;
        if (!streamName || typeof streamName !== "string" || !region || typeof region !== "string") {
            return res.status(400).json({ message: "Missing required parameters" });
        }
        if (req.api_key || (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))) {
            const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: streamName });
            if (!vessel)
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            if (!(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }
        }
        const timestamp = qTimestamp && typeof qTimestamp === "string" ? new Date(qTimestamp).getTime() : Date.now();
        const extension = "jpeg";
        const screenshotData = yield awsKinesis_1.default.getScreenshot(streamName, region, timestamp, extension.toUpperCase());
        if (screenshotData) {
            yield responseWithZipMediaData(res, region, streamName, timestamp, timestamp + 10000, timestamp, extension, screenshotData, req.user);
        }
        else {
            if (!res.headersSent) {
                res.status(500).json({ message: "Cant process the request" });
            }
            res.end();
        }
    }
    catch (error) {
        console.error("Error in get-screenshot route:", error);
        if (!res.headersSent) {
            if (error.name === "ScreenshotNotFoundError" || error.name === "ResourceNotFoundException") {
                res.status(404).json({ message: "Sorry, image not found for the selected timestamp. Please try again." });
            }
            else if (error.name === "InvalidArgumentException") {
                res.status(400).json({ message: "Invalid parameters" });
            }
            else if (error.name === "NotAuthorizedException") {
                res.status(403).json({ message: "Not authorized to access the stream." });
            }
            else {
                res.status(500).json({ message: "Internal server error" });
            }
        }
        res.end();
    }
}));
router.get("/getClip", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.GET_CLIP), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("streamName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("timestamp")
        .optional()
        .isISO8601()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { streamName, region, timestamp } = req.query;
        if (!streamName || typeof streamName !== "string" || !region || typeof region !== "string") {
            return res.status(400).json({ message: "Missing required parameters" });
        }
        if (req.api_key || (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))) {
            const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: streamName });
            if (!vessel)
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            if (!(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }
        }
        let targetTimestamp = timestamp && typeof timestamp === "string" ? new Date(timestamp).getTime() : Date.now();
        // take the window in scope 30 seconds before target timestamp and 30 seconds after
        const offsetBefore = 30000;
        const offsetAfter = 30000;
        const startTime = targetTimestamp - offsetBefore;
        const endTime = targetTimestamp + offsetAfter;
        const clipResponse = yield awsKinesis_1.default.getClip(streamName, region, startTime, endTime);
        if (clipResponse.Payload instanceof Buffer) {
            const extension = clipResponse.ContentType.split("/").pop() || "mp4";
            yield responseWithZipMediaData(res, region, streamName, startTime, endTime, targetTimestamp, extension, clipResponse.Payload, req.user);
        }
        else {
            console.warn("Unexpected Payload type: ", typeof clipResponse.Payload);
            if (!res.headersSent) {
                res.status(500).json({ message: "Unexpected payload type from Streams Service" });
            }
            res.end();
        }
    }
    catch (error) {
        console.error("Error in get-clip route:", error);
        if (!res.headersSent) {
            if (error.name === "ResourceNotFoundException") {
                res.status(404).json({ message: "Sorry, clip not found for the selected timestamp. Please try again." });
            }
            else if (error.name === "InvalidArgumentException") {
                res.status(400).json({ message: "Invalid parameters" });
            }
            else if (error.name === "NotAuthorizedException") {
                res.status(403).json({ message: "Not authorized to access the stream." });
            }
            else {
                res.status(400).json({ message: error.message });
            }
        }
        res.end();
    }
}));
router.get("/hlsStreamingSessionURL", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_STREAM_URL), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("streamName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("streamMode")
        .isString()
        .notEmpty()
        .toUpperCase()
        .isIn(["LIVE", "ON_DEMAND"])
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("minutes")
        .isNumeric()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { streamName, region, streamMode, minutes } = req.query;
        if (!streamName ||
            typeof streamName !== "string" ||
            !region ||
            typeof region !== "string" ||
            !streamMode ||
            typeof streamMode !== "string") {
            return res.status(400).json({ message: "Missing required parameters" });
        }
        const stream = yield Stream_service_1.default.fetchSingle({ unitId: streamName });
        if (!stream)
            return res.status(404).json({ message: "Stream does not exist" });
        if (req.api_key || (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))) {
            const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: stream.unit_id });
            if (!vessel)
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            if (!(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }
        }
        const url = yield awsKinesis_1.default.getHlsStreamingSessionURL({
            streamName,
            region,
            streamMode,
            minutes: minutes ? parseInt(minutes, 10) : undefined,
        });
        res.json({ url: url });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   name: Streams
 *   description: Get data regarding streams
 * components:
 *   schemas:
 *     Stream:
 *       type: object
 *       properties:
 *         DeviceName:
 *           type: string
 *           nullable: true
 *           example: null
 *         StreamName:
 *           type: string
 *           description: The name of the stream.
 *           example: "prototype-24"
 *         StreamARN:
 *           type: string
 *           description: The Amazon Resource Name (ARN) of the stream.
 *           example: "arn:aws:kinesisvideo:ap-southeast-1:123456789012:stream/prototype-24/1724069687123"
 *         MediaType:
 *           type: string
 *           nullable: true
 *           example: null
 *         KmsKeyId:
 *           type: string
 *           description: The KMS key ID used for encryption.
 *           example: "arn:aws:kms:ap-southeast-1:123456789012:alias/aws/kinesisvideo"
 *         Version:
 *           type: string
 *           description: The version of the stream.
 *           example: "ww1ljAmwqPscFzFHDxRO"
 *         Status:
 *           type: string
 *           enum:
 *             - ACTIVE
 *             - INACTIVE
 *           description: The current status of the stream.
 *           example: "ACTIVE"
 *         CreationTime:
 *           type: string
 *           format: date-time
 *           description: The time when the stream was created.
 *           example: "2024-08-19T12:14:47.123Z"
 *         DataRetentionInHours:
 *           type: integer
 *           description: The data retention period in hours.
 *           example: 720
 *         Tags:
 *           type: object
 *           deprecated: true
 *           description: The tags associated with the stream.
 *           properties:
 *             Name:
 *               type: string
 *               example: BRP Malapascua MRRV-4403
 *             Thumbnail:
 *               type: string
 *               example: https://portal.quartermaster.us/4403.jpg
 *           example: {
 *             "Name": "BRP Malapascua MRRV-4403",
 *             "Thumbnail": "https://portal.quartermaster.us/4403.jpg"
 *           }
 *         IsLive:
 *           type: boolean
 *           description: Indicates whether the stream is live.
 *           example: false
 *         Region:
 *           type: string
 *           description: The region of the stream.
 *           example: "us-east-2"
 *         RegionGroupId:
 *           type: string
 *           nullable: true
 *           description: The region group of the stream.
 *           example: "66e000000000000000000000"
 *         VesselId:
 *           type: string
 *           nullable: true
 *           description: The vessel id of the stream.
 *           example: "66e000000000000000000000"
 *         VesselIsActive:
 *           type: boolean
 *           nullable: true
 *           description: "Whether the vessel associated with this stream is active."
 *           example: true
 *         ThumbnailS3Key:
 *           type: string
 *           nullable: true
 *           description: "Whether the vessel associated with this stream is active."
 *           example: true
 *         VesselName:
 *           type: string
 *           nullable: true
 *           description: The name of the vessel associated with this stream.
 *           example: "BRP Malapascua MRRV-4403"
 */
/**
 * @swagger
 * /kinesis/listStreams:
 *   get:
 *     summary: List all available Kinesis streams
 *     description: Fetches a list of all Kinesis video streams from specified region or all regions if no region is specified.
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: region
 *         in: query
 *         required: false
 *         description: AWS region to fetch streams from. If not provided, streams from all active regions will be fetched.
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *     responses:
 *       200:
 *         description: A list of streams
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Stream'
 *       400:
 *         description: Invalid region provided
 *       500:
 *         description: Server error
 */
/**
 * @swagger
 * /kinesis/hlsStreamingSessionURL:
 *   get:
 *     summary: Get HLS streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */
/**
 * @swagger
 * /kinesis/dashStreamingSessionURL:
 *   get:
 *     summary: Get Dash streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */
