import mongoose from "mongoose";
import db from "../modules/db";
import { IStatistics } from "src/interfaces/Statistics";

const statisticsSchema = new mongoose.Schema(
    {
        stats: { type: mongoose.Schema.Types.Mixed, required: true },
        fromTimestamp: { type: Date, required: true, unique: true },
        toTimestamp: { type: Date, required: true, unique: true },
        type: { type: String, required: true, enum: ["weekly", "daily"] },
    },
    { minimize: false },
);

const Statistics = db.qm.model<IStatistics>("Statistics", statisticsSchema);

export default Statistics;
