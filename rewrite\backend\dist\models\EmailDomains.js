"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const EmailDomainSchema = new mongoose_1.default.Schema({
    domain: { type: String, required: true },
});
const EmailDomains = db_1.default.qm.model("EmailDomains", EmailDomainSchema, "email_domains");
exports.default = EmailDomains;
