import express, { Request, Response, NextFunction } from "express";
import assignEndpointId from "../../middlewares/assignEndpointId";
import isAuthenticated from "../../middlewares/auth";
import { validateData } from "../../middlewares/validator";
import { endpointIds } from "../../utils/endpointIds";
import { validateError, canAccessVessel, userHasPermissions } from "../../utils/functions";
import awsKinesis from "../../modules/awsKinesis";
import { query } from "express-validator";
import rateLimit from "express-rate-limit";
import vesselService from "../../services/Vessel.service";
import streamService from "../../services/Stream.service";
import { permissions } from "../../utils/permissions";

const router = express.Router();

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req: Request, res: Response, next: NextFunction): void {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

let tts = Date.now();
router.get(
    "/dashStreamingSessionURL",
    (req, res, next) => {
        tts = Date.now();
        next();
    },
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_URL_V2),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND", "LIVE_REPLAY"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("startTimestamp")
                    .isISO8601()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                query("totalDuration")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        try {
            const { streamName, region, streamMode, startTimestamp, totalDuration } = req.query as {
                streamName: string;
                region: string;
                streamMode: string;
                startTimestamp: string;
                totalDuration: string;
            };

            if (
                !streamName ||
                typeof streamName !== "string" ||
                !region ||
                typeof region !== "string" ||
                !streamMode ||
                typeof streamMode !== "string"
            ) {
                return res.status(400).json({ message: "Missing required parameters" });
            }

            const stream = await streamService.fetchSingle({ unitId: streamName });
            if (!stream) return res.status(404).json({ message: "Stream does not exist" });

            if (req.api_key || (req.user && !userHasPermissions(req.user, [permissions.accessAllVessels]))) {
                const vessel = await vesselService.findByAssignedUnitId({ unitId: stream.unit_id });
                if (!vessel) return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                if (!canAccessVessel(req, vessel)) {
                    return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
                }
            }

            const ts = Date.now();
            const data = await awsKinesis.getDashStreamingSessionURL_V2({
                streamName,
                region,
                streamMode,
                startTimestamp: new Date(startTimestamp).getTime(),
                totalDuration: parseInt(totalDuration, 10),
            });
            console.log("[dashStreamingSessionURL] session url time taken", Date.now() - ts, "ms");

            console.log("[dashStreamingSessionURL] total time taken", Date.now() - tts, "ms");
            res.json({ data });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
