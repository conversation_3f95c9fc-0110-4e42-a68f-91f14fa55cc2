import React, { useCallback } from "react";
import { IconButton, Skeleton } from "@mui/material";
import { OpenInNew } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import InsetMap from "../../../components/InsetMap";
import { useApp } from "../../../hooks/AppHook";

const Map = ({ vessel, newCoordinate, streamMode, allVesselCoordinates, onSelectVessel, artifactIndicator }) => {
    const { setSelectedVessel, google } = useApp();
    const navigate = useNavigate();

    const handleButtonClick = useCallback(() => {
        setSelectedVessel(vessel.vesselId);
        navigate("/dashboard/map");
    }, [vessel.vesselId, setSelectedVessel, navigate]);

    return (
        <>
            {!google ? (
                <Skeleton data-testid="loading-skeleton" animation="wave" variant="rectangular" height={"100%"} />
            ) :
                <InsetMap
                    vessel={vessel}
                    newCoordinate={newCoordinate}
                    initialZoom={0.5}
                    streamMode={streamMode}
                    allVesselCoordinates={allVesselCoordinates}
                    onSelectVessel={onSelectVessel}
                    artifactIndicator={artifactIndicator}
                />
            }
            <IconButton onClick={handleButtonClick} sx={{ position: "absolute", bottom: 25, left: 1, zIndex: 0 }} disableRipple>
                <OpenInNew sx={{ color: "#000" }} />
            </IconButton>
        </>
    );
};

export default React.memo(Map);
