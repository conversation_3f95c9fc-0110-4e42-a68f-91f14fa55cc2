"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateData = void 0;
const express_validator_1 = require("express-validator");
const validateData = (rules, req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    // Run validation rules and gather validation errors
    return Promise.all(rules.map((rule) => rule.run(req))).then(() => {
        const errors = (0, express_validator_1.validationResult)(req);
        // If there are validation errors, respond with a 400 Bad Request status and error messages
        if (errors.isEmpty()) {
            next();
        }
        else {
            console.log(errors.array());
            return res.status(400).json({ message: errors.array()[0].msg });
        }
    });
});
exports.validateData = validateData;
