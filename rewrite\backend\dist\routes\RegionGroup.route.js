"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const permissions_1 = require("../utils/permissions");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const mongoose_1 = __importStar(require("mongoose"));
const timezonesList_1 = require("../utils/timezonesList");
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const RegionGroup_service_1 = __importDefault(require("../services/RegionGroup.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_REGION_GROUPS), auth_1.default, (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const regionGroups = yield RegionGroup_service_1.default.find();
        res.json(regionGroups);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_REGION_GROUP), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRegionsGroups]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("timezone")
        .isString()
        .notEmpty()
        .custom(timezonesList_1.isValidTimezoneOffset)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    // body("vessel_ids")
    //     .isArray()
    //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    // body("vessel_ids.*")
    //     .isString()
    //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, timezone } = req.body;
        // const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
        // const vesselsAlreadyInGroup = await RegionGroup.find({ vessel_ids: { $in: objectIds } }, { _id: 1 });
        // if (vesselsAlreadyInGroup.length > 0) {
        //     res.status(409).json({ message: `Vessel is already assigned to a region group.` });
        //     return;
        // }
        const regionGroup = yield RegionGroup_service_1.default.create({
            name,
            timezone,
            // vessel_ids: vessel_ids.map((id) => mongoose.Types.ObjectId(id)),
            created_by: new mongoose_1.default.Types.ObjectId(req.user._id),
        });
        res.json({ message: `Region group has been created`, regionGroup });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.UPDATE_REGION_GROUP), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRegionsGroups]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("name")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.body)("timezone")
        .isString()
        .notEmpty()
        .custom(timezonesList_1.isValidTimezoneOffset)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    // body("vessel_ids")
    //     .isArray()
    //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    // body("vessel_ids.*")
    //     .isString()
    //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const data = req.body;
        // const { vessel_ids } = data;
        // const objectIds = vessel_ids.map((id) => mongoose.Types.ObjectId(id));
        // const vesselsAlreadyInGroup = await RegionGroup.find({ _id: { $ne: req.params.id }, vessel_ids: { $in: objectIds } }, { _id: 1 });
        // if (vesselsAlreadyInGroup.length > 0) {
        //     res.status(409).json({ message: `Vessel is already assigned to a region group.` });
        //     return;
        // }
        const result = yield RegionGroup_service_1.default.update(Object.assign({ id: req.params.id }, data));
        if (!result)
            return res.status(400).json({ message: `Region group does not exist` });
        return res.json({ message: `Region group '${data.name}' has been edited`, regionGroup: result });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_REGION_GROUP), auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageRegionsGroups]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const result = yield RegionGroup_service_1.default.delete({ id });
        if (!result)
            return res.status(400).json({ message: `Region group does not exist` });
        return res.json({ message: `Region group has been deleted` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
/**
 * @swagger
 * tags:
 *   name: Region Groups
 *   description: Fetch region groups
 * components:
 *   schemas:
 *     RegionGroup:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the region group
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: Name of the region group
 *           example: Philipines
 *         timezone:
 *           type: string
 *           description: Timezone of the region group
 *           example: +8:00
 *         vessels:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               vessel_id:
 *                 type: string
 *                 description: Document Id of the vessel
 *                 example: 67942a74a7f838634a00190a
 *               unit_id:
 *                 type: string
 *                 description: Unit id of the camera
 *                 example: prototype-24
 *               name:
 *                 type: string
 *                 description: Name of the vessel
 *                 example: BRP Bagacay (MRRV-4410)
 *         created_by:
 *           type: object
 *           properties:
 *             _id:
 *               type: string
 *               description: Document Id of the user
 *               example: 67942a74a7f838634a00190a
 *             name:
 *               type: string
 *               description: Name of the user
 *               example: John Doe
 *         creationTimestamp:
 *           type: string
 *           description: Creation timestamp of the region group
 *           example: 2025-06-24T10:00:00.000Z
 */
/**
 * @swagger
 * /regionGroups:
 *   get:
 *     summary: Fetch all region groups
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Region Groups]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of region groups
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/RegionGroup'
 *       500:
 *         description: Server error
 */
