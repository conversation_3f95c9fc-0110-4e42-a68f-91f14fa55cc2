"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const userEndpointRestrictions_1 = require("../utils/userEndpointRestrictions");
const restrictEndpointByUser = (req, res, next) => {
    const endpointId = req._endpoint_id;
    if (endpointId && userEndpointRestrictions_1.userEndpointRestrictions[endpointId]) {
        if (process.env.NODE_ENV !== "portal") {
            return res.status(403).json({ message: "Access denied. Operation restricted to portal environment." });
        }
        const allowedUsers = userEndpointRestrictions_1.userEndpointRestrictions[endpointId];
        if (!allowedUsers.includes(req.user._id.toString())) {
            return res.status(403).json({ message: "Access denied. Operation restricted to specific user(s)." });
        }
    }
    next();
};
exports.default = restrictEndpointByUser;
