import { useEffect, useMemo, useState, useRef, useCallback } from "react";
import { UserProvider } from "../../../providers/UserProvider";
import { StoreProvider } from "../../../providers/StoreProvider";
import dayjs from "dayjs";
import { defaultValues, handleVesselTimezone, simplifyTimezone, permissions, displayCoordinates } from "../../../utils";
import { Grid, Typography, IconButton, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import PreviewMedia from "../../../components/PreviewMedia";
import { getSocket } from "../../../socket";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import artifactController from "../../../controllers/Aritfact.controller";

const ClusterInfoWindow = ({ currentArtifacts, currentClusterInfoWindow, vesselInfo, user ,ts }) => {
    const tss = new Date().getTime();
    console.log("Cluster info window render", tss- ts , "ms");
    const [currentIndex, setCurrentIndex] = useState(0);
    const [unifiedIndex, setUnifiedIndex] = useState(0);
    const [archivedIds, setArchivedIds] = useState([]);
    // const artifacts = markers.map((marker) => marker.artifactData).filter((artifact) => !archivedIds.includes(artifact._id));
    
    const artifacts = useMemo(() => {
        const filteredArtifacts = currentArtifacts.filter((artifact) => !archivedIds.includes(artifact._id));
        // Ensure currentIndex doesn't go out of bounds
        if (currentIndex >= filteredArtifacts.length && filteredArtifacts.length > 0) {
            setCurrentIndex(filteredArtifacts.length - 1);
        }
        return filteredArtifacts;
    }, [currentArtifacts, archivedIds]);

    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    // const artifact = artifacts[currentIndex];
    const baseArtifact = useMemo(() => {
        return artifacts[currentIndex];
    }, [artifacts, currentIndex])
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [loading, setLoading] = useState(true);
    const [detailLoading, setDetailLoading] = useState(true);
    // const [modalExapand, setModalExpand] = useState(false);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);
    const [fullArtifact, setFullArtifact] = useState(null);
    const artifactCache = useRef(new Map());
    const prefetchingRef = useRef(new Set());
    // Add AbortController and request tracking
    const abortControllerRef = useRef(null);
    const currentRequestIdRef = useRef(0);

    const isUnified = useMemo(() => {
        return baseArtifact && baseArtifact.duplications.length >= 1;
    }, [baseArtifact]);

    const totalUnified = useMemo(() => {
        return isUnified ? baseArtifact.duplications.length + 1 : 0;
    }, [isUnified, baseArtifact]);
    const currentArtifact = useMemo(() => {
        if (!isUnified || unifiedIndex === 0) {
            return baseArtifact;
        }
        const duplicationIndex = unifiedIndex - 1;
        const duplication = baseArtifact?.duplications?.[duplicationIndex];
        return duplication || baseArtifact;
    }, [baseArtifact, isUnified, unifiedIndex]);

    const fetchArtifactDetails = async (artifactId, useCache = true) => {
        // Cancel any pending request
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        // Create new AbortController for this request
        abortControllerRef.current = new AbortController();
        const currentRequestId = ++currentRequestIdRef.current;
        if (useCache && artifactCache.current.has(artifactId)) {
            return artifactCache.current.get(artifactId);
        }

        try {
            const fullArtifactData = await artifactController.getArtifactDetail(artifactId, abortControllerRef.current.signal);

            // Only process the response if this is still the current request
            if (currentRequestId === currentRequestIdRef.current) {
                // Cache the result
                artifactCache.current.set(artifactId, fullArtifactData);
                return fullArtifactData;
            } else {
                // This response is from a stale request, ignore it
                // console.log(`Ignoring stale response for artifact: ${artifactId}, currentRequestId: ${currentRequestId}, latestRequestId: ${currentRequestIdRef.current}`);
                return null;
            }
        } catch (error) {
            if (error.name === "AbortError" || error.name === "CanceledError" || error.code === "ERR_CANCELED") {
                // console.log(`Request was aborted for artifact: ${artifactId}, requestId: ${currentRequestId}`);
                return null;
            }
            console.error("Error fetching artifact details:", error);
            // Fallback to using the minimal artifact data
            const minimalArtifact = artifacts.find((a) => a._id === artifactId);
            return minimalArtifact;
        }
    };

    const prefetchNextArtifact = async () => {
        const nextIndex = (currentIndex + 1) % artifacts.length;
        const nextArtifact = artifacts[nextIndex];

        if (nextArtifact && !prefetchingRef.current.has(nextArtifact._id) && !artifactCache.current.has(nextArtifact._id)) {
            prefetchingRef.current.add(nextArtifact._id);

            // Create a separate AbortController for prefetching
            const prefetchAbortController = new AbortController();

            try {
                const prefetchData = await artifactController.getArtifactDetail(nextArtifact._id, prefetchAbortController.signal);
                // Cache the result if the request wasn't aborted
                if (!prefetchAbortController.signal.aborted) {
                    artifactCache.current.set(nextArtifact._id, prefetchData);
                }
            } catch (error) {
                if (error.name !== "AbortError") {
                    console.error("Error prefetching artifact:", error);
                }
            } finally {
                prefetchingRef.current.delete(nextArtifact._id);
            }
        }
    };
    const thumbRef = useRef(null);
    const imageRef = useRef(null);
    useEffect(() => {
        const artifact = isUnified && unifiedIndex > 0 ? currentArtifact : baseArtifact;
        if (artifact && artifacts.length > 0) {
            // setLoading(true);
            fetchArtifactDetails(artifact._id).then((fullArtifactData) => {
                // Only update state if this is still the current artifact
                const thumbnailUrl = fullArtifactData.thumbnail_url;
                const videoUrl = fullArtifactData.video_url;
                const imageUrl = fullArtifactData.image_url;

                if (fullArtifactData && artifact._id === currentArtifact._id) {
                    setFullArtifact(fullArtifactData);
                    if (unifiedIndex === 0) {
                        if (fullArtifactData.video_path) {
                            setThumbnail(thumbnailUrl || imageUrl || null);
                            setSrc(videoUrl || null);
                            thumbRef.current = thumbnailUrl || imageUrl || null;
                            imageRef.current = videoUrl || null;

                        } else {
                            setThumbnail(thumbnailUrl || imageUrl || null);
                            setSrc(imageUrl || null);
                            thumbRef.current = thumbnailUrl || imageUrl || null;
                            imageRef.current = imageUrl || null;
                        }

                    } else {
                        setThumbnail(thumbRef.current);
                        setSrc(imageRef.current);
                    }

                    setLoading(false);
                    setDetailLoading(false);
                    // setModalExpand(false);

                    // Prefetch next artifact
                    prefetchNextArtifact();
                }
            });
        }
    }, [baseArtifact, currentIndex, artifacts, currentArtifact, unifiedIndex]);


    const vesselTimezone = useMemo(() => {
        const timezone = handleVesselTimezone(fullArtifact || baseArtifact, vesselInfo);
        return timezone;
    }, [fullArtifact, baseArtifact, vesselInfo]);

    const handleNavigation = (direction) => {
        // Cancel any pending request before navigation
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }

        setLoading(true);
        setCurrentIndex((prevIndex) => {
            if (direction === "prev") {
                return prevIndex > 0 ? prevIndex - 1 : artifacts.length - 1;
            } else {
                return (prevIndex + 1) % artifacts.length;
            }
        });
        setUnifiedIndex(0);
    };
    const handleUnifiedPrevious = useCallback(() => {
        setDetailLoading(true); // this is for unified navigation to load only detail part
        if (unifiedIndex > 0) {
            setUnifiedIndex(unifiedIndex - 1);
        }
    }, [unifiedIndex]);

    const handleUnifiedNext = useCallback(() => {
        setDetailLoading(true);
        if (unifiedIndex < totalUnified - 1) {
            setUnifiedIndex(unifiedIndex + 1);
        }
    }, [unifiedIndex, totalUnified]);


    const handleClose = () => {
        // Cancel any pending request when closing
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        currentClusterInfoWindow.close();
    };
    const fetchUserFlaggedArtifacts = async () => {
        await artifactFlagController.getUserFlaggedArtifactIds();
        if (!baseArtifact) return;
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(baseArtifact._id));
    };

    useEffect(() => {
        fetchUserFlaggedArtifacts();
    }, []);

    // const showClick = () => {
    //     setModalExpand((prev) => !prev);
    // };

    useEffect(() => {
        const socket = getSocket();
        const handleLocalArchive = (e) => {
            const updatedArtifact = e?.artifact;
            if (!updatedArtifact) return;
            setArchivedIds((prev) => [...prev, updatedArtifact._id]);
            // Clear the cache for the archived artifact to prevent stale data
            artifactCache.current.delete(updatedArtifact._id);
            // Clear the current artifact view if it was the one archived
            setFullArtifact(null);
            setSrc(null);
            setThumbnail(null);
            setLoading(true);
        };
        socket.on("artifact/changed", handleLocalArchive);
        socket.on("artifacts_flagged/changed", fetchUserFlaggedArtifacts);
        return () => {
            socket.off("artifact/changed", handleLocalArchive);
            socket.off("artifacts_flagged/changed", fetchUserFlaggedArtifacts);
        };
    }, []);

    // Cleanup effect to cancel pending requests on unmount
    useEffect(() => {
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    return (
        <Grid
            container
            direction="column"
            style={{
                color: "white",
                padding: "20px",
                background: "#343B44",
                maxWidth: "330px",
                height: "570px",
                display: "flex",
                flexDirection: "column",
            }}
        >
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                    .content-scroll::-webkit-scrollbar {
                        width: 2px;
                    }
                    .content-scroll::-webkit-scrollbar-track {
                        background: #343B44;
                        border-radius: 1px;
                    }
                    .content-scroll::-webkit-scrollbar-thumb {
                        background: #fff;
                        border-radius: 1px;
                    }
                    .content-scroll::-webkit-scrollbar-thumb:hover {
                        background: #ccc;
                    }
                `}
            </style>
            <Grid sx={{ height: "230px", marginBottom: "20px" }}>
                <Grid container justifyContent="space-between" alignItems="center" style={{ marginBottom: "10px" }}>
                    <Typography variant="h6">Artifact {artifacts.length > 1 ? `${currentIndex + 1} / ${artifacts.length}` : ""}</Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: "white",
                            border: "1px solid white",
                            "&:hover": {
                                backgroundColor: "white",
                                color: "#4F5968",
                            },
                        }}
                    >
                        <CloseIcon sx={{ fontSize: "16px" }} />
                    </IconButton>
                </Grid>
                {/* Image/Video Section */}
                <Grid
                    sx={{
                        position: "relative",
                        backgroundColor: "#343B44",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        height: 200,
                        borderRadius: 1,
                    }}
                >
                    {loading ? (
                        <Skeleton
                            variant="rectangular"
                            width="100%"
                            height="100%"
                            sx={{
                                borderRadius: 1,
                                minHeight: 200,
                                minWidth: 290,
                            }}
                        />
                    ) : (
                        <PreviewMedia
                            thumbnailLink={thumbnail}
                            originalLink={src}
                            cardId={fullArtifact?._id || currentArtifact._id}
                            isImage={!fullArtifact?.video_path}
                            style={{ borderRadius: 8 }}
                            showFullscreenIcon={true}
                            showFullscreenIconForMap={!fullArtifact?.video_path}
                            // userTest={user}
                            showVideoThumbnail={fullArtifact?.video_path}
                            showArchiveButton={hasManageArtifacts}
                            isArchived={fullArtifact?.portal?.is_archived}
                            vesselId={fullArtifact?.onboard_vessel_id}
                            skeletonStyle={{
                                minHeight: 200,
                                minWidth: 290,
                            }}
                            flaggedArtifact={flaggedArtifact}
                            isBounding={isUnified}
                            det_nbbox={fullArtifact?.det_nbbox}
                            isUnified={isUnified}
                            unifiedArtifacts={isUnified ? [baseArtifact, ...(baseArtifact?.duplications || [])] : []}

                        />
                    )}
                </Grid>
            </Grid>
            {isUnified && (
                <Grid container justifyContent="center" sx={{ marginBottom: "10px" }}>
                    <Grid container justifyContent="space-between" alignItems="center" sx={{ maxWidth: "250px" }}>
                        <IconButton
                            onClick={handleUnifiedPrevious}
                            disabled={unifiedIndex === 0}
                            sx={{
                                color: "white",
                                fontSize: "20px",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                                "&:disabled": {
                                    color: "rgba(255,255,255,0.3)",
                                },
                            }}
                        >
                            <ChevronLeftIcon sx={{ fontSize: "20px" }} />
                        </IconButton>
                        <Typography
                            variant="caption"
                            sx={{
                                color: "white",
                                fontWeight: 500,
                                minWidth: "40px",
                                textAlign: "center",
                                padding: "5px 17px",
                                borderRadius: "100px",
                                // backgroundColor: "rgba(255,255,255,0.1)",
                            }}
                        >
                            Total Detections ({String(unifiedIndex + 1).padStart(2, "0")}/{String(totalUnified).padStart(2, "0")})
                        </Typography>
                        <IconButton
                            onClick={handleUnifiedNext}
                            disabled={unifiedIndex === totalUnified - 1}
                            sx={{
                                color: "white",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                                "&:disabled": {
                                    color: "rgba(255,255,255,0.3)",
                                },
                            }}
                        >
                            <KeyboardArrowRightIcon sx={{ fontSize: "20px" }} />
                        </IconButton>
                    </Grid>
                </Grid>
            )}
            <Grid
                sx={{
                    maxHeight: "265px",
                    overflowY: "auto",
                    flex: 1,
                    display: "flex",
                    flexDirection: "column",
                    paddingRight: "2px",
                    marginTop: "10px",
                }}
                className="content-scroll"
            >
                {loading || detailLoading || !fullArtifact ? (
                    <>
                        <Skeleton variant="text" width="80%" height={24} sx={{ marginTop: "5px" }} />
                        <Skeleton variant="text" width="70%" height={24} />
                        <Skeleton variant="text" width="60%" height={24} />
                        <Skeleton variant="text" width="75%" height={24} />
                        <Skeleton variant="text" width="90%" height={24} />
                        <Skeleton variant="text" width="85%" height={24} />
                        <Skeleton variant="text" width="95%" height={24} />
                        <Skeleton variant="text" width="90%" height={24} />
                        <Skeleton variant="text" width="85%" height={24} />
                        <Skeleton variant="text" width="95%" height={24} />
                    </>
                ) : (
                    <>
                        <Typography>
                            <strong>Timestamp</strong>:{" "}
                            {fullArtifact.timestamp
                                ? dayjs(fullArtifact.timestamp).tz(vesselTimezone).format(defaultValues.dateTimeFormat())
                                : "Not available"}{" "}
                            {vesselTimezone && simplifyTimezone(vesselTimezone)}
                        </Typography>
                        <Typography>
                            <strong>Location:</strong>{" "}
                            {displayCoordinates([fullArtifact.location?.coordinates?.[1], fullArtifact.location?.coordinates?.[0]], !!user?.use_MGRS)}
                            <br />
                        </Typography>
                        <Typography>
                            <strong>Super Category</strong>: {fullArtifact.super_category || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Category</strong>: {fullArtifact.category || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Size</strong>: {fullArtifact.size || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Color</strong>: {fullArtifact.color || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Orientation:</strong> {fullArtifact.vessel_orientation || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Detected Country:</strong> {fullArtifact.home_country || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Bearing Angle:</strong>{" "}
                            {fullArtifact.true_bearing ? `${Number(fullArtifact.true_bearing).toFixed(2)}\u00B0` : "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Features:</strong> {fullArtifact.vessel_features || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Description:</strong> {fullArtifact.others || "Not available"}
                        </Typography>
                        {/* {fullArtifact.others ? (
                            <Typography sx={{ textTransform: "lowercase" }}>
                                {fullArtifact.others &&
                                    (modalExapand || fullArtifact.others.length <= 90
                                        ? fullArtifact.others
                                        : fullArtifact.others.slice(0, 90) + "...")}
                                {fullArtifact.others.length > 90 && (
                                    <span onClick={showClick} style={{ color: "#007bff", cursor: "pointer", marginLeft: "5px" }}>
                                        {modalExapand ? "Show Less" : "Read More"}
                                    </span>
                                )}
                            </Typography>
                        ) : (
                            <Typography>{"Not available"}</Typography>
                        )} */}
                    </>
                )}
            </Grid>
            <Grid
                sx={{
                    height: "35px",
                    flexShrink: 0,
                    marginTop: "auto",
                }}
            >
                {artifacts.length > 1 && (
                    <Grid container justifyContent="space-between" sx={{ marginBottom: "1px" }}>
                        <IconButton
                            onClick={() => handleNavigation("prev")}
                            sx={{
                                color: "white",
                                fontSize: "24px",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <ChevronLeftIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                        <IconButton
                            onClick={() => handleNavigation("next")}
                            sx={{
                                color: "white",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <KeyboardArrowRightIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                    </Grid>
                )}
            </Grid>
        </Grid>
    );
};

const WrappedClusterInfoWindow = (props) => {
    return (
        <UserProvider>
            <StoreProvider>
                <ClusterInfoWindow {...props} />
            </StoreProvider>
        </UserProvider>
    );
};

export default WrappedClusterInfoWindow;
