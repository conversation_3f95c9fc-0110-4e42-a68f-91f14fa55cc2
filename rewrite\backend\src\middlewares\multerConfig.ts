import multer from "multer";
import { Request, Response, NextFunction } from "express";
import { IFile } from "src/interfaces/File";

const storage = multer.memoryStorage();

const fileFilter = (req: Request, file: IFile, cb: multer.FileFilterCallback) => {
    if (file.mimetype.startsWith("image/")) {
        cb(null, true);
    } else {
        cb(new Error("Only image files are allowed!"));
    }
};

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: fileFilter,
});

const handleMulterError = (error: Error, req: Request, res: Response, next: NextFunction) => {
    if (error instanceof multer.MulterError) {
        if (error.code === "LIMIT_FILE_SIZE") {
            return res.status(400).json({
                message: "File too large. Maximum size is 5MB.",
            });
        }
        if (error.code === "LIMIT_UNEXPECTED_FILE") {
            return res.status(400).json({
                message: "Unexpected field name for file upload.",
            });
        }
        return res.status(400).json({
            message: "File upload error: " + error.message,
        });
    }

    if (error.message === "Only image files are allowed!") {
        return res.status(400).json({
            message: "Only image files are allowed!",
        });
    }

    next(error);
};

export { upload, handleMulterError };
