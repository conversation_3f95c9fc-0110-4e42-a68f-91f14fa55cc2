"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const hasPermission_1 = __importDefault(require("../middlewares/hasPermission"));
const permissions_1 = require("../utils/permissions");
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const functions_1 = require("../utils/functions");
const mongoose_1 = __importStar(require("mongoose"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const ApiKey_service_1 = __importDefault(require("../services/ApiKey.service"));
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.get("/", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const apiKeys = yield ApiKey_service_1.default.fetchAll();
        res.json(apiKeys);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.post("/", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.body)("description")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("email")
        .optional()
        .isEmail()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { description, email } = req.body;
        yield ApiKey_service_1.default.create({ description, email, created_by: req.user._id.toString() });
        res.json({ message: `Api Key has been created` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/allowedEndpoints", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_endpoints")
        .isArray()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.check)("allowed_endpoints.*")
        .custom(functions_1.isIntStrict)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { allowed_endpoints } = req.body;
        const result = yield ApiKey_service_1.default.updateAllowedEndpoints({ id: req.params.id, allowed_endpoints });
        if (!result)
            return res.status(404).json({ message: `Api Key does not exist` });
        return res.json({ message: `Api Key has been updated` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/revoke", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("revoke")
        .isBoolean()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { revoke } = req.body;
        const result = yield ApiKey_service_1.default.updateRevocationStatus({ id: req.params.id, is_revoked: revoke });
        if (!result)
            return res.status(404).json({ message: `Api Key does not exist` });
        return res.json({ message: `Api Key has been updated` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.delete("/:id", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = yield ApiKey_service_1.default.delete({ id: req.params.id });
        if (!result)
            return res.status(404).json({ message: `Api Key does not exist` });
        return res.json({ message: `Api Key has been deleted` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/allowedVessels", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels")
        .isArray()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("allowed_vessels.*")
        .custom(mongoose_1.isValidObjectId)
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { allowed_vessels } = req.body;
        const apiKey = yield ApiKey_service_1.default.updateAllowedVessels({ id, allowed_vessels });
        if (!apiKey)
            return res.status(404).json({ message: "API key not found" });
        res.json(apiKey);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
router.patch("/:id/details", auth_1.default, hasPermission_1.default.bind(this, [permissions_1.permissions.manageApiKeys]), validator_1.validateData.bind(this, [
    (0, express_validator_1.param)("id")
        .custom(mongoose_1.isValidObjectId)
        .bail()
        .customSanitizer((v) => new mongoose_1.default.Types.ObjectId(v))
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("email")
        .optional()
        .isEmail()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.body)("description")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email, description } = req.body;
        const result = yield ApiKey_service_1.default.update({ id: req.params.id, email, description });
        if (!result)
            return res.status(404).json({ message: `Api Key does not exist` });
        return res.json({ message: `Api Key details have been updated` });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
