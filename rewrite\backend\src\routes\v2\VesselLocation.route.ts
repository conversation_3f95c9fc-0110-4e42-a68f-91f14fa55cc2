import express, { Request, Response, NextFunction } from "express";
import { validateData } from "../../middlewares/validator";
import { body } from "express-validator";
import { validateError, canAccessVessel, getLocationsCollections } from "../../utils/functions";
import limitPromise from "../../modules/pLimit";
import mongoose from "mongoose";
import isAuthenticated from "../../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../../middlewares/assignEndpointId";
import { endpointIds } from "../../utils/endpointIds";
import db from "../../modules/db";
import compression from "compression";
import vesselService from "../../services/Vessel.service";
import vesselLocationService from "../../services/VesselLocation.service";
import { IQueryFilter } from "src/interfaces/Common";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.post(
    "/:vesselId",
    assignEndpointId.bind(this, endpointIds.FETCH_COORDINATES_V2),
    isAuthenticated,
    (req: Request, res: Response, next: NextFunction) =>
        validateData(
            [
                // the below cannot be verified by test cases
                // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
                body("lastKnown")
                    .customSanitizer((v: string | number) => Number(v))
                    .isInt({ min: 0, max: 1 })
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("startTimestamp")
                    .isInt()
                    .customSanitizer((v: string | number) => Number(v))
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("endTimestamp")
                    .isInt()
                    .customSanitizer((v: string | number) => Number(v))
                    .withMessage((value: string | number, { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
                body("excludeIds")
                    .isArray()
                    .bail()
                    .customSanitizer((v: string[]) => v.map((id: string) => new mongoose.Types.ObjectId(id)))
                    .withMessage((value: string[], { path }: { path: string }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselId } = req.params as { vesselId: string };
            const { startTimestamp, endTimestamp, lastKnown, excludeIds } = req.body as {
                startTimestamp?: number;
                endTimestamp?: number;
                lastKnown?: number;
                excludeIds?: mongoose.Types.ObjectId[];
            };

            console.log(`/v2/vesselLocations ${vesselId}`, startTimestamp, endTimestamp, lastKnown);

            const ts = new Date().getTime();

            if (endTimestamp && !startTimestamp) {
                return res.status(400).json({ message: "startTimestamp is required when endTimestamp is provided" });
            }

            const vessel = await vesselService.findById({ id: vesselId });
            if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

            if (!canAccessVessel(req, vessel)) {
                return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
            }

            if (lastKnown) {
                // const lastLocation = await db.lookups.collection("last_locations_lookup").findOne({ vessel_id: vessel._id }, {
                //     projection: {
                //         _id: '$last_location_id',
                //         longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                //         latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                //         timestamp: '$data.timestamp',
                //         groundSpeed: '$data.groundSpeed',
                //         isStationary: '$data.isStationary'
                //     }
                // });

                const lastLocation = await vesselLocationService.findLastKnownLocation({
                    vesselId,
                    projection: {
                        _id: "$last_location_id",
                        longitude: { $arrayElemAt: ["$data.location.coordinates", 0] },
                        latitude: { $arrayElemAt: ["$data.location.coordinates", 1] },
                        timestamp: "$data.timestamp",
                        groundSpeed: "$data.groundSpeed",
                        isStationary: "$data.isStationary",
                    },
                });

                console.log(`/v2/vesselLocations ${vesselId} time taken to query ${new Date().getTime() - ts}`);
                console.log(`/v2/vesselLocations ${vesselId} received ${lastLocation ? 1 : 0} coordinates`);
                console.log(`/v2/vesselLocations ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

                return res.json(lastLocation);
            }

            const collections = await getLocationsCollections(db.locationsOptimized, startTimestamp || 0, endTimestamp || Date.now());
            const query: IQueryFilter = { "metadata.onboardVesselId": new mongoose.Types.ObjectId(vesselId) };

            if (startTimestamp) {
                const endTime = endTimestamp || Date.now();
                query.timestamp = { $gt: new Date(startTimestamp), $lt: new Date(endTime) };
            }
            if (excludeIds) query._id = { $nin: excludeIds };

            const locations = await limitPromise(async () => {
                if (isClosed) return res.end();
                console.log(`/v2/vesselLocations ${vesselId} querying DB`);

                const resultPromises = collections.map((collection: mongoose.Collection) => {
                    const cursor = collection.aggregate([
                        { $match: query },
                        {
                            $project: {
                                _id: 1,
                                timestamp: 1,
                                groundSpeed: 1,
                                isStationary: 1,
                                latitude: { $arrayElemAt: ["$location.coordinates", 1] },
                                longitude: { $arrayElemAt: ["$location.coordinates", 0] },
                            },
                        },
                    ]);

                    if (isSwagger) {
                        cursor.limit(20);
                    }

                    return cursor.toArray();
                });

                const allResults = await Promise.all(resultPromises);
                const flattenedResults = allResults.flat();

                return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            });

            console.log(`/v2/vesselLocations ${vesselId} time taken to query ${new Date().getTime() - ts}`);
            console.log(`/v2/vesselLocations ${vesselId} received ${(Array.isArray(locations) && locations.length) || 1} coordinates`);
            console.log(`/v2/vesselLocations ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(locations);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

/**
 * @swagger
 * tags:
 *   name: Vessel Locations
 *   description: Fetch vessel location data
 * components:
 *   schemas:
 *     VesselLocation:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the vessel location
 *           example: "66e75fc080f445d4f062b294"
 *         latitude:
 *           type: number
 *           format: float
 *           description: Latitude of the vessel's location
 *           example: 8.333152
 *         longitude:
 *           type: number
 *           format: float
 *           description: Longitude of the vessel's location
 *           example: 117.2075473
 *         groundSpeed:
 *           type: number
 *           format: float
 *           description: Ground speed in knots
 *           example: 12.5
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp of the location record
 *           example: "2023-06-16T12:00:00.000Z"
 *         isStationary:
 *           type: boolean
 *           description: Indicates whether the vessel is stationary at this location
 *           example: false
 */

/**
 * @swagger
 * /v2/vesselLocations/{vesselId}:
 *   post:
 *     summary: Fetch vessel location data
 *     description: Fetches location data for a specific vessel, with options for historical data or last known position
 *     tags: [Vessel Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: vesselId
 *         in: path
 *         required: true
 *         description: The ID of the vessel to fetch locations for
 *         schema:
 *           type: string
 *           example: 683df46b073245cf0fd62bb9
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lastKnown:
 *                 type: integer
 *                 enum: [0, 1]
 *                 description: Flag to only fetch last known location (0 for false, 1 for true)
 *                 default: 0
 *                 example: 0
 *               startTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: Start unix timestamp in milliseconds for filtering location data
 *                 example: 1727136000000
 *               endTimestamp:
 *                 type: integer
 *                 required: false
 *                 description: End unix timestamp in milliseconds for filtering location data
 *                 example: 1727222400000
 *               excludeIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 required: false
 *                 description: Array of document IDs to exclude from the result
 *                 example: ["64c6b0f7f83c8b57fa5f3242", "64c6b0f7f83c8b57fa5f3243"]
 *     responses:
 *       200:
 *         description: An array of vessel location coordinates
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - description: Array of locations when lastKnown=0 or not provided
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/VesselLocation'
 *                 - allOf:
 *                   - description: Single location object when lastKnown=1
 *                   - $ref: '#/components/schemas/VesselLocation'
 *                 - description: When no locations are found
 *                   type: "null"
 *       400:
 *         description: Invalid request or vessel name
 *       403:
 *         description: Cannot access this unit
 *       500:
 *         description: Internal server error
 */

export default router;
