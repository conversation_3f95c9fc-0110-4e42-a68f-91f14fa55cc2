import express, { Request, Response } from "express";
import { query } from "express-validator";
import assignEndpointId from "../middlewares/assignEndpointId";
import isAuthenticated from "../middlewares/auth";
import { validateData } from "../middlewares/validator";
import { validateError, canAccessVessel } from "../utils/functions";
import { endpointIds } from "../utils/endpointIds";
import limitPromise from "../modules/pLimit";
import db from "../modules/db";
import vesselService from "../services/Vessel.service";
import rateLimit from "express-rate-limit";
import compression from "compression";
import { isValidObjectId } from "mongoose";
import { IAudio } from "../interfaces/Audio";
import { IQueryFilter } from "src/interfaces/Common";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 40,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);
router.use(compression());

router.get(
    "/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_AUDIOS),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v) => v.split(",").map((v: string) => v.trim()))
            .custom((v) => v.every((id: string) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
        query("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    ]),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts: number = new Date().getTime();
            const { vesselIds, startTimestampISO, endTimestampISO }: IQueryFilter = req.query;
            console.log(`/audio ${vesselIds}`, startTimestampISO, endTimestampISO);

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });

            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            const query: IQueryFilter = {};
            query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };

            if (startTimestampISO) {
                const endTime: string | number = endTimestampISO || Date.now();
                query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
            }
            query.host_location = { $ne: null };

            const audios = await limitPromise(async () => {
                if (isClosed) {
                    res.end();
                    return {};
                }

                console.log(`/audio querying DB`, query);

                const ts = new Date().getTime();

                const cursor = db.audio.collection<IAudio>("audio_files").find(query, {
                    projection: {
                        _id: 1,
                        frequency: 1,
                        timestamp: 1,
                        aws_region: 1,
                        bucket_name: 1,
                        host_location: 1,
                        onboard_vessel_id: 1,
                        audio_path: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                const result: IAudio[] = await cursor.toArray();

                console.log(`/audio time taken to query ${new Date().getTime() - ts}`);

                const groupedAudios = result.reduce((acc: { [vesselId: string]: IAudio[] }, audio: IAudio) => {
                    const vesselId: string = audio.onboard_vessel_id.toString();
                    if (!acc[vesselId]) {
                        acc[vesselId] = [];
                    }
                    acc[vesselId].push(audio);
                    return acc;
                }, {});

                vesselIds.forEach((vesselId: string) => {
                    if (!groupedAudios[vesselId]) {
                        groupedAudios[vesselId] = [];
                    }
                });

                return groupedAudios;
            });
            console.log(`/audio received ${Object.keys(audios).length} audio groups`);

            if (isClosed) return res.end();

            res.json(audios);

            console.log(`/audio time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

export default router;
