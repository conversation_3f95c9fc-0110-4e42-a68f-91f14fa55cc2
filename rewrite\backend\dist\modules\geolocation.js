"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.googleMapsClient = void 0;
exports.fetchGeolocation = fetchGeolocation;
const google_maps_services_js_1 = require("@googlemaps/google-maps-services-js");
const googleMapsClient = new google_maps_services_js_1.Client();
exports.googleMapsClient = googleMapsClient;
function fetchGeolocation(lat, lng) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        try {
            const { data, status } = yield googleMapsClient.reverseGeocode({
                params: {
                    latlng: `${lat},${lng}`,
                    key: process.env.GOOGLE_API_KEY,
                },
            });
            if (status === 200 && data.results.length > 0) {
                const filterAddress = ((_a = data.results.find((address) => !address.types.includes(google_maps_services_js_1.AddressType.plus_code))) === null || _a === void 0 ? void 0 : _a.formatted_address) ||
                    data.results[0].formatted_address;
                return filterAddress;
            }
            throw new Error("No valid address found.");
        }
        catch (err) {
            console.error("Google Maps Geocoding Error:", err);
            throw new Error(`Google Maps Geocoding Error: ${err.message || "Unexpected error"}`);
        }
    });
}
