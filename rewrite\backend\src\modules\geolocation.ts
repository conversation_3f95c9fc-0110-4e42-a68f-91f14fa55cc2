import { Client, AddressType } from "@googlemaps/google-maps-services-js";

const googleMapsClient = new Client();

async function fetchGeolocation(lat: number, lng: number) {
    try {
        const { data, status } = await googleMapsClient.reverseGeocode({
            params: {
                latlng: `${lat},${lng}`,
                key: process.env.GOOGLE_API_KEY as string,
            },
        });

        if (status === 200 && data.results.length > 0) {
            const filterAddress =
                data.results.find((address) => !address.types.includes(AddressType.plus_code))?.formatted_address ||
                data.results[0].formatted_address;
            return filterAddress;
        }

        throw new Error("No valid address found.");
    } catch (err: any) {
        console.error("Google Maps Geocoding Error:", err);
        throw new Error(`Google Maps Geocoding Error: ${err.message || "Unexpected error"}`);
    }
}

export { fetchGeolocation, googleMapsClient };
