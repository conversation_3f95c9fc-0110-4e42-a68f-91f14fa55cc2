import jwt, { JwtPayload } from "jsonwebtoken";
import <PERSON><PERSON><PERSON><PERSON> from "../models/ApiKey";
import { NextFunction, Request, Response } from "express";
import { getUser } from "../queries/User";

const isAuthenticated = async (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.header("authorization");
    if (!authHeader) return res.status(401).json({ message: "Unauthorized" });
    if (!authHeader.startsWith("Bearer ")) return res.status(401).json({ message: "Token type must be Bearer" });
    const token = authHeader.split("Bearer ")[1];
    if (!token) return res.status(401).json({ message: "Token is invalid" });

    try {
        const { user_id, api_key_id } = jwt.verify(token, process.env.JWT_SECRET as string) as JwtPayload;
        if (user_id) {
            const user = await getUser({ user_id, includeUnprojected: true });
            if (user.is_deleted) return res.status(401).json({ message: "Your account has been deleted." });
            if (!user.jwt_tokens.includes(token)) return res.status(401).json({ message: "Token is not available." });
            req.user = user;

            console.log(
                `[${req.method}: ${req.originalUrl}] user: ${JSON.stringify({ _id: user._id, name: user.name, email: user.email })}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `,
            );
            next();
        } else if (api_key_id) {
            const apiKey = await ApiKey.findOne({ _id: api_key_id });
            if (!apiKey) return res.status(401).json({ message: "API key is invalid" });
            if (apiKey.is_deleted) return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });
            if (apiKey.is_revoked) return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });

            if (!apiKey.allowed_endpoints.includes(req._endpoint_id)) return res.status(403).json({ message: "You cannot access this resource" });

            apiKey.requests += 1;
            apiKey.requests_endpoints[req._endpoint_id] = (apiKey.requests_endpoints[req._endpoint_id] || 0) + 1;
            apiKey.last_used = new Date();
            apiKey.markModified("requests_endpoints");

            await apiKey.save();

            req.api_key = apiKey.toObject();

            console.log(
                `[${req.method}: ${req.originalUrl}] apiKey: ${apiKey._id}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `,
            );
            next();
        } else throw new Error("JWT token returned unexpected data");
    } catch (err) {
        console.error(err);
        if (err instanceof jwt.JsonWebTokenError) res.status(401).json({ message: "Token is invalid" });
        else if (err instanceof Error) res.status(500).json({ message: `Unexpected error occured: ${err.message}` });
        else res.status(500).json({ message: `Unexpected error occured: ${err}` });
    }
};

export default isAuthenticated;
