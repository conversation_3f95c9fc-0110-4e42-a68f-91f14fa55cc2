import mongoose from "mongoose";
import db from "../modules/db";
import { IPermission } from "src/interfaces/Permission";

const permissionSchema = new mongoose.Schema({
    permission_id: { type: Number, required: true, unique: true },
    permission_name: { type: String, required: true, unique: true },
    permission_description: { type: String, required: true },
    assignable: { type: Boolean, required: true, default: true },
});

const Permission = db.qm.model<IPermission>("Permission", permissionSchema);

export default Permission;
