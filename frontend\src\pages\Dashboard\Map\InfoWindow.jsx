import React, { useEffect, useMemo, useState } from "react";
import { Grid, Typography, IconButton, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PreviewMedia from "../../../components/PreviewMedia";
import dayjs from "dayjs";
import { defaultValues, handleVesselTimezone, simplifyTimezone, permissions, displayCoordinates } from "../../../utils";
import { UserProvider } from "../../../providers/UserProvider";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import { getSocket } from "../../../socket";
import artifactController from "../../../controllers/Aritfact.controller";

const InfoWindow = ({ artifact, artifactInfowWindow, vesselInfo, user }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [flaggedArtifact, setFlaggedArtifact] = useState(false);
    const [fullArtifact, setFullArtifact] = useState(null);
    const [loading, setLoading] = useState(true);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const vesselTimezone = useMemo(() => {
        const timezone = handleVesselTimezone(fullArtifact || artifact, vesselInfo);
        return timezone;
    }, [fullArtifact, artifact, vesselInfo]);

    const handleFlagChanged = async () => {
        await artifactFlagController.getUserFlaggedArtifactIds();
        setFlaggedArtifact(artifactFlagController.isArtifactFlaggedByUser(artifact._id));
    };

    const fetchArtifactDetails = async () => {
        try {
            setLoading(true);
            const response = await artifactController.getArtifactDetail(artifact._id);
            setFullArtifact(response);
        } catch (error) {
            if (error.name === "AbortError" || error.name === "CanceledError" || error.code === "ERR_CANCELED") {
                console.log("Request was aborted for artifact:", artifact._id);
                return;
            }
            console.error("Error fetching artifact details:", error);
            // Fallback to using the minimal artifact data
            setFullArtifact(artifact);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const socket = getSocket();

        handleFlagChanged();
        fetchArtifactDetails();

        socket.on("artifacts_flagged/changed", handleFlagChanged);
        return () => socket.off("artifacts_flagged/changed", handleFlagChanged);
    }, [artifact._id]);

    useEffect(() => {
        if (fullArtifact) {
            const thumbnailUrl = fullArtifact.thumbnail_url;
            const videoUrl = fullArtifact.video_url;
            const imageUrl = fullArtifact.image_url;

            if (fullArtifact.video_path) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [fullArtifact]);

    const handleClose = () => {
        artifactInfowWindow.close();
    };

    return (
        <Grid
            container
            direction="column"
            sx={{
                padding: 2,
                backgroundColor: "#343B44",
                color: "white",
                borderRadius: 2,
                maxWidth: 330,
                gap: 2,
            }}
        >
            {/* Add custom styles for InfoWindow */}
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                `}
            </style>
            {/* Header */}
            <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{artifact.name || "Artifact"}</Typography>
                <IconButton
                    onClick={handleClose}
                    sx={{
                        color: "white",
                        border: "1px solid white",
                        "&:hover": {
                            backgroundColor: "white",
                            color: "#4F5968",
                        },
                    }}
                >
                    <CloseIcon sx={{ fontSize: "14px" }} />
                </IconButton>
            </Grid>
            <Grid
                sx={{
                    position: "relative",
                    backgroundColor: "#343B44",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: 200,
                    borderRadius: 1,
                }}
            >
                {loading || !fullArtifact ? (
                    <Skeleton
                        variant="rectangular"
                        width="100%"
                        height="100%"
                        sx={{
                            borderRadius: 1,
                            minHeight: 200,
                            minWidth: 290,
                        }}
                    />
                ) : (
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={fullArtifact._id}
                        isImage={!fullArtifact.video_path}
                        style={{ borderRadius: 8 }}
                        showFullscreenIconForMap={!fullArtifact.video_path}
                        showVideoThumbnail={fullArtifact.video_path}
                        showArchiveButton={hasManageArtifacts}
                        isArchived={fullArtifact?.portal?.is_archived}
                        vesselId={fullArtifact?.onboard_vessel_id}
                        // userTest={user}
                        skeletonStyle={{
                            minHeight: 200,
                            minWidth: 290,
                        }}
                        flaggedArtifact={flaggedArtifact}
                    />
                )}
            </Grid>
            <Grid sx={{ maxHeight: "280px", display: "flex", flexDirection: "column", overflowY: "auto" }}>
                {loading || !fullArtifact ? (
                    <>
                        <Skeleton variant="text" width="80%" height={24} />
                        <Skeleton variant="text" width="70%" height={24} />
                        <Skeleton variant="text" width="60%" height={24} />
                        <Skeleton variant="text" width="75%" height={24} />
                        <Skeleton variant="text" width="90%" height={24} />
                        <Skeleton variant="text" width="70%" height={24} />
                        <Skeleton variant="text" width="60%" height={24} />
                        <Skeleton variant="text" width="90%" height={24} />
                        <Skeleton variant="text" width="85%" height={24} />
                    </>
                ) : (
                    <>
                        <Typography>
                            <strong>Timestamp</strong>:{" "}
                            {fullArtifact.timestamp
                                ? dayjs(fullArtifact.timestamp).tz(vesselTimezone).format(defaultValues.dateTimeFormat())
                                : "Not available"}{" "}
                            {vesselTimezone && simplifyTimezone(vesselTimezone)}
                        </Typography>
                        <Typography>
                            <strong>Location:</strong>{" "}
                            {displayCoordinates([fullArtifact.location?.coordinates?.[0], fullArtifact.location?.coordinates?.[1]], !!user?.use_MGRS)}
                            <br />
                        </Typography>
                        <Typography>
                            <strong>Super Category:</strong> {fullArtifact.super_category || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Category:</strong> {fullArtifact.category || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Color:</strong> {fullArtifact.color || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Size:</strong> {fullArtifact.size || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Orientation:</strong> {fullArtifact.vessel_orientation || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Detected Country:</strong> {fullArtifact.home_country || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Bearing Angle:</strong>{" "}
                            {fullArtifact.true_bearing ? `${Number(fullArtifact.true_bearing).toFixed(2)}\u00B0` : "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Features:</strong> {fullArtifact.vessel_features || "Not available"}
                        </Typography>
                        <Typography>
                            <strong>Description:</strong> {fullArtifact.others || "Not available"}
                        </Typography>
                    </>
                )}
            </Grid>
        </Grid>
    );
};

const WrappedInfoWindow = (props) => (
    <UserProvider>
        <InfoWindow {...props} />
    </UserProvider>
);

export default WrappedInfoWindow;
