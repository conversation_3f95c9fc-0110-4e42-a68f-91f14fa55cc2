import mongoose from "mongoose";
import { ILocation } from "./VesselLocation";

export interface IAudio {
    _id: mongoose.Types.ObjectId;
    bucket_name: string;
    aws_region: string;
    host_location: ILocation;
    metadata_path: string;
    audio_path: string;
    unit_id: string;
    frequency: string;
    onboard_vessel_id: mongoose.Types.ObjectId;
    metadata: Record<string, string | number | boolean>;
    timestamp: Date;
    creation_timestamp: Date;
}
