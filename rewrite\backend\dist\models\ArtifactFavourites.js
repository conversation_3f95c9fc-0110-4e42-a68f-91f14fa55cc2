"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const User_1 = __importDefault(require("./User"));
const ioEmitter_1 = __importDefault(require("../modules/ioEmitter"));
const schema = mongoose_1.default.Schema;
const ArtifactFavouritesSchema = new schema({
    user_id: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: User_1.default,
        required: true,
    },
    artifact_id: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        required: true,
    },
}, {
    timestamps: true,
});
ArtifactFavouritesSchema.post("save", (favourite) => {
    if (!favourite)
        return;
    ioEmitter_1.default.emit("notifyAll", { name: `favourites/changed`, data: favourite.toObject() });
});
ArtifactFavouritesSchema.post("findOneAndDelete", (favourite) => {
    if (!favourite)
        return;
    ioEmitter_1.default.emit("notifyAll", { name: `favourites/changed`, data: favourite.toObject() });
});
const ArtifactFavourites = db_1.default.qm.model("ArtifactFavourites", ArtifactFavouritesSchema, "artifact_favourites");
exports.default = ArtifactFavourites;
