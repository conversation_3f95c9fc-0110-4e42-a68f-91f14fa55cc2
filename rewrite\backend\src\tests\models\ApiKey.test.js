const mockingoose = require('mockingoose');
const ApiKey = require('../../models/ApiKey');
const { apiKeysList } = require('../data/ApiKeys');
const ioEmitter = require('../../modules/ioEmitter');

// Mock the ioEmitter
jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn(),
}));

describe('ApiKey Model', () => {
    it('should create the document', async () => {
        const doc = apiKeysList[0]

        mockingoose(ApiKey).toReturn(doc, 'create');

        const result = await ApiKey.create({ description: doc.description });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with findById', async () => {
        const doc = apiKeysList[0]

        mockingoose(Api<PERSON>ey).toReturn(doc, 'findOne');

        const result = await ApiKey.findById({ _id: doc._id });

        expect(result).toBeInstanceOf(Object)
        Object.keys(doc).forEach(prop => {
            expect(result).toHaveProperty(prop)
        })
    });

    it('should return the doc with find', async () => {
        const docs = apiKeysList

        mockingoose(ApiKey).toReturn(docs, 'find');

        const result = await ApiKey.find();

        expect(result).toBeInstanceOf(Array)
        Object.keys(docs[0]).forEach(prop => {
            expect(result[0]).toHaveProperty(prop)
        })
    });

    it('should trigger post save hook and emit an event', async () => {
        const doc = apiKeysList[0]

        // Mocking the save operation
        mockingoose(ApiKey).toReturn(doc, 'save');

        const apiKey = new ApiKey(doc);
        await apiKey.save(); // This should trigger the `post('save')` hook

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });

    it('should trigger post findOneAndDelete hook and emit an event', async () => {
        const doc = apiKeysList[0]


        // Mocking the save operation
        mockingoose(ApiKey).toReturn(doc, 'findOneAndDelete');

        const deletedDoc = await ApiKey.findOneAndDelete({ _id: doc._id })

        ApiKey.emitChangedEvent(deletedDoc)

        // ApiKey.schema.post('findOneAndDelete')(deletedDoc);

        // Check if the ioEmitter was called
        expect(ioEmitter.emit).toHaveBeenCalled();
    });
});
