"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const User_1 = __importDefault(require("./User"));
const NotificationAlertsSchema = new mongoose_1.default.Schema({
    super_category: { type: [String], required: true },
    sub_category: { type: [String], required: true },
    country_flags: { type: Array, default: [] },
    type: { type: String, required: true, enum: ["email", "app", "both"] },
    title: { type: [String], required: true },
    unit_id: { type: [String], required: false }, // kept for backward compatibility
    vessel_ids: {
        type: [mongoose_1.default.Schema.Types.Mixed],
        required: true,
        validate: {
            validator: function (arr) {
                return arr.every((item) => item === "all" || mongoose_1.default.Types.ObjectId.isValid(item));
            },
            message: 'vessel_ids must contain valid ObjectIds or "all"',
        },
    },
    receivers: [{ type: String }], // will be used in v2 to send to multiple users
    created_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
    is_enabled: { type: Boolean, default: true },
    created_by: { type: mongoose_1.default.Schema.Types.ObjectId, ref: User_1.default },
});
const NotificationAlert = db_1.default.qm.model("NotificationAlert", NotificationAlertsSchema, "notifications_alerts");
exports.default = NotificationAlert;
