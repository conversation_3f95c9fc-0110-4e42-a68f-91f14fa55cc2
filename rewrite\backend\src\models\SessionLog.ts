import mongoose from "mongoose";
import db from "../modules/db";
import ioEmitter from "../modules/ioEmitter";

const sessionLogSchema = new mongoose.Schema({
    socket_id: { type: String, required: true },
    device: { type: String, required: true },
    browser: { type: String, required: true },
    user_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    disconnect_timestamp: { type: Date, default: null },
    connect_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
    environment: { type: String, required: true, default: () => process.env.NODE_ENV },
});

sessionLogSchema.post("save", (log) => {
    ioEmitter.emit("notifyAll", { name: `logs/changed`, data: log.toObject() });
});

sessionLogSchema.post("findOneAndDelete", (log) => {
    ioEmitter.emit("notifyAll", { name: `logs/changed`, data: log.toObject() });
});

// function emitChangedEvent(log) {
//     ioEmitter.emit('notifyAll', { name: `logs/changed`, data: log.toObject() });
// }

const SessionLog = db.qm.model("SessionLog", sessionLogSchema, "logs_sessions");

export default SessionLog;
// module.exports.emitChangedEvent = process.env.NODE_ENV === 'test' && emitChangedEvent
