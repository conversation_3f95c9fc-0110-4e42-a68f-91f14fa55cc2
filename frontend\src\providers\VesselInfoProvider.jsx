import { useEffect, useState } from "react";
import { VesselInfoContext } from "../contexts/VesselInfoContext";
import vesselController from "../controllers/Vessel.controller";
import { useUser } from "../hooks/UserHook";
import _ from "lodash";

export const VesselInfoProvider = ({ children }) => {
    const [vesselInfo, setVesselInfo] = useState([]);
    const { user } = useUser();

    const fetchVesselsInfo = async () => {
        try {
            const vessels = await vesselController.fetchAll({});
            if (!_.isEqual(vessels, vesselInfo)) {
                setVesselInfo(vessels);
            }
            return vessels;
        } catch (err) {
            console.error("An error occurred while fetching vessels in the VesselContext :", err);
        }
    };
    useEffect(() => {
        if (user) {
            fetchVesselsInfo();
        }
    }, [user]);
    return <VesselInfoContext.Provider value={{ vesselInfo, fetchVesselsInfo }}>{children}</VesselInfoContext.Provider>;
};
