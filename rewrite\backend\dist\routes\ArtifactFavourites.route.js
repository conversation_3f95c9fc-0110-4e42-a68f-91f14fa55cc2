"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const endpointIds_1 = require("../utils/endpointIds");
const auth_1 = __importDefault(require("../middlewares/auth"));
const validator_1 = require("../middlewares/validator");
const express_validator_1 = require("express-validator");
const mongoose_1 = __importDefault(require("mongoose"));
const ArtifactFavourites_service_1 = require("../services/ArtifactFavourites.service");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
router.use("/", apiLimiter);
router.post("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.CREATE_FAVOURITE_ARTIFACT), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("artifact_id")
        .isString()
        .withMessage("artifact_id must be a string")
        .notEmpty()
        .withMessage("artifact_id must not be an empty string")
        .custom((value) => mongoose_1.default.Types.ObjectId.isValid(value))
        .withMessage("artifact_id must be a valid object id"),
], req, res, next), ArtifactFavourites_service_1.addFavouriteArtifact);
// there is currently no usecase for this endpoint
// router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_FAVOURITE_ARTIFACTS), isAuthenticated, getAllFavouriteArtifacts);
router.get("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_USER_FAVOURITE_ARTIFACTS), auth_1.default, ArtifactFavourites_service_1.getUserFavouriteArtifacts);
router.delete("/", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.DELETE_FAVOURITE_ARTIFACT), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.body)("artifact_id")
        .isString()
        .withMessage("artifact_id must be a string")
        .notEmpty()
        .withMessage("artifact_id must not be an empty string")
        .custom((value) => mongoose_1.default.Types.ObjectId.isValid(value))
        .withMessage("artifact_id must be a valid object id"),
], req, res, next), ArtifactFavourites_service_1.deleteFavouriteArtifact);
exports.default = router;
