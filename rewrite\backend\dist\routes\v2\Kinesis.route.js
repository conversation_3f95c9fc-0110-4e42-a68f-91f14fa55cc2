"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const assignEndpointId_1 = __importDefault(require("../../middlewares/assignEndpointId"));
const auth_1 = __importDefault(require("../../middlewares/auth"));
const validator_1 = require("../../middlewares/validator");
const endpointIds_1 = require("../../utils/endpointIds");
const functions_1 = require("../../utils/functions");
const awsKinesis_1 = __importDefault(require("../../modules/awsKinesis"));
const express_validator_1 = require("express-validator");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const Vessel_service_1 = __importDefault(require("../../services/Vessel.service"));
const Stream_service_1 = __importDefault(require("../../services/Stream.service"));
const permissions_1 = require("../../utils/permissions");
const router = express_1.default.Router();
const authUserApiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});
// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    }
    else {
        apiLimiter(req, res, next);
    }
}
router.use("/", conditionalRateLimiter);
let tts = Date.now();
router.get("/dashStreamingSessionURL", (req, res, next) => {
    tts = Date.now();
    next();
}, assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_STREAM_URL_V2), auth_1.default, (req, res, next) => (0, validator_1.validateData)([
    (0, express_validator_1.query)("streamName")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("region")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("streamMode")
        .isString()
        .notEmpty()
        .toUpperCase()
        .isIn(["LIVE", "ON_DEMAND", "LIVE_REPLAY"])
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    (0, express_validator_1.query)("startTimestamp")
        .isISO8601()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
    (0, express_validator_1.query)("totalDuration")
        .isNumeric()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        .optional(),
], req, res, next), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { streamName, region, streamMode, startTimestamp, totalDuration } = req.query;
        if (!streamName ||
            typeof streamName !== "string" ||
            !region ||
            typeof region !== "string" ||
            !streamMode ||
            typeof streamMode !== "string") {
            return res.status(400).json({ message: "Missing required parameters" });
        }
        const stream = yield Stream_service_1.default.fetchSingle({ unitId: streamName });
        if (!stream)
            return res.status(404).json({ message: "Stream does not exist" });
        if (req.api_key || (req.user && !(0, functions_1.userHasPermissions)(req.user, [permissions_1.permissions.accessAllVessels]))) {
            const vessel = yield Vessel_service_1.default.findByAssignedUnitId({ unitId: stream.unit_id });
            if (!vessel)
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            if (!(0, functions_1.canAccessVessel)(req, vessel)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }
        }
        const ts = Date.now();
        const data = yield awsKinesis_1.default.getDashStreamingSessionURL_V2({
            streamName,
            region,
            streamMode,
            startTimestamp: new Date(startTimestamp).getTime(),
            totalDuration: parseInt(totalDuration, 10),
        });
        console.log("[dashStreamingSessionURL] session url time taken", Date.now() - ts, "ms");
        console.log("[dashStreamingSessionURL] total time taken", Date.now() - tts, "ms");
        res.json({ data });
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
}));
exports.default = router;
