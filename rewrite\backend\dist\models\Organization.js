"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const db_1 = __importDefault(require("../modules/db"));
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("./User"));
const organizationSchema = new mongoose_1.default.Schema({
    name: { type: String, required: true },
    domain: { type: String, required: true, unique: true },
    is_internal: { type: Boolean, required: true, default: false },
    is_miscellaneous: { type: Boolean, required: true, default: false },
    created_by: { type: mongoose_1.default.Schema.Types.ObjectId, ref: User_1.default },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});
// organizationSchema.post("save", emitChangedEvent);
// organizationSchema.post("findOneAndDelete", emitChangedEvent);
// function emitChangedEvent(organization) {
//     ioEmitter.emit("notifyAll", { name: `organization/changed`, data: organization.toObject() });
// }
const Organization = db_1.default.qm.model("Organization", organizationSchema, "organizations");
exports.default = Organization;
// module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
