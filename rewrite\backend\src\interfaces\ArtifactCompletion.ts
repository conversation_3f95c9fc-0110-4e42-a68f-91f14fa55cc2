import mongoose from "mongoose";
import { IArtifactSynonym } from "./ArtifactSuggestion";

export interface IDataCache {
    superCategories?: string[];
    categories?: string[];
    sizes?: string[];
    vessels?: string[];
    weapons?: string[];
    synonyms?: IArtifactSynonym[];
    prompt?: IToolSchema;
}

export interface IToolSchema {
    tool: {
        type: "function";
        function: {
            name: string;
            description: string;
            parameters: {
                type: "object";
                properties: {
                    country_flags: { type: "array"; items: { type: "string" } };
                    colors: { type: "array"; items: { type: "string" } };
                    categories: { type: "array"; items: { type: "string"; enum: string[] } };
                    subcategory: { type: "array"; items: { type: "string"; enum: string[] } };
                    sizes: { type: "array"; items: { type: "string"; enum: string[] } };
                    weapons: { type: "array"; items: { type: "string"; enum: string[] } };
                    vessel_name: { type: "array"; items: { type: "string"; enum: string[] } };
                };
                required: string[];
            };
        };
    };
    systemMessage: string;
}

export interface IUserCompletionLog {
    _id: mongoose.Types.ObjectId;
    user_id: mongoose.Types.ObjectId | null;
    command: string;
    response: string;
    completion_type: string;
    completed_at?: Date;
    updated_at?: Date;
}
