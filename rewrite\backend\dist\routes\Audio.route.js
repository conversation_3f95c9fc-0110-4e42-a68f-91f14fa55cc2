"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const assignEndpointId_1 = __importDefault(require("../middlewares/assignEndpointId"));
const auth_1 = __importDefault(require("../middlewares/auth"));
const validator_1 = require("../middlewares/validator");
const functions_1 = require("../utils/functions");
const endpointIds_1 = require("../utils/endpointIds");
const pLimit_1 = __importDefault(require("../modules/pLimit"));
const db_1 = __importDefault(require("../modules/db"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const compression_1 = __importDefault(require("compression"));
const mongoose_1 = require("mongoose");
const router = express_1.default.Router();
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 5 * 1000,
    limit: 40,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});
router.use("/", apiLimiter);
router.use((0, compression_1.default)());
router.get("/bulk", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_AUDIOS), auth_1.default, validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("vesselIds")
        .isString()
        .withMessage(`vesselIds is a required string`)
        .notEmpty()
        .withMessage(`vesselIds must be a comma-separated string`)
        .if((0, express_validator_1.query)("vesselIds").exists())
        .customSanitizer((v) => v.split(",").map((v) => v.trim()))
        .custom((v) => v.every((id) => (0, mongoose_1.isValidObjectId)(id)))
        .withMessage(`vesselIds must be valid object IDs`),
    (0, express_validator_1.query)("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    (0, express_validator_1.query)("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const requestURL = req.get("Referer");
    const isSwagger = requestURL ? requestURL.includes("/docs") : false;
    let isClosed = false;
    const onClose = () => {
        isClosed = true;
    };
    res.on("close", onClose);
    try {
        const ts = new Date().getTime();
        const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
        console.log(`/audio ${vesselIds}`, startTimestampISO, endTimestampISO);
        if (endTimestampISO && !startTimestampISO) {
            return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
        }
        const vessels = yield Vessel_service_1.default.find({ _id: { $in: vesselIds } });
        const assignedVessels = vessels.filter((vessel) => (0, functions_1.canAccessVessel)(req, vessel));
        const query = {};
        query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };
        if (startTimestampISO) {
            const endTime = endTimestampISO || Date.now();
            query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
        }
        query.host_location = { $ne: null };
        const audios = yield (0, pLimit_1.default)(() => __awaiter(void 0, void 0, void 0, function* () {
            if (isClosed) {
                res.end();
                return {};
            }
            console.log(`/audio querying DB`, query);
            const ts = new Date().getTime();
            const cursor = db_1.default.audio.collection("audio_files").find(query, {
                projection: {
                    _id: 1,
                    frequency: 1,
                    timestamp: 1,
                    aws_region: 1,
                    bucket_name: 1,
                    host_location: 1,
                    onboard_vessel_id: 1,
                    audio_path: 1,
                },
            });
            if (isSwagger) {
                cursor.limit(20);
            }
            const result = yield cursor.toArray();
            console.log(`/audio time taken to query ${new Date().getTime() - ts}`);
            const groupedAudios = result.reduce((acc, audio) => {
                const vesselId = audio.onboard_vessel_id.toString();
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push(audio);
                return acc;
            }, {});
            vesselIds.forEach((vesselId) => {
                if (!groupedAudios[vesselId]) {
                    groupedAudios[vesselId] = [];
                }
            });
            return groupedAudios;
        }));
        console.log(`/audio received ${Object.keys(audios).length} audio groups`);
        if (isClosed)
            return res.end();
        res.json(audios);
        console.log(`/audio time taken to respond ${new Date().getTime() - ts}`);
    }
    catch (err) {
        (0, functions_1.validateError)(err, res);
    }
    finally {
        res.removeListener("close", onClose);
    }
}));
exports.default = router;
