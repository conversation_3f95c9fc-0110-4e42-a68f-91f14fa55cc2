"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
mongoose_1.default.set("strictQuery", false);
if (!process.env.MONGO_URI) {
    throw new Error("MONGO_URI must be set in env variables");
}
const db = {
    qm: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "quartermaster" }),
    qmai: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "artifact_processor" }),
    qmShared: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "quartermaster-shared" }),
    locations: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "locations" }),
    locationsOptimized: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "locations_optimized" }),
    locationsRaw: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "locations_raw" }),
    aisRaw: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "ais_raw" }),
    audio: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "audio_processor" }),
    lookups: mongoose_1.default.createConnection(process.env.MONGO_URI, { dbName: "lookups" }),
};
db.qm.on("open", () => console.log("DB connected to Quartermaster"));
db.qmai.on("open", () => console.log("DB connected to QMAI"));
db.qmShared.on("open", () => console.log("DB connected to Quartermaster-Shared"));
db.locations.on("open", () => console.log("DB connected to Locations"));
db.locationsOptimized.on("open", () => console.log("DB connected to Locations Optimized"));
db.locationsRaw.on("open", () => console.log("DB connected to Locations Raw"));
db.aisRaw.on("open", () => console.log("DB connected to AIS Raw"));
db.audio.on("open", () => console.log("DB connected to QMAudio"));
db.lookups.on("open", () => console.log("DB connected to Lookups"));
db.qm.on("error", (err) => console.error(err));
db.qmai.on("error", (err) => console.error(err));
db.qmShared.on("error", (err) => console.error(err));
db.locations.on("error", (err) => console.error(err));
db.locationsOptimized.on("error", (err) => console.error(err));
db.locationsRaw.on("error", (err) => console.error(err));
db.aisRaw.on("error", (err) => console.error(err));
db.audio.on("error", (err) => console.error(err));
db.lookups.on("error", (err) => console.error(err));
exports.default = db;
