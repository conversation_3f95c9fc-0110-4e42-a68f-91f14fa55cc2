"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const User_route_1 = __importDefault(require("./User.route"));
const Kinesis_route_1 = __importDefault(require("./Kinesis.route"));
const Region_route_1 = __importDefault(require("./Region.route"));
const VesselLocation_route_1 = __importDefault(require("./VesselLocation.route"));
const Role_route_1 = __importDefault(require("./Role.route"));
const Permission_route_1 = __importDefault(require("./Permission.route"));
const Artifact_route_1 = __importDefault(require("./Artifact.route"));
const ArtifactFavourites_route_1 = __importDefault(require("./ArtifactFavourites.route"));
const ArtifactSuggestions_route_1 = __importDefault(require("./ArtifactSuggestions.route"));
const Log_route_1 = __importDefault(require("./Log.route"));
const S3_route_1 = __importDefault(require("./S3.route"));
const ApiKey_route_1 = __importDefault(require("./ApiKey.route"));
const ApiEndpoint_route_1 = __importDefault(require("./ApiEndpoint.route"));
const Statistics_route_1 = __importDefault(require("./Statistics.route"));
const Vessels_route_1 = __importDefault(require("./Vessels.route"));
const Geolocation_route_1 = __importDefault(require("./Geolocation.route"));
const RegionGroup_route_1 = __importDefault(require("./RegionGroup.route"));
const TourGuide_route_1 = __importDefault(require("./TourGuide.route"));
const NotificationAlert_route_1 = __importDefault(require("./NotificationAlert.route"));
const InAppNotification_route_1 = __importDefault(require("./InAppNotification.route"));
const NotificationSummary_route_1 = __importDefault(require("./NotificationSummary.route"));
const ArtifactCompletions_route_1 = __importDefault(require("./ArtifactCompletions.route"));
const EmailDomains_route_1 = __importDefault(require("./EmailDomains.route"));
const HomePorts_route_1 = __importDefault(require("./HomePorts.route"));
const Organization_route_1 = __importDefault(require("./Organization.route"));
const VesselManagement_route_1 = __importDefault(require("./VesselManagement.route"));
const Thingsboard_route_1 = __importDefault(require("./Thingsboard.route"));
const VesselAis_route_1 = __importDefault(require("./VesselAis.route"));
const Audio_route_1 = __importDefault(require("./Audio.route"));
const router = express_1.default.Router();
router.use("/users", User_route_1.default);
router.use("/kinesis", Kinesis_route_1.default);
router.use("/regions", Region_route_1.default);
router.use("/vesselLocations", VesselLocation_route_1.default);
router.use("/roles", Role_route_1.default);
router.use("/permissions", Permission_route_1.default);
router.use("/artifacts", Artifact_route_1.default);
router.use("/artifactFavourites", ArtifactFavourites_route_1.default);
router.use("/suggestions", ArtifactSuggestions_route_1.default);
router.use("/logs", Log_route_1.default);
router.use("/s3", S3_route_1.default);
router.use("/apiKeys", ApiKey_route_1.default);
router.use("/apiEndpoints", ApiEndpoint_route_1.default);
router.use("/statistics", Statistics_route_1.default);
router.use("/vessels", Vessels_route_1.default);
router.use("/geolocations", Geolocation_route_1.default);
router.use("/regionGroups", RegionGroup_route_1.default);
router.use("/tourGuides", TourGuide_route_1.default);
router.use("/notificationsAlerts", NotificationAlert_route_1.default);
router.use("/inAppNotifications", InAppNotification_route_1.default);
router.use("/summaryReports", NotificationSummary_route_1.default);
router.use("/completions", ArtifactCompletions_route_1.default);
router.use("/emailsDomain", EmailDomains_route_1.default);
router.use("/organizations", Organization_route_1.default);
router.use("/homePorts", HomePorts_route_1.default);
router.use("/vesselManagement", VesselManagement_route_1.default);
router.use("/thingsboard", Thingsboard_route_1.default);
router.use("/vesselAis", VesselAis_route_1.default);
router.use("/audios", Audio_route_1.default);
exports.default = router;
