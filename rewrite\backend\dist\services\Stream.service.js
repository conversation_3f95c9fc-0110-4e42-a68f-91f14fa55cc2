"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Region_1 = __importDefault(require("../models/Region"));
const Vessel_service_1 = __importDefault(require("../services/Vessel.service"));
const awsKinesis_1 = __importDefault(require("../modules/awsKinesis"));
const p_limit_1 = __importDefault(require("p-limit"));
const RegionGroup_1 = __importDefault(require("../models/RegionGroup"));
const functions_1 = require("../utils/functions");
const limit = (0, p_limit_1.default)(1);
class StreamService {
    constructor() {
        this.streamsInfo = { data: [], lastCheck: new Date(0) };
        setInterval(() => {
            (0, functions_1.consoleLogObjectSize)(this.streamsInfo.data, "streamService.streamsInfo");
        }, 60000); // 1 minutes
    }
    resetCache() {
        return __awaiter(this, void 0, void 0, function* () {
            this.streamsInfo = { data: [], lastCheck: new Date(0) };
        });
    }
    fetchAll() {
        return __awaiter(this, arguments, void 0, function* ({ regions } = {}) {
            const lastCheck = this.streamsInfo.lastCheck;
            if (Date.now() - lastCheck.getTime() < 300000) {
                // return existing data if last check was less than 5 minutes ago
                console.log("returning existing");
                return this.streamsInfo.data.filter((stream) => (regions === undefined ? true : regions.includes(stream.region)));
            }
            console.log("[StreamService.fetchAll] Fetching updated list");
            const awsRegions = (yield Region_1.default.find()).filter((r) => r.is_live);
            const allStreams = yield Promise.all(awsRegions.map((region) => __awaiter(this, void 0, void 0, function* () {
                try {
                    return yield awsKinesis_1.default.listStreams({ region: region.value });
                }
                catch (_a) {
                    console.error(`[StreamService.fetchAll] [FATAL] Error fetching streams for region ${region.value}`);
                    return [];
                }
            })));
            const flattenedStreams = allStreams.flat();
            const [regionGroups, vessels] = yield Promise.all([
                RegionGroup_1.default.find(),
                Vessel_service_1.default.find({ unit_id: { $ne: null } }, { _id: 1, unit_id: 1, name: 1, thumbnail_compressed_s3_key: 1, region_group_id: 1 }),
            ]);
            const unitToVesselMap = new Map();
            vessels.forEach((vessel) => {
                if (vessel.unit_id) {
                    unitToVesselMap.set(vessel.unit_id, vessel);
                }
            });
            const streamsInfo = flattenedStreams.map((stream) => {
                const vessel = unitToVesselMap.get(stream.StreamName);
                // const regionGroup = vessel ? regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString())) : null;
                const regionGroup = vessel ? regionGroups.find((rg) => { var _a; return rg._id.toString() === ((_a = vessel.region_group_id) === null || _a === void 0 ? void 0 : _a.toString()); }) : null;
                return {
                    unit_id: stream.StreamName,
                    name: (vessel === null || vessel === void 0 ? void 0 : vessel.name) || null,
                    thumbnail: (vessel === null || vessel === void 0 ? void 0 : vessel.thumbnail_compressed_s3_key) || null,
                    region: stream.Region || "",
                    is_live: stream.IsLive || false,
                    timezone: (regionGroup === null || regionGroup === void 0 ? void 0 : regionGroup.timezone) || null,
                    // region_group_id: regionGroup?._id || null,
                    region_group_id: (vessel === null || vessel === void 0 ? void 0 : vessel.region_group_id) || null,
                };
            });
            this.streamsInfo = { data: streamsInfo, lastCheck: new Date() };
            return streamsInfo.filter((stream) => (regions === undefined ? true : regions.includes(stream.region)));
        });
    }
    fetchSingle(_a) {
        return __awaiter(this, arguments, void 0, function* ({ unitId }) {
            let streamInfo = this.streamsInfo.data.find((s) => s.unit_id === unitId);
            if (!streamInfo) {
                const streams = yield limit(() => this.fetchAll());
                streamInfo = streams.find((s) => s.unit_id === unitId);
                if (!streamInfo) {
                    console.error("[StreamService.fetchSingle] couldn't find stream", unitId);
                }
            }
            if (!streamInfo)
                return null;
            return streamInfo;
        });
    }
}
const streamService = new StreamService();
exports.default = streamService;
