const apiKeysList = [
    {
        "_id": "66f2c43345fbceb6fc036b34",
        "description": "This is a test key",
        "allowed_endpoints": [
            101,
            102,
            103,
            104,
            105,
            106,
            107,
            108,
            201,
            202,
            301,
            401,
            501,
            601,
            602,
            603,
            604,
            701,
            801,
            802,
            901
        ],
        "is_deleted": false,
        "is_revoked": false,
        "api_key": "d21e1a57f2de39b3f4fbd42cf871d9bc",
        "__v": 18,
        "requests": 140,
        "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5X2lkIjoiNjZmMmM0MzM0NWZiY2ViNmZjMDM2YjM0IiwiaWF0IjoxNzI3Mzc3MDQ1LCJleHAiOjE3Mjc0NjM0NDV9.4NwPQ7O6kyFSrp0fiC__wtkDUyqO5wnd38l2VCsR0eE",
        "creation_timestamp": "2024-09-24T13:52:51.178Z"
    },
    {
        "_id": "66f2c6e945fbceb6fc036b90",
        "description": "Test key 2",
        "allowed_endpoints": [],
        "is_deleted": false,
        "is_revoked": false,
        "api_key": "079fc9e755a8245654c1c768787ee24c",
        "__v": 15,
        "requests": 0,
        "jwt_token": null,
        "creation_timestamp": "2024-09-24T14:04:25.153Z"
    },
    {
        "_id": "66f590738f6aea89dc6a2f07",
        "description": "hello test key",
        "allowed_endpoints": [],
        "is_deleted": false,
        "is_revoked": false,
        "requests": 0,
        "api_key": "3f77920bef3481f08c7295a429cbeabd",
        "creation_timestamp": "2024-09-26T16:48:51.075Z",
        "__v": 0
    }
]

module.exports = {
    apiKeysList
}