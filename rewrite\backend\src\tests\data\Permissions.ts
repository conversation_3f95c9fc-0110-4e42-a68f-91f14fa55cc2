const permissionsList = [
    {
        "_id": "66d6fd7de480fbb5a5e1abc6",
        "permission_id": 100,
        "permission_name": "MANAGE_ROLES",
        "permission_description": "User can add, remove, and update roles",
        "assignable": true
    },
    {
        "_id": "66d70185e480fbb5a5e1abc9",
        "permission_id": 200,
        "permission_name": "MANAGE_USERS",
        "permission_description": "User can update roles for the users",
        "assignable": true
    },
    {
        "_id": "66d701b0e480fbb5a5e1abcb",
        "permission_id": 300,
        "permission_name": "CHANGE_REGION",
        "permission_description": "User can switch region on the dashboard",
        "assignable": true
    },
    {
        "_id": "66e0546871fd26bbd48668a2",
        "permission_id": 400,
        "permission_name": "VIEW_SESSION_LOGS",
        "permission_description": "User view session logs on the dashboard",
        "assignable": true
    },
    {
        "_id": "66f2ab205fc1d8f82af7c720",
        "permission_id": 500,
        "permission_name": "MANAGE_API_KEYS",
        "permission_description": "User can manage API developer keys on the dashboard",
        "assignable": false
    }
]

export { permissionsList }