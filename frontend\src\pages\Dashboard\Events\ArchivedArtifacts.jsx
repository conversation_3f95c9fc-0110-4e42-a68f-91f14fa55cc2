import { Grid } from "@mui/material";
import { useEffect, useState, memo, useCallback, useRef } from "react";
import DetailModal from "./DetailModal";
import theme from "../../../theme";
import VirtualizedCardList from "./VirtualizedCardList";
import axiosInstance from "../../../axios";
import { helperIconModes } from "../../../utils";
import { getSocket } from "../../../socket";

const ArchivedArtifacts = ({ vessels }) => {
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const hasMore = useRef(true);
    const [page, setPage] = useState(1);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();
    const loadMoreTimeoutRef = useRef();
    const listRef = useRef();

    const fetchArtifacts = async (isSocketTriggered = false, isLoadMore = false) => {
        if (!isSocketTriggered) {
            setIsLoading(true);
        }

        try {
            const currentPage = isLoadMore ? page + 1 : 1;
            const payload = {
                page: currentPage,
                pageSize: 100,
            };

            const response = await axiosInstance.get("/artifacts/archived", { params: payload });
            const { artifacts, totalCount } = response.data;

            if (isLoadMore) {
                setEvents((prev) => [...prev, ...artifacts]);
                setPage(currentPage);
            } else {
                setEvents(artifacts);
                setPage(1);
            }

            hasMore.current = artifacts.length < totalCount;
            setIsLoading(false);
        } catch (err) {
            console.error("Error fetching archived artifacts", err);
            setIsLoading(false);
        }
    };

    const handleLoadMore = useCallback(() => {
        if (!isLoading && hasMore.current) {
            // Clear any existing timeout
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }

            // Set a new timeout
            loadMoreTimeoutRef.current = setTimeout(() => {
                fetchArtifacts(false, true);
            }, 500);
        }
    }, [isLoading, hasMore.current, fetchArtifacts]);

    useEffect(() => {
        const unidIds = vessels.map((v) => v.unit_id);
        setFilteredEvents(events.filter((e) => unidIds.includes(e.unit_id)));
    }, [events, vessels]);

    useEffect(() => {
        fetchArtifacts();
    }, []);

    useEffect(() => {
        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;

            setEvents((prev) => {
                if (updatedArtifact?.portal?.is_archived) {
                    // Add to archived list if not already present
                    const exists = prev.find((a) => a._id === updatedArtifact._id);
                    if (!exists) {
                        return [updatedArtifact, ...prev].sort((a, b) => new Date(b.portal.archived_at) - new Date(a.portal.archived_at));
                    }
                } else {
                    // Remove from archived list if unarchived
                    return prev.filter((a) => a._id !== updatedArtifact._id);
                }
                return prev;
            });
        };

        socket.on("artifact/changed", handleArtifactChanged);

        return () => {
            socket.off("artifact/changed", handleArtifactChanged);
            // Clear timeout on unmount
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, []);

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                <Grid container height={"100%"} overflow={"hidden"} ref={virtualizedListContainerRef}>
                    <VirtualizedCardList
                        ref={listRef}
                        events={filteredEvents}
                        setShowDetailModal={setShowDetailModal}
                        setSelectedCard={setSelectedCard}
                        isLoading={isLoading}
                        onLoadMore={handleLoadMore}
                        hasMore={hasMore.current} // Disable load more in single view
                        containerRef={virtualizedListContainerRef}
                        buttonsToShow={[helperIconModes.ARCHIVE]}
                    />
                </Grid>
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
            />
        </Grid>
    );
};

export default memo(ArchivedArtifacts);
