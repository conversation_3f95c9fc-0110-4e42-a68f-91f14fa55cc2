"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const assignEndpointId_1 = __importDefault(require("../../middlewares/assignEndpointId"));
const endpointIds_1 = require("../../utils/endpointIds");
const validator_1 = require("../../middlewares/validator");
const express_validator_1 = require("express-validator");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const node_stream_1 = require("node:stream");
const staticMap_1 = require("../../utils/staticMap");
const router = express_1.default.Router();
router.get("/map.:ext?", assignEndpointId_1.default.bind(this, endpointIds_1.endpointIds.FETCH_MAP_FOR_SUMMARIES_V2), validator_1.validateData.bind(this, [
    (0, express_validator_1.query)("token")
        .isString()
        .notEmpty()
        .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
]), (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // decrypt jwt_token to get locations
    const { token } = req.query;
    const { markers } = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
    try {
        if (!markers) {
            return res.status(500).send({ message: "Error processing request" });
        }
        const { ext } = req.params;
        const mapData = yield (0, staticMap_1.getStaticMap)(markers, ext);
        if (mapData && "mimeType" in mapData && "source" in mapData) {
            res.setHeader("Content-Type", mapData.mimeType);
            res.setHeader("Content-Length", mapData.source.headers.get("content-length"));
            res.setHeader("access-control-allow-origin", "*");
            res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            res.setHeader("Expires", "0");
            res.setHeader("Pragma", "no-cache");
            if (mapData.source) {
                const nodeStream = node_stream_1.Readable.fromWeb(mapData.source.body);
                nodeStream.pipe(res);
            }
            else {
                return res.status(500).send({ message: "Error serving image" });
            }
        }
        else {
            res.status(500).send({ message: "Error serving image" });
        }
    }
    catch (error) {
        console.error("Error serving image:", error);
        res.status(500).send({ message: "Error serving image" });
    }
}));
exports.default = router;
