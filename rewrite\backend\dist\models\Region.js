"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const regionSchema = new mongoose_1.default.Schema({
    name: { type: String, required: true },
    value: { type: String, required: true, unique: true },
    is_live: { type: Boolean, required: true },
    timezone: { type: String, required: true },
});
const Region = db_1.default.qm.model("Region", regionSchema);
module.exports = Region;
exports.default = Region;
