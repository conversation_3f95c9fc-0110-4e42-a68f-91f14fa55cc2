"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const db_1 = __importDefault(require("../modules/db"));
const ArtifactSuggestionSchema = new mongoose_1.default.Schema({
    search: { type: String, required: true },
    click: { type: Number, default: 0 },
    impressions: { type: Number, default: 0 },
});
ArtifactSuggestionSchema.index({ search: 1 }, { unique: true });
const ArtifactSuggestion = db_1.default.qm.model("ArtifactSuggestion", ArtifactSuggestionSchema, "artifact_suggestions");
exports.default = ArtifactSuggestion;
