const jwt = require('jsonwebtoken');
const ApiKey = require('../../models/ApiKey');
const isAuthenticated = require('../../middlewares/auth');
const { getUser } = require('../../queries/User');

jest.mock('../../queries/User');
jest.mock('jsonwebtoken');
jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('isAuthenticated middleware', () => {
    let req, res, next;

    beforeEach(() => {
        jest.resetAllMocks();
        req = {
            header: jest.fn(),
            _endpoint_id: 'some-endpoint',
        };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
        };
        next = jest.fn();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    test('should return 401 if authorization header is missing', async () => {
        req.header.mockReturnValue(null);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Unauthorized' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if token type is not Bearer', async () => {
        req.header.mockReturnValue('Token some-token');

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token type must be Bearer' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if token is missing', async () => {
        req.header.mockReturnValue('Bearer ');

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if JWT token is invalid', async () => {
        req.header.mockReturnValue('Bearer invalid-token');
        jwt.verify.mockImplementation(() => {
            throw new jwt.JsonWebTokenError('Invalid token');
        });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should authenticate user if JWT token is valid and contains user_id', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = { id: 'user1', name: 'Test User', is_deleted: false, jwt_tokens: ['valid-token'] };
        jwt.verify.mockReturnValue({ user_id: 'user1' });
        getUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(getUser).toHaveBeenCalledWith({ user_id: 'user1' });
        expect(req.user).toEqual(mockUser);
        expect(next).toHaveBeenCalled();
    });

    test('should return 401 if user account is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = { id: 'user1', name: 'Test User', is_deleted: true };
        jwt.verify.mockReturnValue({ user_id: 'user1' });
        getUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Your account has been deleted.' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 401 if token is not available in user tokens', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockUser = { id: 'user1', name: 'Test User', is_deleted: false, jwt_tokens: [] };
        jwt.verify.mockReturnValue({ user_id: 'user1' });
        getUser.mockResolvedValue(mockUser);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'Token is not available.' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should authenticate API key if valid and allowed for the endpoint', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apiKey1',
            allowed_endpoints: ['some-endpoint'],
            is_deleted: false,
            is_revoked: false,
            requests: 0,
            save: jest.fn().mockResolvedValue(),
        };
        jwt.verify.mockReturnValue({ api_key_id: 'apiKey1' });
        ApiKey.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(ApiKey.findOne).toHaveBeenCalledWith({ _id: 'apiKey1' });
        expect(mockApiKey.requests).toBe(1);
        expect(mockApiKey.save).toHaveBeenCalled();
        expect(req.api_key_id).toBe('apiKey1');
        expect(next).toHaveBeenCalled();
    });

    test('should return 401 if API key is invalid', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        jwt.verify.mockReturnValue({ api_key_id: 'invalidKey' });
        ApiKey.findOne.mockResolvedValue(null);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(401);
        expect(res.json).toHaveBeenCalledWith({ message: 'API key is invalid' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 403 if API key does not allow access to the endpoint', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = {
            _id: 'apiKey1',
            allowed_endpoints: ['other-endpoint'],
            is_deleted: false,
            is_revoked: false,
        };
        jwt.verify.mockReturnValue({ api_key_id: 'apiKey1' });
        ApiKey.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(403);
        expect(res.json).toHaveBeenCalledWith({ message: 'You cannot access this resource' });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 500 for unexpected JWT response', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        jwt.verify.mockReturnValue({ unexpectedKey: 'value' });

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Unexpected error occured: JWT token returned unexpected data',
        });
    });

    test('should return 400 if API key is deleted', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = { is_deleted: true, is_revoked: false };
        jwt.verify.mockReturnValue({ api_key_id: 'apiKey1' });
        ApiKey.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your API key has been removed. Please contact an administrator',
        });
        expect(next).not.toHaveBeenCalled();
    });

    test('should return 400 if API key is revoked', async () => {
        req.header.mockReturnValue('Bearer valid-token');
        const mockApiKey = { is_deleted: false, is_revoked: true };
        jwt.verify.mockReturnValue({ api_key_id: 'apiKey1' });
        ApiKey.findOne.mockResolvedValue(mockApiKey);

        await isAuthenticated(req, res, next);

        expect(res.status).toHaveBeenCalledWith(400);
        expect(res.json).toHaveBeenCalledWith({
            message: 'Your access has been revoked. Please contact an administrator',
        });
        expect(next).not.toHaveBeenCalled();
    });

});
