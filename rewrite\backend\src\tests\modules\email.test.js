const nodemailer = require('nodemailer');
const { sendEmail } = require('../../modules/email'); // Update with the correct path

jest.mock('nodemailer');
jest.mock('googleapis', () => {
    const mockGetAccessToken = jest.fn().mockResolvedValue({ token: 'mockAccessToken' });
    return {
        google: {
            auth: {
                OAuth2: jest.fn().mockImplementation(() => ({
                    setCredentials: jest.fn(),
                    getAccessToken: mockGetAccessToken,
                })),
            },
        },
    };
});

describe('sendEmail module', () => {
    beforeEach(() => {
        jest.clearAllMocks(); // Clear any previous mocks
    });

    it('should send an email successfully', async () => {
        nodemailer.createTransport.mockReturnValue({
            sendMail: jest.fn().mockResolvedValue(true),
        });

        const result = await sendEmail({ to: '<EMAIL>', subject: 'Test', text: 'Test email' });
        expect(result).toBe('Email sent');
        expect(nodemailer.createTransport).toHaveBeenCalled();
        expect(nodemailer.createTransport().sendMail).toHaveBeenCalledWith({
            to: '<EMAIL>',
            subject: 'Test',
            text: 'Test email',
        });
    });

    it('should log an error when sending the email fails', async () => {
        const mockError = new Error('Failed to send email');
        nodemailer.createTransport.mockReturnValue({
            sendMail: jest.fn().mockRejectedValue(mockError),
        });

        await expect(sendEmail({ to: '<EMAIL>', subject: 'Test', text: 'Test email' })).rejects.toThrow(mockError);
    });
});
