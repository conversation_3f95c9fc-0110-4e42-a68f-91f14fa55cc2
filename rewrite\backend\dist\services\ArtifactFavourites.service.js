"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addFavouriteArtifact = addFavouriteArtifact;
exports.deleteFavouriteArtifact = deleteFavouriteArtifact;
exports.getUserFavouriteArtifacts = getUserFavouriteArtifacts;
const mongoose_1 = __importDefault(require("mongoose"));
const ArtifactFavourites_1 = __importDefault(require("../models/ArtifactFavourites"));
const db_1 = __importDefault(require("../modules/db"));
const functions_1 = require("../utils/functions");
const permissions_1 = require("../utils/permissions");
const Vessel_1 = __importDefault(require("../models/Vessel"));
const awsS3_1 = require("../modules/awsS3");
const isVesselProvisioned = (_a, vessel_id_1) => __awaiter(void 0, [_a, vessel_id_1], void 0, function* ({ user, api_key }, vessel_id) {
    if (!user)
        throw new Error("User is required");
    if (!user.permissions)
        throw new Error("User permissions are required");
    if (!vessel_id)
        throw new Error("Vessel ID is required");
    if (user.permissions.find((p) => p.permission_id === permissions_1.permissions.accessAllVessels)) {
        return true;
    }
    else {
        const vessel = (yield Vessel_1.default.findOne({ _id: new mongoose_1.default.Types.ObjectId(vessel_id) }, { _id: 1, is_active: 1, region_group_id: 1 }));
        console.log("vessel", vessel);
        if (!vessel) {
            console.warn(`[ArtifactFavourites.service.isVesselProvisioned] Vessel ${vessel_id} not found`);
            return false;
        }
        return (0, functions_1.canAccessVessel)({ user, api_key }, vessel);
    }
});
function addFavouriteArtifact(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { _id: user_id } = req.user;
            const { artifact_id } = req.body;
            if (!user_id || !artifact_id) {
                return res.status(400).send({ message: "user_id and artifact_id are required" });
            }
            const artifact = (yield db_1.default.qmai
                .collection("analysis_results")
                .findOne({ _id: new mongoose_1.default.Types.ObjectId(artifact_id) }, { projection: { _id: 1, onboard_vessel_id: 1 } }));
            if (!artifact) {
                return res.status(404).send({ message: "Artifact not found" });
            }
            if (!(yield isVesselProvisioned(req, artifact.onboard_vessel_id.toString()))) {
                return res.status(403).send({ message: "Vessel is not assigned to you" });
            }
            const isAlreadyExists = yield ArtifactFavourites_1.default.findOne({ user_id, artifact_id });
            if (isAlreadyExists) {
                return res.status(400).send({ message: "Artifact already in favourites" });
            }
            const newFavourite = new ArtifactFavourites_1.default({ user_id, artifact_id });
            yield newFavourite.save();
            res.status(201).send({ message: "Artifact added to favourites" });
        }
        catch (error) {
            (0, functions_1.validateError)(error, res);
        }
    });
}
// there is currently no usecase for this
// async function getAllFavouriteArtifacts(req, res) {
//     try {
//         const favourites = await ArtifactFavourite.find({});
//         res.status(200).send(favourites);
//     } catch (error) {
//         validateError(error, res);
//     }
// }
function deleteFavouriteArtifact(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { _id: user_id } = req.user;
            const { artifact_id } = req.body;
            if (!user_id || !artifact_id) {
                return res.status(400).send({ message: "user_id and artifact_id are required" });
            }
            const result = yield ArtifactFavourites_1.default.findOneAndDelete({ user_id, artifact_id });
            if (!result) {
                return res.status(404).send({ message: "Artifact not found in favourites" });
            }
            res.status(200).send({ message: "Artifact removed from favourites" });
        }
        catch (error) {
            (0, functions_1.validateError)(error, res);
        }
    });
}
function getUserFavouriteArtifacts(req, res) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { _id: user_id } = req.user;
            if (!user_id) {
                return res.status(400).send({ message: "user_id is required" });
            }
            const allFavorites = yield ArtifactFavourites_1.default.find({ user_id }).select("artifact_id createdAt").sort({ createdAt: -1 }); // Sort by favorite creation time
            const artifactIds = allFavorites.map((fav) => fav.artifact_id);
            const query = {
                _id: { $in: artifactIds },
                "portal.is_archived": { $ne: true },
                vessel_presence: true,
                super_category: { $ne: null },
            };
            if (!req.user.permissions.find((p) => p.permission_id === permissions_1.permissions.accessAllVessels)) {
                const activeVessels = yield Vessel_1.default.find({ _id: { $in: req.user.allowed_vessels }, is_active: true }, { _id: 1 });
                query.onboard_vessel_id = { $in: activeVessels.map((v) => v._id) };
            }
            const artifacts = (yield db_1.default.qmai
                .collection("analysis_results")
                .find(query, {
                projection: {
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    thumbnail_image_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                    portal: 1,
                },
            })
                .toArray());
            // ensure favorite artifacts only include the ones from the database and maintain favorite order
            const validFavorites = allFavorites.filter((fav) => artifacts.some((art) => art._id.toString() === fav.artifact_id.toString()));
            // Sort artifacts in the same order as favorites
            const orderedArtifacts = validFavorites
                .map((fav) => artifacts.find((art) => art._id.toString() === fav.artifact_id.toString()))
                .filter((art) => art !== undefined)
                .map((artifact) => {
                const withUrls = Object.assign({}, artifact);
                if (artifact.image_path) {
                    withUrls.image_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.video_path) {
                    withUrls.video_url = (0, awsS3_1.processBatchItem)({
                        bucketName: artifact.bucket_name,
                        key: artifact.video_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact.thumbnail_image_path) {
                    withUrls.thumbnail_url = (0, awsS3_1.processBatchItem)({
                        bucketName: awsS3_1.s3Config.buckets.compressedItems.name,
                        key: artifact.thumbnail_image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                return withUrls;
            });
            res.status(200).send({ favourites: validFavorites, artifacts: orderedArtifacts });
        }
        catch (error) {
            (0, functions_1.validateError)(error, res);
        }
    });
}
